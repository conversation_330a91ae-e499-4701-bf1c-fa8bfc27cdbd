import re
import pytz
from datetime import datetime
import time
import os

from biz.constants.constants import *
from biz.obj.worker import Worker
from biz.task.__base import TaskBase
from utils.utils import *
from utils.file import *
from utils import *


class TaskListenWorld(TaskBase):
    TASK_NAME = "监听世界"
    IS_VALUE = True
    VALUE_ID = 1

    @classmethod
    def run(cls, wk: Worker):
        color = COLOR_GREEN + "|" + COLOR_QI_LING2
        temp_list, info_list = [], []
        last_ts = settings.cur_time_stamp
        wk.record("正在监听世界消息...")
        while True:
            listen_list = wk.cfg_plan_task["监听关键字列表"]
            infos = '|'.join(listen_list)
            time.sleep(1)
            # 如果离上次持久化超过3秒
            if info_list and settings.cur_time_stamp - last_ts > 3:
                wk.record('正在持久化监听日志...')
                content = '\n'.join(info_list) + '\n'
                file_append_content(FILE_LISTEN_LOG, content)
                if temp_list:  # 保留最后一个元素
                    temp_list = [temp_list[-1]]
                info_list.clear()
                last_ts = settings.cur_time_stamp
            x, y = wk.get_str_pos(*RECT_SYSTEM_BROADCAST, infos, color)
            if x <= 0:
                continue
            msg = wk.ocr(250, y-2, 710, y+15, color=color, zk=ZK_NAME_11).rstrip(" 一_灬")
            # 去重
            if msg == "" or msg in temp_list or all(keyword not in msg for keyword in listen_list):
                time.sleep(1)
                continue
            wk.record(f'监听到消息: {msg}')
            temp_list.append(msg)
            
            # 从wnd_title中提取出几线
            china_tz = pytz.timezone('Asia/Shanghai')
            cur_dt_time = datetime.now(china_tz).strftime("%m-%d %H:%M:%S")
            line = cls.get_line(wk)
            hour = wk.ocr(*RECT_SHICHEN, color=COLOR_WHITE, zk=ZK_NAME_11)
            info_list.append(f'{cur_dt_time} {hour} {line} {msg}')
            

    @classmethod
    def get_line(self, wk: Worker):
        wnd_title = wk.get_window_title(wk.hwnd)
        match = re.search(r'([一二三四五六]+线)', wnd_title)
        line = match.group(1) if match else "未知线路"
        return line

    @classmethod
    def get_default_biz_config(cls):
        return {
            "监听关键字列表": [
                "在占天时竟然获得了紫色天命",
                "锣鼓喧天",
                "砸中了头",
            ],
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        lst_listen_world = settings.wnd_main.lst_listen_world
        lst_listen_world.clear()
        lst_listen_world.addItems(cls.CONFIG["监听关键字列表"])


    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        lst_listen_world = settings.wnd_main.lst_listen_world
        cls.CONFIG["监听关键字列表"] = [lst_listen_world.item(i).text()
                           for i in range(lst_listen_world.count())]
        # print('监听关键字列表:', cls.CONFIG["监听关键字列表"])
        super().cfg_save(plan_name)


class TaskSwitchAccount(TaskBase):
    TASK_NAME = "切号"
    IS_VALUE = True
    VALUE_ID = 2

    @classmethod
    def run(cls, wk: Worker):
        print('TaskSwitchAccount')
        ...

    # @classmethod
    # def get_default_biz_config(cls):
    #     return {
    #         "账密列表": [],
    #     }

    # @classmethod
    # def cfg_read(cls, plan_name: str):  # 文件到控件
    #     config_path = f'config/account_{plan_name}.json'
    #     account_list = file_read_json(config_path).get('账密列表', [])
    #     for row, account_info in enumerate(account_list):
    #         account = decrypt(account_info.get('账号', ''))
    #         password = decrypt(account_info.get('密码', ''))
    #         comment = account_info.get('备注', '')
    #         settings.wnd_main.tbe_switch_account.item(row, 0).setText(account)
    #         settings.wnd_main.tbe_switch_account.item(row, 1).setText(password)
    #         settings.wnd_main.tbe_switch_account.item(row, 2).setText(comment)
        

    # @classmethod
    # def cfg_save(cls, plan_name: str):  # 控件到文件
    #     config_path = f'config/account_{plan_name}.json'
    #     account_list = []

    #     row_count = settings.wnd_main.tbe_switch_account.rowCount()
    #     for row in range(row_count):
    #         account_item = settings.wnd_main.tbe_switch_account.item(row, 0)
    #         password_item = settings.wnd_main.tbe_switch_account.item(row, 1)
    #         comment_item = settings.wnd_main.tbe_switch_account.item(row, 2)
    #         # 获取每个单元格的文本
    #         account = account_item.text() if account_item else ""
    #         password = password_item.text() if password_item else ""
    #         comment = comment_item.text() if comment_item else ""
    #         # 如果所有单元格都是空的，停止遍历
    #         if not account and not password and not comment:
    #             break
    #         # 添加非空行到列表
    #         account_info = {
    #             '账号': encrypt(account),
    #             '密码': encrypt(password),
    #             '备注': comment
    #         }
    #         account_list.append(account_info)

    #     with open(config_path, 'w', encoding='utf-8') as f:
    #         json.dump({'账密列表': account_list}, f, ensure_ascii=False, indent=4)


class TaskGetBack(TaskBase):
    TASK_NAME = "领回归"
    IS_VALUE = True
    VALUE_ID = 3

    @classmethod
    def run(cls, wk: Worker):
        cls.back_to_kai_feng(wk)
        if cls.cur_map(wk) != "开封":
            wk.record("回到开封失败")
            return
        wk.record('正在找礼品发放使...')
        cls.talk_with_cur_map_npc(wk, "礼品发放使", ["豪侠归来"])
        wk.record('正在吃奖章/经验/悟性/礼券书...')
        pics = "初回奖章.bmp|用_经验书.bmp|用_悟性书.bmp|小遁甲.bmp"
        try:
            cls.bag_use_item_all(wk, pics, is_close=False)
        except:
            pass
        wk.record('正在使用初回礼包...')
        cls.bag_use_item(wk, "江湖百宝箱.bmp", is_close=False)
        msleep(400)
        wk.record('正在使用物品...')
        pics = "用_通宝钻.bmp|用_高级行囊月卡.bmp|用_行囊月卡.bmp"
        if wk.cfg_plan_task['用镖局令']:
            pics += "|铜镖局令.bmp"
        if not wk.cfg_plan_task['丢闭关套']:  # 不丢的话就用掉
            pics += "|存_会神香.bmp|存_高级凝神香.bmp"
        cls.bag_use_item_all(wk, pics, is_close=False)
        msleep(400)
        if wk.cfg_plan_task['丢闭关套'] or wk.cfg_plan_task['丢小遁甲']:
            wk.record('正在丢弃物品...')
            pics = ''
            if wk.cfg_plan_task['丢闭关套']:
                pics = '存_会神香.bmp|存_高级凝神香.bmp'
            if wk.cfg_plan_task['丢小遁甲']:
                pics = pics + '|小遁甲.bmp' if pics else '小遁甲.bmp'
            cls.throw_things(wk, pics)
        cls.close_pages(wk)

    @classmethod
    def do_sth_before_use_thing(cls, wk: Worker):
        # 如果出现了遁甲页, 直接关掉, 并丢弃遁甲
        if wk.find_str(*RECT_FULL, "唐门", COLOR_BLACK):
            wk.record("小遁甲干扰了吃礼券书, 所以丢弃小遁甲")
            cls.close_page(wk)
            cls.throw_things(wk, "小遁甲.bmp")
            raise Exception("小遁甲干扰了礼券书")
    
    @classmethod
    def get_default_biz_config(cls):
        return {
            "丢小遁甲": False,
            "丢闭关套": False,
            "用镖局令": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_huigui_throw_xiaodj.setChecked(
            cls.CONFIG["丢小遁甲"])
        settings.wnd_main.chk_huigui_throw_biguan.setChecked(
            cls.CONFIG["丢闭关套"])
        settings.wnd_main.chk_huigui_use_biaoju.setChecked(
            cls.CONFIG["用镖局令"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["丢小遁甲"] = settings.wnd_main.chk_huigui_throw_xiaodj.isChecked()
        cls.CONFIG["丢闭关套"] = settings.wnd_main.chk_huigui_throw_biguan.isChecked()
        cls.CONFIG["用镖局令"] = settings.wnd_main.chk_huigui_use_biaoju.isChecked()
        super().cfg_save(plan_name)
