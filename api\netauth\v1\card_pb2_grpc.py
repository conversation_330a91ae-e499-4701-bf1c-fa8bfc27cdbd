# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from api.netauth.v1 import card_pb2 as api_dot_netauth_dot_v1_dot_card__pb2


class CardStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCardInfoV2 = channel.unary_unary(
                '/api.netauth.v1.Card/GetCardInfoV2',
                request_serializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCardInfoRequestV2.SerializeToString,
                response_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCardInfoReplyV2.FromString,
                )
        self.UnBindCard = channel.unary_unary(
                '/api.netauth.v1.Card/UnBindCard',
                request_serializer=api_dot_netauth_dot_v1_dot_card__pb2.UnBindCardRequest.SerializeToString,
                response_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.UnBindCardReply.FromString,
                )
        self.GetUpdateInfo = channel.unary_unary(
                '/api.netauth.v1.Card/GetUpdateInfo',
                request_serializer=api_dot_netauth_dot_v1_dot_card__pb2.GetUpdateInfoRequest.SerializeToString,
                response_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.GetUpdateInfoReply.FromString,
                )
        self.SetLatestVersionInfo = channel.unary_unary(
                '/api.netauth.v1.Card/SetLatestVersionInfo',
                request_serializer=api_dot_netauth_dot_v1_dot_card__pb2.SetLatestVersionInfoRequest.SerializeToString,
                response_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.SetLatestVersionInfoReply.FromString,
                )
        self.GetCustomInfo = channel.unary_unary(
                '/api.netauth.v1.Card/GetCustomInfo',
                request_serializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCustomInfoRequest.SerializeToString,
                response_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCustomInfoReply.FromString,
                )


class CardServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetCardInfoV2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UnBindCard(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetUpdateInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetLatestVersionInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCustomInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CardServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCardInfoV2': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCardInfoV2,
                    request_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCardInfoRequestV2.FromString,
                    response_serializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCardInfoReplyV2.SerializeToString,
            ),
            'UnBindCard': grpc.unary_unary_rpc_method_handler(
                    servicer.UnBindCard,
                    request_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.UnBindCardRequest.FromString,
                    response_serializer=api_dot_netauth_dot_v1_dot_card__pb2.UnBindCardReply.SerializeToString,
            ),
            'GetUpdateInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUpdateInfo,
                    request_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.GetUpdateInfoRequest.FromString,
                    response_serializer=api_dot_netauth_dot_v1_dot_card__pb2.GetUpdateInfoReply.SerializeToString,
            ),
            'SetLatestVersionInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.SetLatestVersionInfo,
                    request_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.SetLatestVersionInfoRequest.FromString,
                    response_serializer=api_dot_netauth_dot_v1_dot_card__pb2.SetLatestVersionInfoReply.SerializeToString,
            ),
            'GetCustomInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCustomInfo,
                    request_deserializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCustomInfoRequest.FromString,
                    response_serializer=api_dot_netauth_dot_v1_dot_card__pb2.GetCustomInfoReply.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'api.netauth.v1.Card', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Card(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetCardInfoV2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/api.netauth.v1.Card/GetCardInfoV2',
            api_dot_netauth_dot_v1_dot_card__pb2.GetCardInfoRequestV2.SerializeToString,
            api_dot_netauth_dot_v1_dot_card__pb2.GetCardInfoReplyV2.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UnBindCard(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/api.netauth.v1.Card/UnBindCard',
            api_dot_netauth_dot_v1_dot_card__pb2.UnBindCardRequest.SerializeToString,
            api_dot_netauth_dot_v1_dot_card__pb2.UnBindCardReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetUpdateInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/api.netauth.v1.Card/GetUpdateInfo',
            api_dot_netauth_dot_v1_dot_card__pb2.GetUpdateInfoRequest.SerializeToString,
            api_dot_netauth_dot_v1_dot_card__pb2.GetUpdateInfoReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetLatestVersionInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/api.netauth.v1.Card/SetLatestVersionInfo',
            api_dot_netauth_dot_v1_dot_card__pb2.SetLatestVersionInfoRequest.SerializeToString,
            api_dot_netauth_dot_v1_dot_card__pb2.SetLatestVersionInfoReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCustomInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/api.netauth.v1.Card/GetCustomInfo',
            api_dot_netauth_dot_v1_dot_card__pb2.GetCustomInfoRequest.SerializeToString,
            api_dot_netauth_dot_v1_dot_card__pb2.GetCustomInfoReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
