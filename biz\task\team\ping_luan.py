from PySide2.QtCore import QTime

from biz.task._jrw_xl_dg import TaskJrwXlDg
from biz.constants.constants import *
from biz.obj.worker import Worker
from utils import *
from biz.constants.constants import *


class TaskPingLuan(TaskJrwXlDg):
    TASK_NAME = "带队平乱"
    
    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        if wk.cfg_plan_task["切首发宠"]:
            wk.team.is_switch_primary_bb_enable = True
        else:
            wk.team.is_switch_primary_bb_enable = False

    @classmethod
    def get_task_name(cls) -> str:
        return "平乱任务"

    @classmethod
    def get_task_publisher_name(cls) -> str:
        return "曹都尉"
    
    @classmethod
    def handle_final_exception(cls, wk: Worker):
        cls.refresh_task_list(wk)
        return "pass"
    
    @classmethod
    def talk_recv_task(cls, wk: Worker) -> bool:
        res = wk.find_str_click(*RECT_TALK, "尽管吩咐", COLOR_TALK_ITEM)
        if res:
            if settings.exec_rate != 1.5:
                if cls.is_talk_show_info(wk, "已完成足够数量", timeout=300):
                    wk.record("队伍中有人已经做完了240环, 自动结束")
                    raise Exception("队伍中有人已经做完了240环, 自动结束")
            wk.move_click(268,326)  # 没问题
        return res
    
    @classmethod
    def task_npc_find_way(cls, wk: Worker, specify_item="", color=COLOR_TALK_ITEM, npc_name="", npc_map=""):
        far_maps = ["神龙岛", "无量山", "青云幽谷"]
        if npc_map in far_maps and cls.is_cjdj_enable(wk):  # 目标地图很远, 且有超遁
            cur_map_name = cls.cur_map(wk)
            if cur_map_name != npc_map:  # 而且当前的地图不在远目标地图
                wk.record(f"当前地图:{cur_map_name}, 目标地图:{npc_map} 过远, 自动超遁")
                cls.close_pages(wk)
                npc_name = cls.fast_fly(wk)
        return super().task_npc_find_way(wk, specify_item, color, npc_name, npc_map)
    
    @classmethod
    def is_reach_time(cls, wk: Worker):
        if wk.cfg_plan_task["定时结束"]:
            if wk.cur_task_start_time_fmt > wk.cfg_plan_task["定时结束时间"]:
                wk.record("任务开始时间超过定时结束时间, 忽略定时结束")
                wk.cfg_plan_task["定时结束"] = False
                return False
            if settings.cur_time_fmt[:5] >= wk.cfg_plan_task["定时结束时间"]:
                wk.record("定时结束时间到")
                return True
        return False
    
    @classmethod
    def handle_find_way_often(cls, wk: Worker):
        raise Exception("寻路超过次数")
    
    @classmethod
    def enable_task_fix_equip_bb(cls, wk: Worker):
        return wk.cfg_plan_task["修理忠诚"]

    @classmethod
    def get_task_setting_count(cls, wk: Worker) -> int:
        return wk.cfg_plan_task["次数"]
    
    @classmethod
    def after_fight_switch_primary_bb(cls, wk: Worker):
        if wk.cfg_plan_task["切首发宠"]:
            wk.team.is_switch_primary_bb_enable = True
        else:
            wk.team.is_switch_primary_bb_enable = False
        if wk.team.is_switch_primary_bb_enable:
            cls.switch_primary_bb(wk)

    @classmethod
    def get_default_biz_config(cls):
        return {
            "次数": 240,
            "定时结束": False,
            "定时结束时间": "14:04",
            "修理忠诚": False,
            "切首发宠": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_ping_luan.setValue(cls.CONFIG["次数"])
        settings.wnd_main.groupBox_time_stop_ping_luan.setChecked(cls.CONFIG["定时结束"])
        settings.wnd_main.tedt_timer_ping_luan.setTime(QTime.fromString(cls.CONFIG["定时结束时间"], "HH:mm"))
        settings.wnd_main.chk_ping_luan_fix_bb.setChecked(cls.CONFIG["修理忠诚"])
        settings.wnd_main.chk_ping_luan_switch_primary_bb.setChecked(cls.CONFIG["切首发宠"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["次数"] = int(settings.wnd_main.spin_count_ping_luan.value())
        cls.CONFIG["定时结束"] = settings.wnd_main.groupBox_time_stop_ping_luan.isChecked()
        cls.CONFIG["定时结束时间"] = settings.wnd_main.tedt_timer_ping_luan.time().toString("HH:mm")
        cls.CONFIG["修理忠诚"] = settings.wnd_main.chk_ping_luan_fix_bb.isChecked()
        cls.CONFIG["切首发宠"] = settings.wnd_main.chk_ping_luan_switch_primary_bb.isChecked()
        super().cfg_save(plan_name)
