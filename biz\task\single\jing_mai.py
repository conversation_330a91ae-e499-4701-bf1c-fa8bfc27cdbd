from biz.constants.constants import *
from biz.task.single.nei_gong import TaskNeiGong
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskJingMai(TaskNeiGong):
    TASK_NAME = "经脉贯通"
    DONE_TIP = "经脉畅通"

    @classmethod
    def run(cls, wk: Worker):
        cls.leave_team(wk)
        if not cls.is_in_bi_guan(wk):
            if not cls.back_to_school(wk):
                wk.record("回门派失败，自动结束")
                return
        try:
            cls.open_bi_guan_page(wk, item="贯通")
        except:
            return
        for i in range(1800):
            msleep(2000)
            if not cls.is_in_bi_guan(wk):
                wk.record("已被传送出闭关场所，结束闭关")
                break
            if i == 0 or rnd(0, 5) == 1:
                wk.record("经脉贯通中...")
            if cls.is_biguan_full(wk):
                wk.record("闭关完成，结束闭关")
                wk.find_pic_click(*RECT_FULL, "结束闭关.bmp")
                msleep(500)
                break
        wk.find_pic_click(*RECT_FULL, "结束闭关.bmp")  # 有时候点一次会没点到
