from biz.task._jrw_xl_dg import TaskJrwXlDg
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskSiXiang(TaskJrwXlDg):
    TASK_NAME = "四象跑环"
    IS_TEAM_TASK = False
    IS_DIFFICULT_TASK = True
    NEED_AVOID_FIGHT = True
    IS_TASK_FIX_EQUIP_BB_ENABLE = True

    @classmethod
    def run(cls, wk: Worker):
        if not cls.is_ride_have_skill(wk, "麾马"):
            wk.record("没有麾马，直接离队跑四象")
            cls.leave_team(wk)
        elif cls.get_teammate_count(wk) > 1:
            wk.record("不能带着队友接四象, 正在退队...")
            cls.leave_team(wk)
        super().run(wk)
        cls.back_to_kai_feng(wk)

    @classmethod
    def receive_task(cls, wk: Worker):
        cls.back_to_school(wk)
        cls.do_receive_task(wk)

    @classmethod
    def do_receive_task(cls, wk: Worker, npc_name=""):
        npc_name = cls.get_task_publisher_name()
        cls.do_fix_bb(wk)
        cls.do_buy_xmx(wk)
        return super().do_receive_task(wk, npc_name)
    
    @classmethod
    def click_system_task_name(cls, wk: Worker, enable_white=False):
        if wk.cfg_plan_task['买环']:
            enable_white = True
        return super().click_system_task_name(wk, enable_white=enable_white)

    @classmethod
    def find_way_fight(cls, wk: Worker):
        # 先检查下血内, 可能内功绝艺消耗大
        if cls.people_need_supply(wk):
            wk.record("血内低于战后补充阈值, 前往开封客栈补充...")
            cls.back_to_kai_feng(wk)
        super().find_way_fight(wk)

    @classmethod
    def get_task_name(cls) -> str:
        return "四象灵力任务"

    @classmethod
    def get_task_publisher_name(cls) -> str:
        return "玄金真人|孔辰|执善堂主事|藏经阁主事|唐大"

    @classmethod
    def get_enemy_talk_item(cls):
        return "挑战"
    
    @classmethod
    def is_enemy_hit_lsgs(cls, wk: Worker):
        x, y = POS_ENENMY_LIST[IDX_BOSS]
        return wk.is_region_color_count_enough(
            x-41, y-83, x+47, y-33, COLOR_GESHI, min_count=200,
            color2=COLOR_SHIHOU, min_count2=160, timeout=900)

    @classmethod
    def goto_recv_task_place(cls, wk: Worker):
        cls.back_to_school(wk)

    @classmethod
    def do_buy_ring(cls, wk: Worker):
        wk.record('正在购买四象环...')
        cls.close_other_talk(wk)
        msleep(400)
        for i in range(100):
            if cls.talk_click_specify_item(wk, '购买四象'):
                msleep(800)
                if cls.click_confirm(wk, timeout=400):
                    wk.record('买环成功')
                break
            if i == 0 or wk.is_stuck:
                cls.click_system_task_name(wk)
                msleep(1000)

    @classmethod
    def handle_final_exception(cls, wk: Worker):
        cls.refresh_task_list(wk)
        return "pass"

    @classmethod
    def talk_recv_task(cls, wk: Worker) -> bool:
        res = wk.find_str_click(*RECT_TALK, "领取四象", COLOR_TALK_ITEM)
        if cls.is_talk_show_info(wk, "今日已经完成了", COLOR_BLACK, timeout=200):
            wk.record("任务完成达到上限")
            if wk.cfg_plan_task['买环']:
                cls.do_buy_ring(wk)
            else:
                raise Exception("任务完成达到上限")
        elif cls.is_talk_show_info(wk, "悟性", COLOR_BLACK):
            wk.record("悟性不够")
            cls.close_other_talk(wk)
            raise Exception("任务完成达到上限")
        return res

    @classmethod
    def get_task_setting_count(cls, wk: Worker) -> int:
        return wk.cfg_plan_task["次数"]

    @classmethod
    def get_default_biz_config(cls):
        return {
            "次数": 20,
            "买环": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_si_xiang.setValue(cls.CONFIG["次数"])
        settings.wnd_main.chk_si_xiang_buy.setChecked(cls.CONFIG["买环"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["次数"] = settings.wnd_main.spin_count_si_xiang.value()
        cls.CONFIG["买环"] = settings.wnd_main.chk_si_xiang_buy.isChecked()
        super().cfg_save(plan_name)
