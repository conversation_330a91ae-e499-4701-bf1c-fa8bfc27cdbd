from PySide2.QtCore import QMutexLocker

from utils.plugins.dm import Dm
from .utils import *


class Op(Dm):
    COM_NAME = "op.opsoft"
    DLL_NAME = "op_x86.dll"
    REG_NAME = "tools.dll"

    def bind_window(self, hwnd: int, mode_display, mode_mouse, mode_keypad, mode_back, mode_public="") -> int:
        locker = QMutexLocker(self.mutex)
        print('mode_display:', mode_display, 'mode_mouse:', mode_mouse, 'mode_keypad:', mode_keypad, 'mode_back:', mode_back)
        return self.obj.BindWindow(hwnd, mode_display, mode_mouse, mode_keypad, mode_back)