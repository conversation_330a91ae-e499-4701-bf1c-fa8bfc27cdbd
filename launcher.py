import os
import ctypes
from ctypes import wintypes
import shutil
import time

kernel32 = ctypes.windll.kernel32
user32 = ctypes.windll.user32

class PROCESS_INFORMATION(ctypes.Structure):
    _fields_ = [
        ("hProcess", wintypes.HANDLE),
        ("hThread", wintypes.HANDLE),
        ("dwProcessId", wintypes.DWORD),
        ("dwThreadId", wintypes.DWORD)
    ]

def launch_main(main_exe_name):
    start_info = ctypes.create_string_buffer(68)
    start_info.dwFlags = 0x00000080  # STARTF_FORCEOFFFEEDBACK
    
    process_info = PROCESS_INFORMATION()
    
    if not kernel32.CreateProcessW(None, ctypes.c_wchar_p(main_exe_name), None, None, False, 0, None, None, ctypes.byref(start_info), ctypes.byref(process_info)):
        return False
    
    # user32.WaitForInputIdle(process_info.hProcess, 0xFFFFFFFF)
    kernel32.CloseHandle(process_info.hThread)
    kernel32.CloseHandle(process_info.hProcess)


def copy_file_safely(source_path, target_path):
    while True:
        try:
            print("正在复制文件 '%s' 到 '%s'..." % (source_path, target_path))
            shutil.copyfile(source_path, target_path)
            break  # 复制成功，退出循环
        except:
            # 捕获文件被占用的异常
            print(f"文件 '{source_path}' 正在被其他程序使用，等待文件释放...")
            time.sleep(0.1)  # 等待0.1秒后重试


def copy_tree_safe(target_dir, source_dir):
    try:
        print("正在复制目录 '%s' 到 '%s'..." % (source_dir, target_dir))
        if os.path.exists(target_dir):
            print("目标目录 '%s' 已存在，正在删除..." % target_dir)
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        print("目录 '%s' 复制完成" % target_dir)
    except Exception as e:
        print("shutil.copytree: ", e)

def remove_dir_safe(target_dir):
    try:
        shutil.rmtree(target_dir)
    except:
        pass

def update_main(main_exe_name):
    print('start update main')
    if not os.path.exists('./patcher'):
        return
    if not os.listdir('./patcher'):
        remove_dir_safe('./patcher')
        return
    # 更新biz目录的资源文件 source_dir -> target_dir
    source_dir = 'patcher/biz'
    target_dir =  './biz'
    if os.path.exists(source_dir):  # 如果目标目录存在才拷贝目录过去
        copy_tree_safe(target_dir, source_dir)  # 再复制目标目录中的内容到源目录
    # 更新main
    source_path = f'patcher/{main_exe_name}'
    target_path = main_exe_name
    copy_file_safely(source_path, target_path)
    # 最后把patcher.zip和patcher目录删除
    try:
        shutil.rmtree('patcher')
        os.remove('patcher.zip')
    except:
        pass
    
def remove_temp_installer():
    from utils.file import file_remove
    path = os.path.join(os.environ['USERPROFILE'], 'Downloads', 'qlinstaller.exe')
    file_remove(path)


if __name__ == '__main__':
    main_exe_name = 'opengl32ws.dll'
    launch_main(main_exe_name)