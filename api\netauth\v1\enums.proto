syntax = "proto3";

package api.netauth.v1;

enum CardStatus {
	Normal = 0;  // 正常
	Expired = 1;  // 已过期
	Frozen = 2;  // 已冻结
}

enum CardRights {
	One = 0;  // 单开
	Five = 1;  // 五开
	Ten = 2;  // 十开
	Thirty = 3;  // 三十开
    Twenty = 4;  // 二十开
    Sixty = 5;  // 六十开
}

enum CardType {
	Day = 0;  // 天卡=1天
	Month = 1;  // 月卡=30天
	Year = 2;  // 年卡=365天
	Forever = 3;  // 永久卡=3650天
}

enum PaymentMethod {
	Alipay = 0;  // 支付宝
	Wechat = 1;  // 微信
}