import copy
from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskSuiCongDaShu(TaskBase):
    TASK_NAME = "随从打书"
    IS_FREE = True
    IS_TEAM_TASK = False
    IS_DIFFICULT_TASK = False
    NEED_PLAYER_NAME = False

    @classmethod
    def before_run(cls, wk: Worker):
        pass

    @classmethod
    def after_run(cls, wk: Worker):
        pass

    @classmethod
    def run(cls, wk: Worker):
        if not cls.is_bb_page_open(wk):
            wk.record(f"请打开随从页选好BB后 按鼠标右键启动...")
            wk.wait_key(VK_MOUSE_RIGHT)
        pos_gen = cls.cycle_list(wk)
        while True:
            cls.click_confirm(wk, timeout=0)
            if wk.find_pic_click(*RECT_FULL, "使用.bmp", timeout=200):
                wk.record('打书中...')
                msleep(200)
            pos = next(pos_gen)
            print('pos:', pos)
            wk.move_r_click(*pos)
            
    
    @classmethod
    def cycle_list(cls, wk: Worker):
        """循环遍历列表，到达末尾后从头开始"""
        lst = list(range(12))
        pos_lst = [
            (351, 218), (402, 218), (453, 218), (504, 218),
            (351, 269), (402, 269), (453, 269), (504, 269),
            (351, 321), (402, 321), (453, 321), (504, 321),
        ]
        index = -1
        while True:
            index = (index + 1) % len(lst)  # 通过取模运算实现循环索引
            cls.click_confirm(wk, timeout=0)
            length = len(wk.find_pic_ex(
                *RECT_FULL, "随从初书.bmp|随从中书.bmp|随从高书.bmp", timeout=400))
            if not length:
                yield pos_lst[0]
                continue
            length = min(12, length)
            temp_lst = lst[:length]
            if index >= len(temp_lst):
                random_idx = rnd(0, len(temp_lst)-1) if len(temp_lst) > 1 else 0
                yield pos_lst[random_idx]
                continue
            print(f'index:{index}, len(temp_lst):{len(temp_lst)}')
            idx = temp_lst[index]
            print('yield:', idx)
            yield pos_lst[idx]
            
    