ZK_ALL_11 = 0
ZK_DIGIT_11 = 1
ZK_ALL_9 = 2
ZK_DIGIT_9 = 3
ZK_NAME_11 = 4
ZK_BOLD_11 = 5
ZK_DIGIT_11_EX = 6

FOOBAR_X = 65
FOOBAR_Y = 574
FOOBAR_W = 350
FOOBAR_H = 18
FOOBAR_RW = 0
FOOBAR_RH = 0
FOOBAR_TEXT_COLOR = "00ff75"

LAUNCHER_TITLE = "倚天剑与屠龙刀 游戏启动程序"
LAUNCHER_CLASS = "#32770"
WND_TITLE = "倚天剑与屠龙刀—刀剑如梦"
WND_CLASS = "zmyth.l2d.Frame"
PROC_NAME = ""
W_GAME = 880
H_GAME = 635
FILE_LISTEN_LOG = 'biz/data/监听世界记录.txt'


Y_OVERLAP = 350
CX = 440
CY = 320
PEOPLE_NAME_X = 400
PEOPLE_NAME_Y = 218
COUNT_ONE_BAG_THING = 20
COUNT_ONE_DEPOT_THING = 25
IDX_BOSS = 2
SMALL_MAP_CENTER_X = 813
SMALL_MAP_CENTER_Y = 84
Y_LIMIT_CLICK = 542

POS_CHECK_STUCK1 = (370, CY)
POS_CHECK_STUCK2 = (560, CY)
POS_CHECK_STUCK3 = (CX, CY + 100)
POS_TEAM_PLATFORM = (63, 84)
POS_CLAN = (36, 83)
POS_SYSTEM_DOWN = (423, 306)
POS_TASK_DESC_DOWN = (640, 412)
POS_DEPOT_LIST = [
    (164, 109), (255, 108), (343, 106),
    (162, 135), (255, 135), (344, 134)
]
POS_PLATFORM_SWITCH = (305, 128)
POS_SHOP_SELL = (277, 113)
POS_BIG_MAP = (701, 36)
POS_PET_FIGHT = (161, 271)
POS_PEOPLE_AVATAR = (33, 37)
POS_BB_AVATAR = (175, 14)
POS_WUXING_LUNPAN = (695, 20)
POS_TIANMING = (793, 594)
POS_TIANMING_YI_JIAN = (440, 504)
POS_SHANGLV = (734, 142)  # 商旅护送
POS_ZHJJ = (636, 139)  # 战魂竞技
POS_ZHENGZHAN = (685, 142)  # 征战江湖
POS_LIJICANJIA = (638, 521)
POS_LANJIE = (420, 111)
POS_YIBAN = (403, 351)
POS_FAQILANJIE = (639, 471)  # 发起拦截
POS_DUNJIA_KAIFENG = (508, 183)  # 小遁甲-开封
POS_QUICK_FUNC = (79, 81)
POS_XUNMA_NUM = (475, 381)
POS_XUNMA_CONFIRM = (390, 466)
POS_LOGIN_ACCOUNT = (483, 226)
POS_LOGIN_PASSWORD = (475, 321)
POS_CLOSE_SHENGLI = (589, 169)  # 征战 和 商旅 战斗的关闭
POS_GIFT = (847, 486)
POS_FEST = (847, 240)
POS_QIANDAO = (652, 443)
POS_LINGDI_CLOSE = (563, 114)
POS_SWITCH_CHANNEL = (42, 580)
POS_CHANNEL_BANG_HUI = (48, 460)
POS_CHAT = (362, 605)
POS_SMALL_MAP_CENTER = (811, 89)
POS_JI_XIAN_CHENG_HAO_RED = (329, 347)
POS_JI_XIAN_CHENG_HAO_BLUE = (494, 345)
POS_JI_XIAN_AWARD_CONFIRM = (322, 405)
POS_JI_SHOU_INPUT = (162, 100)
POS_JI_SHOU_NEXT = (570, 525)
POS_JI_SHOU_PREV = (442, 526)
POS_JI_SHOU_MY = (291, 528)
POS_JI_SHOU_TMP_DEPOT = (493, 153)
POS_JI_SHOU_ONE_KEY = (249, 375)
POS_JI_SHOU_ZAI_SHOU = (237, 153)
POS_JI_SHOU_JI_SHOU = (268, 420)
POS_JI_SHOU_SINGLE_PRICE = (180, 225)
POS_JI_SHOU_CONFIRM1 = (62, 258)
POS_JI_SHOU_CONFIRM2 = (238, 481)
POS_SHANG_LV_DOWN = (508, 448)
POS_MENG_ZHU = (735, 132)
POS_MC_JIACAOLIAO = (309, 449)
POS_MC_SHIYAO = (414, 449)
POS_MC_KAISHUI = (525,450)
POS_MC_JIACAOLIAO_TF = (400, 235)
POS_MC_JIACAOLIAO_CONFIRM = (298, 328)
POS_HUILU_ZHUANGBEI = (636,196)
POS_HUILU_CONFIRM = (391,468)
POS_HUILU_CONFIRM2 = (388,344)
POS_WEIYANG = (544, 474)
POS_WEIYANG_CONFIRM = (695, 445)

RECT_FULL = (0, 0, 880, 635)
RECT_XPAGE = (600,60,814,158)
RECT_LEFT = (0, 0, 440, 635)
RECT_DOWN = (0, 320, 880, 635)
RECT_RIGHT = (440, 0, 880, 635)
RECT_LEFT_TOP = (0, 0, 267, 158)
RECT_IN_GAME = (3, 615, 27, 634)
RECT_PEOPLE_BB_AVATAR = (2, 1, 247, 73)
RECT_CHECK_AUTH = (374, 134, 458, 190)
RECT_CHECK_LOGIN = (371, 78, 449, 145)
RECT_CHECK_SEL_SERVER = (38, 358, 109, 428)
RECT_SEL_SERVER_POPUP = (389, 279, 487, 327)
RECT_POPUP = (281, 238, 599, 378)
RECT_FIND = (0, 40, 880, 543)
RECT_FIND_SMALL = (0, 150, 880, 543)
RECT_AUTH = (138, 204, 727, 503)
RECT_FIGHT_TOP = (410, 0, 450, 24)
RECT_FIGHT_TOP_BIG = (410, 0, 640, 24)
RECT_SYSTEM_BROADCAST = (253, 0, 692, 105)
RECT_SYSTEM_TASK = (222, 158, 439, 329)
RECT_SYSTEM_TASK_DESC = (233, 349, 629, 423)
RECT_TALK = (204, 238, 680, 550)
RECT_TALK_SMALL = (204, 238, 680, 370)
RECT_TALK_CONTENT = (204, 269, 680, 550)
RECT_TALK_NAME = (204, 238, 334, 265)
RECT_FIGHT_ENEMY = (79, 87, 542, 341)
RECT_FIGHT_SELF = (364, 180, 865, 543)
RECT_MAP = (340, 45, 877, 458)
RECT_CUR_MAP = (717, 5, 810, 23)
RECT_MENU = (450, 571, 876, 620)
RECT_BUFF = (70, 50, 273, 140)
RECT_INFO = (243, 0, 619, 178)
RECT_TEAM_AVATAR = (1, 117, 45, 363)
RECT_SYSTEM_ACTIVITY = (429, 147, 662, 332)
RECT_SELL_THING = (CX, 224, 880, 600)
RECT_STORE_THING = (401, 175, 764, 498)
RECT_FETCH_THING_CONFIRM = (114, 149, 452, 487)
RECT_CHANNEL = (22, 571, 66, 599)
RECT_DEPOT = (115, 78, 399, 498)
RECT_TOU_DIAN = (318, 245, 1060, 699)
RECT_BB_SKILL = (600, 0, 880, 500)
RECT_BI_GUAN = (246, 440, 473, 526)
RECT_BI_GUAN_PROC = (524, 441, 606, 468)
RECT_SKILL = (447, 508, 879, 578)
RECT_AVATAR_TEAM = (1, 120, 26, 340)
RECT_DAOJU = (640, 380, 852, 538)
RECT_MAP_NAME = (717, 3, 789, 26)
RECT_MAP_HOUR = (854, 123, 877, 144)
RECT_SHICHEN = (746,122,880,148)
RECT_MAP_HUAN_XIAN = (842, 24, 873, 53)
RECT_WUXING_LUNPAN = (686, 8, 715, 42)
RECT_PEOPLE_ACTION = (799, 58, 866, 190)
RECT_BB_ACTION = (500, 0, 880, 550)
RECT_TERSE_BB = (329, 108, 490, 210)
RECT_SKIP_FIGHT = (598, 0, 784, 34)
RECT_WALK = (451, 547, 475, 571)
RECT_SMALL_MAP = (747, 26, 875, 129)
RECT_RIGHT_TOP = (694, 4, 876, 166)
RECT_FUJIA_DAOJU = (517, 475, 792, 533)
RECT_BB_AVATAR = (156, 3, 191, 39)
RECT_RUN_AWAY = (736, 155, 866, 247)
RECT_CAI_LIAO_HAO = (407, 343, 562, 425)
RECT_TRADE_NAME = (347, 293, 466, 319)
RECT_TRADE_LOCK_SELF = (450, 496, 536, 531)
RECT_TRADE_LOCK_AGAINST = (450, 142, 539, 165)
RECT_TRADE_AMOUNT_SELF = (288, 505, 398, 528)
RECT_TRADE_AMOUNT_AGAINST = (289, 144, 397, 165)
RECT_TRADE_CONFIRM = (280, 517, 380, 573)
RECT_GAME_LAUNCHER_START = (672, 486, 780, 555)
RECT_GIFT = (792, 483, 868, 613)
RECT_FEST = (824, 211, 874, 261)
RECT_QIAN_DAO = (311, 221, 385, 255)
RECT_XIANG_RUI = (477, 221, 540, 250)
RECT_LING_DI = (319, 338, 379, 368)
RECT_LING_DI2 = (448, 401, 506, 454)
RECT_TODAY_TIANJIANG = (294, 102, 584, 391)
RECT_CALL_BB = (600, 345, 871, 588)
RECT_CALL_BB_LIST = (660, 345, 871, 588)
RECT_SHOUT = (2, 445, 104, 602)
RECT_FIGHT_DIE = (534, 351, 627, 403)
RECT_BOSS_NAME = (211, 166, 354, 199)
RECT_JI_SHOU_NAME = (295, 121, 390, 510)
RECT_JI_SHOU_GOODS = (418, 182, 684, 495)
RECT_SELL_GOODS_NAME = (73, 154, 189, 182)
RECT_SELL_GOODS_NUM = (68, 181, 102, 209)
RECT_CAPTURE = (99, 10, 844, 615)
RECT_JI_XIAN_CUR_RING = (294, 161, 330, 200)
RECT_JI_XIAN_FAIL_COUNT = (424, 184, 461, 211)
RECT_DIFFICULTY = (373, 154, 495, 458)
RECT_ACCOUNT_INPUT = (384, 215, 440, 230)
RECT_PASSWORD_INPUT = (383, 307, 425, 328)
RECT_CENTER = (397, 217, 479, 282)
RECT_XI_NIAN_NUMBER = (524,234,594,337)
RECT_MUCHANG_PAGE = (237, 240, 319, 308)
RECT_ZH_FIGHT_CLOSE = (539, 151, 608, 188)

COLOR_WHITE = "ffffff"
COLOR_BLACK = "000000"
COLOR_GREEN = "00ff00"
COLOR_GOLD = "ffff00"
COLOR_PRICE_GREEN = "00ff01"
COLOR_QUALITY_BLUE = "0099ff"
COLOR_QUALITY_GREEN = "00ff66"
COLOR_QUALITY_ORANGE = "ff6600"
COLOR_QUALITY_PURPLE = "ea3dff"
COLOR_HUNDRED = "ff9c00"
COLOR_THOUSAND = "ff96fc"
COLOR_TALK_ITEM_TASK = "095ed1"
COLOR_TALK_ITEM_TASK_DOING = "127c49"
COLOR_TALK_ITEM = "2b6073"
COLOR_TASK_NAME = "f8f2c3"
COLOR_SERVER_FREE = "3cff00"
COLOR_SERVER_BUSY = "e8ff70"
COLOR_LOGINING = "c5eaff"
COLOR_LOADING = "2e240f"
COLOR_RED = "ff0000"
COLOR_PLATFORM_MAP = "f4e99c"
COLOR_DUN_JIA_NPC = "5fb8be"
COLOR_CYAN = "64fffa"
COLOR_OBJECT_DESC = "00cdcd"
COLOR_NPC_NAME = "6f0a0a"
COLOR_QI_LING1 = "f078a0"  # BOSS麒麟怪的颜色
COLOR_QI_LING2 = "00ffff"  # 小怪麒麟怪的颜色
COLOR_QI_LING3 = "eddaff"  # 麒麟怪选中的颜色
COLOR_BLOOD = "f73632"  # 血量应该的颜色
COLOR_INTERNAL = "3164c2"  # 蓝量颜色
# 空血槽, 从["1d1d29", "1b1b26", "171c28"]
COLOR_FIGHT_EMPTY_BLOOD = "1a1c27"  # 偏色为050505
# 空蓝槽, 从["1d1c2c", "1e1d2d", "1c1b2b", "1c1c29", "1c1c28", "1c1c25", "181d29"]里面取一个平均值
COLOR_FIGHT_EMPTY_INTERNAL = "1b1c29"  # 偏色为050505
COLOR_ACTIVITY_NAME = "eaa334"
COLOR_TASK_FAIL = "d95d5d"
COLOR_TRADE_NAME = "4800ff"
COLOR_CIXUE = "95bafa-111111|cf7adc-111111|1b30c1-111111|f6ecfb-111111"
COLOR_SHIHOU = "e4e7a8-111111|d1d6a2-111111|bcbea1-111111"
COLOR_GESHI = "ffffff-060606"  # 41,83 47,33
COLOR_NAME_VIP = "9fc8ff"
COLOR_TEAM_SERVER = "296173"
COLOR_TASK_UNFINISH = "127c49"
COLOR_ENABLE = "f7d744"
COLOR_DISABLE = "808080"
COLOR_ACCOUNT = "341a01-111111"
COLOR_ORANGE = "ffc800-000000"
COLOR_CHAKAN = "999999"

SHAPE_CURSOR_NORMAL = "2c73c554"
SHAPE_CURSOR_TALK = "6cc83b2f"
SHAPE_CURSOR_FIGHT1 = "2c2aed00"
SHAPE_CURSOR_FIGHT2 = "845a601"

SHAPE_CURSOR_TRADE = "2d47e180"
SHAPE_CURSOR_TEAM = "2754904"
SHAPE_CURSOR_ATTACK = "249981ff"


MCOLOR_IN_GAME = [(
    "594a36", "0|2|b4a990,6|0|bcb3a1,4|8|837b71,3|9|242322,2|1|252524,6|7|242322,7|2|242322,5|1|242322,5|3|242322,8|1|716963,7|5|242322,7|8|7e766d,6|8|7e766d", 1.0
)]
MCOLOR_DUAN_CHUAN_WUQI = [(
    "000000", "1|10|c2a463,1|11|000000,17|12|c2a463,8|8|c7a968,6|10|000000,21|7|c7a968,17|6|c8aa69,13|9|ccad6c,14|6|000000,15|5|c8aa69,2|12|000000,13|0|c7a968,14|9|000000,5|10|c4a564,8|2|c6a866,20|7|c8aa69,20|1|c8aa69,3|4|000000,18|8|000000,16|9|c5a665,8|11|000000,7|12|000000,18|2|000000,12|12|000000,21|6|c7a968,13|7|c9ab6a,1|3|000000,8|5|c7a968", 1.0
)]
MCOLOR_DUAN_CHUAN_FANG = [(
    "c6a866", "20|3|000000,14|1|000000,17|10|c4a564,22|9|000000,9|11|000000,13|8|c0a261,16|11|c4a564,0|4|c7a968,12|5|000000,8|8|000000,21|5|c4a564,1|9|000000,20|2|c2a463,21|7|c5a665,10|7|c5a665,5|10|c1a362,10|9|c1a362,10|11|c0a261,20|11|c8aa69,14|6|c0a261,23|9|c6a866,2|9|000000,21|13|000000,6|2|000000,15|6|000000,19|11|c8aa69,13|0|c1a362,4|4|000000,23|1|c6a866,11|6|c0a261,3|12|000000,16|7|c2a463,8|9|c2a463", 1.0
)]
MCOLOR_DUAN_CHUAN_SHOU = [(
    "c2a463", "17|7|bfa15f,12|9|c4a564,14|8|000000,2|7|000000,12|13|000000,1|5|c6a866,15|3|000000,19|13|000000,11|2|c1a362,19|14|000000,15|14|c7a968,10|12|c6a866,11|11|000000,2|6|000000,20|13|c7a968,10|5|c2a463,18|0|cbac6b,8|5|c4a564,17|14|c7a968,2|11|c5a665,2|1|c4a564,3|6|c6a866,8|14|c5a665,13|14|c6a866,3|5|c6a866,11|0|c2a463,2|12|000000,3|13|c5a665,19|2|bfa15f,4|2|000000,13|4|c4a564", 1.0
)]
MCOLOR_IN_DUAN = [(
    "816a37", "3|1|382b17,6|2|000000,4|5|352817,8|3|19130a,5|0|8d713f,12|1|130e08,14|5|5f492a,4|2|1b150b,13|3|000000,9|8|7a5c38,0|7|84683d,1|3|1a140b,8|9|46351c,10|0|503f21,5|7|573f25,0|9|000000,1|4|231b10", 1.0
)]
MCOLOR_QUICK_EQUIP = [(
    "c4b49e", "9|2|000000,13|6|eaa147,1|8|000000,8|8|000000,3|7|d98818,9|3|000000,6|11|df9a36,11|7|de8e23,11|6|eaa147,11|12|000000,13|9|e39524,1|6|eda447,6|7|000000,2|0|c4b49e", 1.0
)]
MCOLOR_QUICK_DUAN = [(
    "000000", "10|2|000000,11|0|c8aa69,9|6|000000,11|7|c9ab6a,9|5|c9ab6a,8|7|000000,4|7|000000,0|5|000000,1|11|000000,6|12|000000,7|11|000000,5|10|c6a866,1|9|000000,5|2|c9ab6a,1|6|000000", 1.0
)]
MCOLOR_DUAN_WU_QI_L = [(
    "d66a56", "10|7|8d564d,11|6|380000,9|9|210000,4|7|401a08,3|7|421100,3|10|320000,13|5|a43425,10|4|8e3324,13|3|390000,14|4|ac2d1b,6|1|491c1c,6|0|724044,8|8|180000,6|3|380000,0|9|942609,5|2|7f453f,0|1|d0543e", 1.0
)]
MCOLOR_DUAN_WU_QI_R = [(
    "74c0cc", "12|1|a3d6e3,4|10|0c3f49,4|1|1a3b4e,2|1|346874,6|4|004261,10|3|62ccef,1|6|001c2a,10|4|218bad,13|10|001829,1|2|000d17,10|2|8ddbf2,7|10|004761,1|5|001b2c,11|9|2ac4f3,9|6|096d8c,4|11|001123", 1.0
)]
MCOLOR_DUAN_SEL = [
    ("9e9888", "13|5|767069,11|2|847878,0|2|857d7d,10|10|745b54,7|3|857a69,4|2|847a70,7|10|6d5d45,12|2|847b78,13|0|b0aaa3,1|2|877f7f,2|11|5f5851,2|6|454026,5|4|372900,2|5|2d3000,7|7|ebdb61,13|7|716b64,5|6|e6d569,0|3|776d71", 1.0),
    ("ece5c9", "6|6|e8d66c,12|3|6b6557,4|4|d3c86f,9|9|d0bfaf,2|1|e8dabd,3|11|cdbda6,4|7|392600,2|9|d0c1ab,9|4|ded288,2|5|2d2600,12|8|d5c8b3,9|7|372200,10|10|d4c09e,13|7|ccc1b0,9|2|e6dcc9,8|7|e6d676", 1.0),
]
MCOLOR_DUAN_MAO = [(
    "c58264", "8|1|9c7261,7|5|9a382a,4|0|81584a,8|6|330000,0|8|c84c36,12|5|b6402c,3|1|1f0000,5|2|6d4241,1|3|530500,15|8|c45637,8|4|3a0000,13|1|411a0c,7|0|552c1e,11|9|bf7166,11|7|660600,6|6|9e4531,7|1|8e6150,2|1|220000,12|11|1c0000,3|6|3b0200", 1.0
)]
MCOLOR_DUAN_KAI = [(
    "80bdce", "5|6|2a9fbf,12|9|4ec4f0,9|2|002a30,11|3|001122,10|9|51c2d7,4|4|001121,7|9|0a6279,10|2|012d33,8|7|003d46,8|6|2c6572,6|1|8fb3b7,10|11|001026,3|0|9fbdc0,10|3|001726,1|5|000f1f,6|3|00314e", 1.0
)]
MCOLOR_DUAN_HAND_L = [(
    "c6715e", "11|4|782518,2|4|a8412b,8|4|5c2d1b,12|0|e8beb6,11|2|5f1c17,8|8|623226,7|9|210000,13|2|ed9b91,10|7|cd503d,8|0|a4867b,8|10|64231a,8|1|1c0000,12|7|dc553a,3|5|390000,11|1|260000,4|0|34160c", 1.0
)]
MCOLOR_DUAN_HAND_R = [(
    "42c2eb", "4|6|000f1b,5|1|45869b,8|8|004d66,14|3|001c3e,6|6|000d14,11|7|70b6c9,1|8|00161d,13|0|acdef0,12|5|00162c,9|6|41889f,14|2|000a25,8|1|102d33,14|9|47c6e9,12|7|004054", 1.0
)]
MCOLOR_DUAN_YAO = [(
    "854632", "12|1|a58885,1|3|4f0000,7|5|562214,2|4|2d0000,9|6|664139,0|9|3f0000,13|7|410000,3|9|3b0000,9|0|170000,2|2|9c6961,5|5|6f2308,10|9|66251c,7|8|230000,6|7|410000,3|8|240000", 1.0
)]
MCOLOR_DUAN_XUE = [(
    "8bc1cd", "11|9|4cbdde,10|0|adc5cd,3|9|000816,5|4|3e909d,8|8|53c2dd,13|6|0fb2ef,0|1|7fc2d7,3|5|002f31,1|4|00172f,2|3|4c707d,8|3|5d8697,12|9|47cef2,12|8|23c9f3,1|1|2a515b,9|4|5d8fa3,0|5|48adcd", 1.0
)]
MCOLOR_DUAN_XIANG = [(
    "ce8e74", "2|10|c24d47,4|2|64231a,1|0|bd8b79,10|3|1d0000,11|8|6d2814,8|1|1e0000,3|4|771709,6|1|653626,12|1|170000,12|3|581d0f,9|11|d06b4c,3|7|360200,3|5|661100,6|0|d7ada3,2|5|530000,11|0|a68d84", 1.0
)]
MCOLOR_DUAN_YU = [(
    "6fc6df", "5|8|002b34,12|2|001529,11|0|88a29f,4|6|31a1bd,8|1|000917,5|2|1f5b65,6|8|001418,13|2|00364e,7|10|298b95,6|9|00060a,2|1|05333a,13|1|002229,14|4|41ccf8,6|4|000c0e,2|3|49819a,12|6|25a5cb", 1.0
)]
MCOLOR_DUAN_CONFIRM = [(
    "cb9d64", "9|6|2b1b03,16|11|dfa34a,12|7|986718,15|11|86622c,4|6|000000,9|7|3e2809,12|8|694813,4|1|000000,12|1|2f261e,8|0|d4b085,1|7|4c3109,5|8|2e1d07,16|8|e49c29,14|4|000000,11|9|000000,7|1|b08d62,2|9|000000,15|9|a97828,14|11|64481e,14|6|000000,6|0|d5a973", 1.0
)]
MCOLOR_BB_ACTION = [
	("766a5e","13|8|938c81,7|5|1f211a,6|12|463e00,11|8|978b86,6|3|26231b,8|5|25261f,7|6|c8b9b1,4|0|72665b,7|0|34291d,3|2|565147,9|9|3b2a00,10|2|6a655b,5|1|948d82,10|7|9f9591,0|6|c0b8a4,2|0|968b7f,6|11|aca23f,4|13|6c5a15,9|5|23241d",1.0),
	("85796d","4|7|f2e4c9,2|9|c7bf9f,6|10|faf7c4,3|10|79744a,11|0|74695d,11|6|f4e6cc,13|0|4e4538,9|1|645d53,2|13|45441f,0|13|454422,5|7|ecdec4,7|4|908f89,10|7|ecdec4,8|1|251e14,12|0|81786b,0|9|ded7b3,7|0|baafa3,5|9|5e5634,6|2|767166",1.0),
	("7d7165","0|11|3e240a,8|6|402b09,2|9|54391a,7|10|451b00,9|11|6a4626,7|11|7e4f26,4|7|5e4625,1|7|634c25,5|3|1e1b13,0|14|491d00,0|1|6a6358,10|11|5f4024,5|7|634e2c,5|13|6a2d00,4|9|4d3213,3|13|571f00",1.0)
]
MCOLOR_MUCHANG_PAGE = [
    ("000000", "23|12|c5a462,21|11|5c4d2e,25|8|846e43,26|9|c4a363,2|5|c2a25f,22|6|000000,16|9|c4a361,26|2|000000,23|0|c7a662,12|5|c4a361,4|6|a88c52,27|9|c4a363,24|4|0d0b07,21|2|282214,18|6|000000,6|12|342b1a,1|9|000000,13|0|c6a561,6|9|c4a361,25|9|c4a363,24|1|857042,12|3|000000,22|11|4f4227,18|2|000000,7|4|c4a35e,19|0|c6a561,6|1|0d0b06,27|4|c5a464,8|2|c4a35e,16|11|342b1a,13|3|000000,10|0|c5a45f,25|3|5c4d2f,16|1|c7a662,2|1|000000,26|6|c6a565", 0.8)
]

MCOLOR_TUI_CHU = [(
	"713f15","1|4|b7874f,4|6|bf8841,10|3|9a7f63,3|3|8b551a,4|8|664128,8|1|a58971,5|2|dfbb90,12|4|b28650,6|6|180000,9|8|c09a50,3|6|895009,3|8|62462d,12|3|77512c,3|4|814a03",1.0
)]
MCOLOR_TI_HUAN_XI_NIAN = [
	# 替换
	("d6a24a","6|11|5c3c18,3|8|100a03,9|6|000000,8|2|7c5b36,2|11|0d0804,2|2|2c1d09,10|4|4c3310,2|5|965f15,7|1|000000,7|7|ac6d1a,8|10|291c09,9|8|000000,6|0|dfac7b,6|2|d1924e,10|0|ab9676",1.0),
	("d9ec64","1|4|9fbb25,6|10|2a300e,12|9|defd4e,4|9|2c320e,9|8|000000,12|2|6e7742,10|6|000000,6|7|a0ba2b,3|8|1d2209,12|3|2e3317,10|4|1d2209,6|4|83991f,4|10|2b300e,5|1|000000,5|6|1d2207",1.0),
	# 洗练
	("c7904c","9|12|c58531,10|8|3e2709,14|6|e1941e,8|4|e69434,2|0|c09759,4|9|e9962d,2|9|000000,14|12|291e0c,14|1|e9cca1,7|11|d58738,9|11|c9872a,1|2|0f0a03,15|4|f7b96a,11|7|a96914,13|6|b57616,1|3|be7e20,9|7|d9861a,13|9|e39b28,0|4|d58c1e,3|12|c97f36",0.95),
	("cfdf5b","10|7|3a4411,2|2|a1b82d,0|8|c8e13d,9|10|d0eb45,7|3|dcff45,0|1|d2e94c,14|0|e4efab,14|2|ccdd7e,8|10|d4f047,1|3|afcb27,6|0|afbe62,4|5|d9fe34,5|4|bddd2c,5|5|9eba24,12|2|6d773e,11|0|e3f09e",0.95),
]
MCOLOR_XI_NIAN = [
	("c7904c","9|12|c58531,10|8|3e2709,14|6|e1941e,8|4|e69434,2|0|c09759,4|9|e9962d,2|9|000000,14|12|291e0c,14|1|e9cca1,7|11|d58738,9|11|c9872a,1|2|0f0a03,15|4|f7b96a,11|7|a96914,13|6|b57616,1|3|be7e20,9|7|d9861a,13|9|e39b28,0|4|d58c1e,3|12|c97f36",0.95),
	("cfdf5b","10|7|3a4411,2|2|a1b82d,0|8|c8e13d,9|10|d0eb45,7|3|dcff45,0|1|d2e94c,14|0|e4efab,14|2|ccdd7e,8|10|d4f047,1|3|afcb27,6|0|afbe62,4|5|d9fe34,5|4|bddd2c,5|5|9eba24,12|2|6d773e,11|0|e3f09e",0.95),
]
MCOLOR_ZHUANG_BEI = [
	("f1d4b4", "2|10|c98c3d,18|1|f3cda2,1|3|eda447,19|8|e5a13d,12|3|eaa147,11|7|e49830,5|0|f1d4b4,15|2|f2bd7f,16|9|da9b40,14|6|e39524,12|7|e49830,5|7|000000,10|1|f6c9a4,2|7|e4982c,19|5|de951c,8|0|000000,4|6|000000,13|0|f1d4b4,17|2|f2be7e,16|10|d09344,3|9|d29037", 0.8)
]
MCOLOR_JINGPO = [
	("96795a", "7|4|507eac,2|2|617e9d,10|2|6da5e6,1|7|709eda,5|4|49688f,14|5|141613,0|10|464847,8|5|568dd4,15|11|46474d,7|10|5c82a1,17|8|cf9026,11|11|376897,12|0|4f5863,6|11|254871,4|2|143866,17|0|c09461,2|1|586c7f,11|8|2b5484,10|12|001d4c,6|0|b2a89f,5|7|4970aa,6|10|0e3358,4|0|4f5565", 0.8),
	("afb95b", "3|9|91c639,7|6|b1f26c,1|9|baf163,6|4|6d981f,12|7|3f6300,5|3|81ab37,6|8|315400,3|3|9ac650,9|2|8cc036,6|11|9dbd4f,6|7|396500,0|12|a9bb3f,14|6|8ba81e,8|11|4c5700,5|4|8cb93d,5|2|8cad42,10|9|b3df66,15|5|d2f746,4|11|739d29,5|5|7daa2d", 0.8)
]
MCOLOR_WEIYANG = [
	("cb9d64", "14|4|e4a85c,2|9|e49128,10|8|000000,7|8|0f0a03,11|0|dab89b,12|7|000000,6|10|000000,12|5|000000,5|1|dcb280,12|1|6a5c4d,14|1|6b5f50,7|6|754c0b,6|9|5a3d12,2|1|9f7848,0|4|af6d18,7|9|4a330f,4|12|281c0c,14|7|2f1f08,10|4|000000", 0.8),
	("d7ea69", "1|6|1d2208,5|3|a2bb38,11|6|000000,3|6|aecc2b,11|3|7a883d,2|8|dbff3f,14|2|ecfe9b,12|7|dcff45,4|0|dcea8b,1|10|d3ef44,11|8|0f1105,2|9|ddfd47,7|7|b0cc33,2|5|000000,8|0|686d4a,14|5|dbff3f", 0.8)
]

SAFE_MAP_LIST = ['桃花岛', '开封', '雁门关内', "光明顶", "武当山", "峨嵋山", "唐家堡", "少林", "武林盟"]

WILD_MAP_LIST = ['桃花林', '桃花滩', '有座山', '绝情谷', '十字坡', '王屋山',
                 '通吃岛', '神龙岛', '龙门', '终南山', '白驼山', '碧峰峡', '恶人谷', '无量山',
                 '夷州岛', '雁门关外', '大草原', '古墓一层', '古墓二层', '古墓三层', '秦陵一层',
                 '秦陵二层', '玄霜浅滩', '金风山道', '赤霞渡口', '青云幽谷',
                 '燕子坞', '楼兰古道', '玉门关', '落日谷', '斡难河']

FU_BEN_MAP_LIST = [
    # 人物副本
    '万安寺大厅', '万安寺二层', '万安寺三层', '万安寺四层', '万安寺五层', '万安寺六层', '万安寺七层',
    '万安寺八层', '万安寺阁楼', '仙人谷', '双娇岗', '隐士洞', '华山山顶', '华山山间', '华山山脚',
    '木行阵', '土行阵', '水行阵', '金行阵', '火行阵', '九里桥', '开封郊外', '赤松林',
    # 随从副本
    '活死人墓一层', '活死人墓二层', '终南山脚', '全真教', '海边渔村', '倭寇岛',
    '华山绝顶', '武当大殿', '后山', '少室山', '少林寺后山', '无量山脚', '无量剑派',
    '桃花迷阵', '桃花居', '江南郊外', '梅庄', '梅庄水牢',
]

OUTSIDE_MAP_LIST = SAFE_MAP_LIST + WILD_MAP_LIST


VK_ENTER = 13
VK_CTRL = 17
VK_ALT = 18
VK_SHIFT = 16
VK_ESC = 27
VK_BACK = 8
VK_TAB = 9

VK_WAVE = 192
VK_A = 65
VK_C = 67
VK_N = 78
VK_D = 68
VK_Q = 81
VK_E = 69
VK_M = 77
VK_K = 75
VK_O = 79
VK_T = 84
VK_P = 80
VK_G = 71
VK_R = 82
VK_W = 87
VK_Z = 90
VK_X = 88
VK_V = 86
VK_F = 70
VK_SPACE = 32
VK_ANY = 0
VK_MOUSE_LEFT = 1
VK_MOUSE_RIGHT = 2
VK_MOUSE_MIDDLE = 4
VK_F1 = 112
VK_F2 = 113
VK_F3 = 114
VK_F4 = 115
VK_F5 = 116
VK_F6 = 117
VK_F7 = 118
VK_F8 = 119
VK_F9 = 120
VK_F10 = 121
VK_F11 = 122
VK_F12 = 123
VK_UP = 38
VK_DOWN = 40
VK_LEFT = 37
VK_RIGHT = 39


ORDER_LRUD = 0
ORDER_LRDU = 1
ORDER_RLUD = 2
ORDER_RLDU = 3

SAVE_PEOPLE = 0
SAVE_BB = 1

ACTION_NONE = 0  # 不补血补蓝
ACTION_BLOOD = 1  # 补血
ACTION_INTERNAL = 2  # 补蓝

POS_ENENMY_LIST = [
    # 后排
    (136, 360),
    (201, 317),
    (269, 276),  # boss
    (335, 234),
    (405, 193),
    # 前排
    (214, 402),
    (282, 358),
    (350, 318),
    (415, 274),
    (483, 233),
    # 后后排(闯关困难模式, 异兽)
    (55, 321),
    (121, 279),
    (189, 237),
    (256, 193),
    (323, 152),
]  # (x, y-95, x+14, y-81)

# 下面这俩都是点技能 点人宠的下面站位中心坐标
POS_MATE_LIST = [
    (632, 435),  # 0号队员 第三个人
    (563, 472),  # 1号队员 第四个人
    (700, 398),  # 2号队员 第二个人
    (500, 520),  # 3号队员 第五个人
    (769, 353),  # 4号队员 最上面人
]
POS_BB_LIST = [
    (555, 395),  # 0号B 第三个BB
    (489, 432),  # 1号B 第四个BB
    (622, 349),  # 2号B 第二个BB
    (422, 478),  # 3号B 第五个BB
    (690, 303),  # 4号B 最上面BB
]
FIGHT_PEOPLE_INFO = [  # 从上往下数: 按后面括号的组队序号排序
    (0, 617, 654, 353, 356),  # 0号队员 第三个人：下限X617 上限X654 血Y353 蓝Y356
    (1, 550, 587, 395, 398),  # 1号队员 第四个人：下限X550 上限X587 血Y395 蓝Y398
    (2, 684, 721, 311, 314),  # 2号队员 第二个人：下限X684 上限X721 血Y311 蓝Y314
    (3, 483, 520, 437, 440),  # 3号队员 第五个人：下限X483 上限X520 血Y437 蓝Y440
    (4, 751, 788, 269, 272),  # 4号队员 第一个人：下限X751 上限X788 血Y269 蓝Y272
]
FIGHT_BB_INFO = [
    (0, 537, 574, 313, 316),  # 0号队员 第三个人：下限X537 上限X574 血Y313 蓝Y316
    (1, 470, 507, 355, 358),  # 1号队员 第四个人：下限X470 上限X507 血Y355 蓝Y358
    (2, 604, 641, 271, 274),  # 2号队员 第二个人：下限X604 上限X641 血Y271 蓝Y274
    (3, 403, 440, 397, 400),  # 3号队员 第五个人：下限X403 上限X440 血Y397 蓝Y400
    (4, 671, 708, 229, 232),  # 4号队员 第一个人：下限X671 上限X708 血Y229 蓝Y232
]
# 这两个是富甲的
POS_CONFIRM = (612, 405)
X_PEOPLE_BLOOD_INTERNAL = 100
X_PEOPLE_BLOOD_INTERNAL_LOW = 74
X_PEOPLE_BLOOD_INTERNAL_HIGH = 145
X_BB_BLOOD_INTERNAL = 220
X_BB_BLOOD_INTERNAL_LOW = 196
X_BB_BLOOD_INTERNAL_HIGH = 244
Y_PEOPLE_BLOOD = 12
Y_PEOPLE_INTERNAL = 25
Y_BB_BLOOD = 10
Y_BB_INTERNAL = 21

# 世界地图上各坐标对应位置
WORLD_MAP_POS_DICT = {
    "桃花岛": (787, 375),
    "开封": (566, 337),
    "雁门关内": (492, 227),
    "光明顶": (230, 328),
    "少林": (553, 384),
    "峨嵋山": (453, 405),
    "武当山": (484, 462),
    "唐家堡": (398, 447),
    "桃花林": (776, 398),
    "桃花滩": (757, 429),
    "有座山": (643, 375),
    "绝情谷": (702, 407),
    "十字坡": (604, 305),
    "王屋山": (600, 268),
    "通吃岛": (619, 201),
    "神龙岛": (720, 207),
    "龙门": (515, 262),
    "终南山": (406, 302),
    "白驼山": (303, 231),
    "碧峰峡": (361, 382),
    "恶人谷": (304, 423),
    "无量山": (313, 473),
    "夷州岛": (755, 491),
    "雁门关外": (487, 190),
    "大草原": (431, 158),
    "古墓一层": (360, 328),
    "秦陵一层": (440, 340),
    "玄霜浅滩": (611, 426),
    "金风山道": (541, 501),
    "赤霞渡口": (577, 544),
    "青云幽谷": (674, 478),
    "燕子坞": (686, 527),
    "楼兰古道": (366, 210),
    "玉门关": (317, 174),
    "落日谷": (236, 135),
    "斡难河": (341, 103),
}

MAP_CROSS_METHOD = {
    # ------------------ 副本的 -----------------
    # 九里桥
    ("九里桥", "开封郊外"): "郊外的接送",
    ("开封郊外", "九里桥"): "桥的接送",
    ("开封郊外", "赤松林"): "跨多图-九里桥",
    ("赤松林", "开封郊外"): "跨多图-九里桥",
    ("赤松林", "九里桥"): "桥的接送",
    ("九里桥", "赤松林"): "林的接送",
    ("九里桥", "开封"): "开封传送使",
    # 万安寺
    ("万安寺大厅", "万安寺二层"): "传送点",
    ("万安寺大厅", "万安寺三层"): "大厅传送使",
    ("万安寺二层", "万安寺三层"): "传送点",
    ("万安寺二层", "万安寺大厅"): "传送点",
    ("万安寺三层", "万安寺二层"): "传送点",
    ("万安寺三层", "万安寺四层"): "传送点",
    ("万安寺四层", "万安寺三层"): "传送点",
    ("万安寺四层", "万安寺五层"): "传送点",
    ("万安寺五层", "万安寺四层"): "传送点",
    ("万安寺五层", "万安寺六层"): "传送点",
    ("万安寺六层", "万安寺五层"): "传送点",
    ("万安寺六层", "万安寺七层"): "传送点",
    ("万安寺七层", "万安寺八层"): "传送点",
    ("万安寺七层", "万安寺六层"): "传送点",
    ("万安寺八层", "万安寺七层"): "传送点",
    ("万安寺八层", "万安寺阁楼"): "传送点",
    ("万安寺阁楼", "万安寺八层"): "传送点",
    ("万安寺六层", "万安寺大厅"): "空见",
    ("万安寺七层", "万安寺大厅"): "敏君",
    ("万安寺八层", "万安寺大厅"): "苦头陀",
    ("万安寺大厅", "万安寺七层"): "大厅传送使",
    ("万安寺大厅", "万安寺八层"): "大厅传送使",
    ("万安寺阁楼", "万安寺大厅"): "跨多图-万安寺八层",
    ("万安寺二层", "开封"): "跨多图-万安寺大厅",
    ("万安寺大厅", "开封"): "传送点",
    # 梦中大侠
    ("仙人谷", "双娇岗"): "传送点",
    ("仙人谷", "隐士洞"): "山崖|悬崖|崖边",
    ("双娇岗", "仙人谷"): "传送点",
    ("隐士洞", "开封"): "跨多图-仙人谷",
    ("隐士洞", "仙人谷"): "仙人谷",
    ("仙人谷", "开封"): "开封",
    # 华山论剑
    ("华山山顶", "开封"): "开封传送使",
    ("华山山顶", "华山山间"): "传送点",
    ("华山山间", "华山山脚"): "传送点",
    ("华山山间", "华山山顶"): "传送点",
    ("华山山间", "开封"): "跨多图-华山山顶",
    ("华山山顶", "华山山脚"): "跨多图-华山山间",
    ("华山山脚", "华山山顶"): "跨多图-华山山间",
    ("华山山脚", "华山山间"): "传送点",
    ("华山山脚", "开封"): "跨多图-华山山间-华山山顶",
    # 五行阵
    ("木行阵", "土行阵"): "传送点",
    ("土行阵", "水行阵"): "传送点",
    ("水行阵", "金行阵"): "传送点",
    ("金行阵", "火行阵"): "传送点",
    ("火行阵", "金行阵"): "传送点",
    ("金行阵", "水行阵"): "传送点",
    ("水行阵", "土行阵"): "传送点",
    ("土行阵", "木行阵"): "传送点",
    ("木行阵", "碧峰峡"): "阵传送人",
    # 活死人墓
    ("活死人墓一层", "活死人墓二层"): "传送点",
    ("活死人墓二层", "活死人墓三层"): "传送点",
    ("活死人墓二层", "活死人墓一层"): "传送点",
    ("活死人墓一层", "终南山"): "传送点",
    ("活死人墓二层", "终南山"): "跨多图-活死人墓一层",
    # 智破倭寇
    ("海边渔村", "倭寇岛"): "倭寇岛传送使",
    ("倭寇岛", "海边渔村"): "送使",
    ("海边渔村", "桃花滩"): "桃花滩传送使",
    ("倭寇岛", "桃花滩"): "跨多图-海边渔村",
    # 终南破敌
    ("终南山脚", "全真教"): "送使",
    ("全真教", "终南山脚"): "送使",
    ("终南山脚", "终南山"): "传送点",
    # 决战华山
    ("华山绝顶", "龙门"): "传送点",
    # 大战武当
    ("武当大殿", "后山"): "后山传送使",
    ("后山", "武当大殿"): "传送点",
    ("武当大殿", "有座山"): "有座山传送使",
    # 武林大会
    ("少室山", "玄霜浅滩"): "浅滩传送使",
    ("少室山", "少林寺后山"): "后山传送使",
    ("少林寺后山", "少室山"): "传送点",
    ("少林寺后山", "玄霜浅滩"): "跨多图-少室山",
    # 无量剑派
    ("无量山脚", "无量剑派"): "剑派传送使",
    ("无量剑派", "无量山脚"): "传送点",
    ("无量山脚", "无量山"): "传送点",
    ("无量剑派", "无量山"): "跨多图-无量山脚",
    # 桃花招亲
    ("桃花迷阵", "桃花居"): "传送点",
    ("桃花居", "桃花迷阵"): "传送点",
    ("桃花迷阵", "桃花滩"): "传送点",
    ("桃花居", "桃花滩"): "跨多图-桃花迷阵",
    # 梅庄之战
    ("江南郊外", "梅庄"): "传送点",
    ("梅庄", "江南郊外"): "江南郊外传送使",
    ("梅庄", "梅庄水牢"): "水牢传送使",
    ("梅庄水牢", "梅庄"): "传送点",
    ("江南郊外", "金风山道"): "山道传送使",
    ("梅庄水牢", "金风山道"): "跨多图-梅庄-江南郊外",
    # ------------------ 现实的 ------------------
    ("古墓一层", "古墓二层"): "传送点",
    ("古墓二层", "古墓三层"): "传送点",
    ("秦陵一层", "秦陵二层"): "传送点",
    ("秦陵一层", "古墓三层"): "传送点",
    ("古墓三层", "古墓二层"): "传送点",
    ("赤霞渡口", "青云幽谷"): "传送点",
    ("燕子坞", "青云幽谷"): "传送点",
    ("金风山道", "玄霜浅滩"): "传送点",
    ("开封", "武林盟"): "武林盟传送使",
    ("开封", "帮会领地"): "帮会领地管理员",
    ("开封", "关卡人"): "闯关传送使",   # 我要闯关
    ("关卡人", "关卡地"): "闯关管理员",  # 进入地关
    ("关卡地", "关卡天"): "闯关管理员",  # 进入天关
    ("关卡天", "关卡侠"): "闯关管理员",  # 进入侠关
}

# 大地图上的穿梭点 第一个点是跑在点上面的大地图坐标，第二个点是点实际场景中的坐标点
BIG_MAP_POS_DICT = {
    # ------------------ 副本的 -----------------
    # 万安寺
    ("万安寺大厅", "万安寺二层"): [(792, 96), (570, 218)],
    ("万安寺二层", "万安寺大厅"): [(863, 131), (401, 419)],
    ("万安寺二层", "万安寺三层"): [(796, 84), (554, 283)],
    ("万安寺三层", "万安寺二层"): [(857, 127), (458, 461)],
    ("万安寺三层", "万安寺四层"): [(812, 90), (415, 290)],
    ("万安寺四层", "万安寺三层"): [(857, 127), (458, 461)],
    ("万安寺四层", "万安寺五层"): [(812, 90), (415, 290)],
    ("万安寺五层", "万安寺四层"): [(857, 127), (458, 461)],
    ("万安寺五层", "万安寺六层"): [(796, 84), (550, 286)],
    ("万安寺六层", "万安寺五层"): [(857, 127), (458, 461)],
    ("万安寺六层", "万安寺七层"): [(792, 96), (570, 218)],
    ("万安寺七层", "万安寺六层"): [(857, 127), (458, 461)],
    ("万安寺八层", "万安寺七层"): [(857, 127), (458, 461)],
    ("万安寺七层", "万安寺八层"): [(792, 96), (570, 218)],
    ("万安寺八层", "万安寺阁楼"): [(792, 96), (570, 218)],
    ("万安寺阁楼", "万安寺八层"): [(859, 129), (473, 448)],
    ("万安寺大厅", "开封"): [(776, 130), (266, 492)],
    # 梦中大侠
    ("仙人谷", "双娇岗"): [(847, 442), (631, 495)],
    ("双娇岗", "仙人谷"): [(666, 247), (155, 524)],
    # 华山论剑
    ("华山山顶", "华山山间"): [(750, 271), (369, 297)],
    ("华山山间", "华山山顶"): [(809, 142), (714, 391)],
    ("华山山间", "华山山脚"): [(536, 285), (265, 377)],
    ("华山山脚", "华山山间"): [(587, 108), (263, 292)],
    # 五行阵
    ("木行阵", "土行阵"): [(827, 326), (589, 520)],
    ("土行阵", "水行阵"): [(831, 307), (564, 492)],
    ("水行阵", "金行阵"): [(845, 352), (700, 491)],
    ("金行阵", "火行阵"): [(789, 330), (500, 447)],
    ("火行阵", "金行阵"): [(853, 186), (583, 173)],
    ("金行阵", "水行阵"): [(618, 90), (502, 154)],
    ("水行阵", "土行阵"): [(762, 104), (617, 223)],
    ("土行阵", "木行阵"): [(612, 188), (284, 139)],
    # 活死人墓
    ("活死人墓一层", "活死人墓二层"): [(817, 177), (444, 343)],
    ("活死人墓二层", "活死人墓三层"): [(793, 98), (381, 376)],
    ("活死人墓二层", "活死人墓一层"): [(436, 426), (482, 336)],
    ("活死人墓一层", "终南山"): [(526, 287), (507, 298)],
    # 智破倭寇
    # 终南破敌
    ("终南山脚", "终南山"): [(849, 298), (698, 573)],
    # 决战华山
    ("华山绝顶", "龙门"): [(767, 338), (660, 560)],
    # 大战武当
    ("后山", "武当大殿"): [(859, 355), (640, 487)],
    # 武林大会
    ("少林寺后山", "少室山"): [(666, 247), (155, 524)],
    # 无量剑派
    ("无量剑派", "无量山脚"): [(618, 90), (502, 154)],
    ("无量山脚", "无量山"): [(536, 285), (265, 377)],
    # 桃花招亲
    ("桃花迷阵", "桃花居"): [(539, 269), (244, 349)],
    ("桃花居", "桃花迷阵"): [(789, 268), (640, 539)],
    ("桃花迷阵", "桃花滩"): [(773, 352), (617, 276)],
    # 梅庄之战
    ("江南郊外", "梅庄"): [(587, 108), (263, 292)],
    ("梅庄水牢", "梅庄"): [(618, 90), (502, 154)],
    # ------------------ 梦境 ------------------
    ("梦境开封", ""): [(175, 416), (311, 406)],
    ("梦境桃园", ""): [(796, 275), (578, 538)],
    ("梦境雁门关", ""): [(838, 91), (667, 161)],
    ("梦境桃花林", ""): [(530, 261), (339, 434)],
    ("梦境桃花滩", ""): [(770, 467), (602, 454)],
    ("梦境有座山", ""): [(818, 215), (640, 231)],
    ("梦境绝情谷", ""): [(514, 173), (227, 348)],
    ("梦境十字坡", ""): [(666, 247), (155, 524)],
    ("梦境王屋山", ""): [(426, 346), (670, 530)],
    ("梦境通吃岛", ""): [(816, 308), (620, 401)],
    ("梦境神龙岛", ""): [(787, 129), (728, 340)],
    ("梦境龙门", ""): [(591, 252), (160, 422)],
    ("梦境终南山", ""): [(571, 93), (152, 331)],
    ("梦境白驼山", ""): [(770, 341), (610, 537)],
    ("梦境碧峰峡", ""): [(511, 323), (174, 391)],
    ("梦境恶人谷", ""): [(851, 153), (655, 141)],
    ("梦境无量山", ""): [(536, 285), (265, 377)],
    ("梦境夷州岛", ""): [(660, 291), (212, 274)],
    ("梦境雁门关外", ""): [(646, 321), (316, 337)],
    ("梦境大草原", ""): [(498, 413), (373, 357)],
    ("梦境玄霜浅滩", ""): [(761, 78), (364, 119)],
    ("梦境金风山道", ""): [(533, 104), (259, 276)],
    ("梦境赤霞渡口", ""): [(578, 293), (258, 385)],
    ("梦境青云幽谷", ""): [(821, 278), (589, 229)],
    ("梦境燕子坞", ""): [(571, 281), (180, 327)],
    ("梦境楼兰古道", ""): [(626, 109), (207, 235)],
    ("梦境玉门关", ""): [(821, 293), (620, 476)],
    # ------------------ 现实的 ------------------
    ("古墓一层", "古墓二层"): [(824, 189), (323, 226)],
    ("古墓二层", "古墓三层"): [(793, 98), (381, 376)],
    ("秦陵一层", "秦陵二层"): [(741, 164), (486, 221)],
    ("秦陵一层", "古墓三层"): [(279, 375), (254, 445)],
    ("古墓三层", "古墓二层"): [(459, 396), (343, 268)],
    ("帮会领地", "开封"): [(576, 207), (270, 405)],
    ("矿洞", ""): [(620, 220), (447, 416)],
    ("赤霞渡口", "青云幽谷"): [(559, 300), (409, 326)],
    ("燕子坞", "青云幽谷"): [(565, 278), (195, 362)],
    ("金风山道", "玄霜浅滩"): [(834, 366), (591, 431)]
}

NAME_POS_DICT = {
    # 开封
    "银湘玉": (506, 233),
    "胡大夫": (492, 299),
    "桑蝶儿": (595, 200),
    "宋双双": (399, 313),
    "门派使者": (447, 294),
    "江湖万事通": (556, 269),
    "开封商人": (446, 268),
    "帮会领地管理员": (711, 189),
    "钦差大臣": (519, 248),
    "侠义盟使者": (510, 162),
    "节日使者": (431, 334),
    "曹都尉": (200, 274),
    "程小弟": (619, 217),
    "开封随从教员": (635, 164),
    "金鹰": (658, 371),
    "芳芳": (422, 275),
    "凤天情": (639, 340),
	"圣诞使者": (487, 262),	
	"圣诞树": (487, 262),
	"岁岁欢": (486, 160),
	"年兽": (290, 203),
	"无敌小乞丐": (365, 337),
	"礼品发放使": (422, 351),
	"明教接引使者": (479, 302),
	"冯知府": (546, 125),
	"武举考官": (444, 150),
    # 跨服线-屠龙战场
    "龙公子": (570, 254),
    # 野外NPC
    "独孤猴": (644, 267),
	# 桃花岛
	"黄药师": (672, 91),
}

default_biz_cfg_plan = {
    # 人物战斗
    "人物战斗救人": False,
    "人物战斗救人比例": 10,
    "人物战斗救BB": False,
    "人物战斗救BB比例": 10,
    "人物使用技能": False,
    "人物技能使用频率": 99,
    "人物护心百分比": 60,
    "人物未命中继续刺": False,
    "人物刺怪比例": 50,
    "人物刺穴F7": False,
    "人物护心F6": True,
    "人物五行克制F1": False,
    "人物战斗": "攻",
    "人物战后补充": True,
    "人物战后补充比例": 80,
    "唤出BB": False,
    "唤出BB血内比例": 50,
    "唤出BB回合数": 20,
    "唤出BB名字": "白龙",
    "人物切回首发BB": False,
    # BB战斗
    "进攻类BB战斗救人": False,
    "进攻类BB战斗救人比例": 20,
    "进攻类BB战斗救BB": False,
    "进攻类BB战斗救BB比例": 20,
    "防守类BB战斗救人": True,
    "防守类BB战斗救人比例": 20,
    "防守类BB战斗救BB": False,
    "防守类BB战斗救BB比例": 20,
    "BB使用技能": True,
    "BB厚积": True,
    "BB激将": True,
    "BB破釜": True,
    "BB乱神隔世": False,
    "BB金钟罩": False,
    "BB铁布衫": False,
    "BB破釜回合数": 2,
    # 战斗通用配置
    "人物救人宠前提": True,
    "人物拉药怪数": 4,
    "BB救人宠前提": False,
    "BB拉药怪数": 4,
    # 其它
    "回城": "开封票",
    "出城": "跑步",
    "执行列表": [],
    "执行列表选中": [],
    "BB攻防": "自动判断",
    "人宠使用技能": "强制使用",
    "查找灰色行囊页": False,
    "点击整理": False,
    "换副本前修理忠诚": True,
    "修理忠诚方式": "开封NPC",
	"带队换线": False,
	"领双带队打牛": False,
	"领双时间带队打牛": "2小时",
	"领双围捕大盗": False,
	"领双时间围捕大盗": "1小时",
	"领双边关清剿": False,
	"领双时间边关清剿": "1小时",
	"领双百战百胜": False,
	"领双时间百战百胜": "4小时",
	"组满第五页": False,
    # 还有很多业务任务的在导入任务类时会自动注册进来
}

PIC_BANG_HUI = """\
药材_兰香草.bmp|杂货_玉石骰.bmp|杂货_神龙丹.bmp|药材_半春莲.bmp|杂货_某某剑法残谱.bmp|
药材_大血藤.bmp|药材_青黛.bmp|杂货_小龙女的画像.bmp|药材_熊胆.bmp|杂货_白驼山壮骨粉.bmp|\
药材_蛇胆.bmp|药材_曼陀罗.bmp|杂货_破碎的兽皮.bmp|药材_络石纹.bmp|杂货_犬牙.bmp|\
药材_铁兰花.bmp|药材_红花.bmp|杂货_猴儿酒.bmp|药材_孔雀胆.bmp|杂货_马奶酒.bmp|\
杂货_用旧的大马士革刀.bmp|药材_紫珠.bmp|杂货_眼罩.bmp|杂货_相思玉.bmp|药材_香附子.bmp|\
药材_燕窝.bmp|杂货_古墓散卷.bmp|杂货_破烂的头盔.bmp|杂货_破包裹.bmp|杂货_烂背篓.bmp|\
杂货_花岗岩.bmp|杂货_狗骨头.bmp|杂货_赭石.bmp|杂货_没把的锄头.bmp|基石.bmp\
"""

PIC_EXPANSIVE = """\
存_电魄刃石.bmp|存_电魄履石.bmp|存_电魄束石.bmp|存_电魄腕石.bmp|存_电魄甲石.bmp|存_电魄冠石.bmp|
存_雷胆刃石.bmp|存_雷胆履石.bmp|存_雷胆束石.bmp|存_雷胆腕石.bmp|存_雷胆甲石.bmp|存_雷胆冠石.bmp|
存_惊云刃石.bmp|存_惊云履石.bmp|存_惊云束石.bmp|存_惊云腕石.bmp|存_惊云甲石.bmp|存_惊云冠石.bmp|
存_天霜刃石.bmp|存_天霜履石.bmp|存_天霜束石.bmp|存_天霜腕石.bmp|存_天霜甲石.bmp|存_天霜冠石.bmp|
存_无极刃石.bmp|存_无极履石.bmp|存_无极束石.bmp|存_无极腕石.bmp|存_无极甲石.bmp|存_无极冠石.bmp|
存_浩然石.bmp|存_招式残卷.bmp|存_内功残卷.bmp|存_绝艺残卷.bmp|存_天书碎片.bmp|
存_一级金刚石.bmp|存_二级金刚石.bmp|存_三级金刚石.bmp|存_一级聚魂石.bmp|存_二级聚魂石.bmp|存_三级聚魂石.bmp|
存_武器碎片70魂.bmp|存_武器碎片70阵.bmp|存_武器碎片70雨.bmp|存_武器碎片70星.bmp|存_武器碎片70雨2.bmp|
存_武器碎片80魂.bmp|存_武器碎片80阵.bmp|存_武器碎片80雨.bmp|存_武器碎片80星.bmp|
存_武器碎片90魂.bmp|存_武器碎片90阵.bmp|存_武器碎片90雨.bmp|存_武器碎片90星.bmp|
存_武器碎片100魂.bmp|存_武器碎片100阵.bmp|存_武器碎片100雨.bmp|存_武器碎片100星.bmp|
存_武器碎片110魂.bmp|存_武器碎片110阵.bmp|存_武器碎片110雨.bmp|存_武器碎片110星.bmp|
存_神兵碎片1.bmp|存_神兵碎片2.bmp|存_神兵碎片3.bmp|存_神兵碎片4.bmp|存_神兵碎片5.bmp|存_神兵碎片6.bmp|存_神兵碎片7.bmp|存_神兵碎片8.bmp|存_神兵碎片9.bmp|
药引1.bmp|药引2.bmp|药引3.bmp|存_堂主挑战令.bmp|康乃馨.bmp|存_天山雪莲.bmp|存_血玉菩提.bmp|存_挑战密令.bmp|存_挑战信物.bmp|
存_玄电宝盒.bmp|存_幸运宝盒.bmp|存_富甲天下宝盒.bmp|存_幽月宝盒.bmp|
副本装备_男冠.bmp|副本装备_女冠.bmp|副本装备_男衣.bmp|副本装备_女衣.bmp|副本装备_男护手.bmp|副本装备_女护手.bmp|
副本装备_男鞋.bmp|副本装备_女鞋.bmp|副本装备_男腰带.bmp|副本装备_女腰带.bmp|
发光石_护腕.bmp|发光石_靴子.bmp|发光石_项链.bmp|发光石_头盔.bmp|发光石_武器.bmp|发光石_玉佩.bmp|发光石_腰带.bmp|发光石_衣服.bmp|
发光石碎片_护腕.bmp|发光石碎片_靴子.bmp|发光石碎片_项链.bmp|发光石碎片_头盔.bmp|发光石碎片_武器.bmp|发光石碎片_玉佩.bmp|发光石碎片_腰带.bmp|发光石碎片_衣服.bmp
"""

PIC_JINTI = """\
晶体石_泰然.bmp|晶体石_重望.bmp|晶体石_仁慈.bmp|晶体石_凶暴.bmp|晶体石_致命.bmp|晶体石_坚韧.bmp|晶体石_命抗.bmp|晶体石_三元.bmp|晶体石_身法.bmp|晶体石_伤.bmp|晶体石_防.bmp|
"""

PIC_EQUIP = """\
装备_炎龙铠男.bmp|装备_炎龙铠女.bmp|装备_紫弘衫男.bmp|装备_紫弘衫女.bmp|装备_紫薇衣男.bmp|装备_紫薇衣女.bmp|
装备_摇影衣男.bmp|装备_摇影衣女.bmp|装备_冥云衣男.bmp|装备_冥云衣女.bmp|装备_紫弘铠男.bmp|装备_紫弘铠女.bmp|
装备_蚩尤衫男.bmp|装备_蚩尤衫女.bmp|装备_蚩尤铠男.bmp|装备_蚩尤铠女.bmp|装备_勾陈衣女.bmp|
装备_达摩杖.bmp|装备_灭魔杖.bmp|装备_凌波手.bmp|装备_修罗手.bmp|装备_紫电手.bmp|装备_赤焰刀.bmp|装备_破狱刀.bmp|
装备_天风剑.bmp|装备_擎天剑.bmp|装备_断肠剑.bmp|装备_焦尾.bmp|装备_冰清.bmp|装备_绿椅.bmp|
装备_断云镖.bmp|装备_凝冰针.bmp
"""


LINE_KONG_SERVER = {
    '神雕侠侣': ['*************', 'http://ytsl.linekong.cn/line/YTVolumes123J1'],
    '笑傲江湖': ['*************', 'http://ytsl.linekong.cn/line/YTVolumes123X1'],
    '华山论剑': ['*************', 'http://ytsl.linekong.cn/line/YTVolumes123X2'],
    '龙凤呈祥': ['101.42.119.103', 'http://ytsl.linekong.cn/line/YTVolumes123S7'],
    '威震四方': ['101.42.117.218', 'http://ytsl.linekong.cn/line/YTVolumes123S2'],
    '天下无双': ['101.42.117.78', 'http://ytsl.linekong.cn/line/YTVolumes123S16'],
    '风陵渡口': ['82.157.26.214', 'http://ytsl.linekong.cn/line/YTVolumes123S19'],
    '不忘初心': ['82.157.27.48', 'http://ytsl.linekong.cn/line/YTVolumes123S20'],
    '玉女心经': ['*************', 'http://ytsl.linekong.cn/line/YTVolumes123S21'],
    '书剑恩仇': ['*************', 'http://ytsl.linekong.cn/line/YTVolumes123S22'],
    '随心所欲': ['************', 'http://ytsl.linekong.cn/line/YTVolumes123S23'],
}

CMD_LINE = "  -Xss2m -Xms128m -Xmx256m -Dfile.encoding=GB2312 -verbose:gc -cp . senv.xload.XLoad applib htp seasky.zar:hero.zar hero.fore.Start seaskyWin=true startAt=hero1.zar fps=true dcAddress={da} volumesAddress={va}"
