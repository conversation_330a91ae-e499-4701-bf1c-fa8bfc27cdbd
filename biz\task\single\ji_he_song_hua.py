from biz.constants.constants import *
from biz.task.__base import TaskBase
from utils import *
from biz.obj.worker import Worker


class TaskSongHua(TaskBase):
    TASK_NAME = "吉禾送花"
    FLOWER_PIC = "康乃馨.bmp"

    @classmethod
    def run(cls, wk: Worker):
        if not cls.bag_have_item(wk, cls.FLOWER_PIC):
            cls.close_pages(wk)
            cls.buy_flower(wk)
        cls.give_flower(wk)
            
    @classmethod
    def give_flower(cls, wk: Worker):
        receiver_name = cls.get_flower_receiver_name(wk)
        if not receiver_name:
            return
        def use_flower(wk: Worker, item_bmp: str, RECT: tuple):
            for i in range(5):
                if not wk.find_pic_r_click(*RECT, item_bmp, timeout=300):
                    return False
                if not wk.find_pic_offset_click(*RECT_FULL, "送给.bmp", dx=100, dy=8, timeout=400):
                    popup_info = cls.get_popup_info(wk, timeout=400)
                    wk.record(f"五次送花过程发生中断, 原因:{popup_info}")
                    break
                wk.send_string(receiver_name)
                msleep(600)
                cls.click_confirm(wk)
                msleep(400)
            wk.record("送花完成")
            return True
        cls.bag_item_action(wk, cls.FLOWER_PIC, use_flower)
        cls.close_pages(wk)

    @classmethod
    def buy_flower(cls, wk: Worker):
        wk.record("背包里没有康乃馨, 正在前往寄售购买...")
        cls.ji_shou_buy_thing(wk, "康乃馨", 5)
        
        
    @classmethod
    def get_flower_receiver_name(cls, wk: Worker) -> str:
        receiver_row = wk.row + 1
        if receiver_row % 5 == 0:
            receiver_row -= 5
        player_name = settings.wnd_main.tbe_console.item(receiver_row, COL_NAME).text()
        if not player_name:
            wk.record(f"表格第{receiver_row}行没有名字, 无法送花")
        return player_name