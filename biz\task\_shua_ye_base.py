from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker

class TaskShuaYeBase(TaskBase):
    NEED_AVOID_FIGHT = False
    
    @classmethod
    def before_run(cls, wk: Worker):
        cls.close_pages(wk)
        if wk.is_fight:  
            TaskBase.fight_operation(wk)
        super().before_run(wk)  # 避免这里出现战后包满的情况, 前面处理战斗
        # 务必保证先识别出shuaye_map
        if wk.cfg_plan_task["刷野地图"] == "当前地图":
            wk.shuaye_map = cls.cur_map(wk)
        else:
            wk.shuaye_map = wk.cfg_plan_task["刷野地图"]
        wk.record(f"刷野地图:{wk.shuaye_map}")
        
    @classmethod
    def get_enemy_color_list(cls, wk: Worker):
        return [COLOR_GREEN, COLOR_GOLD, COLOR_RED]

    @classmethod
    def is_reach_time(cls, wk: Worker, start_ts: int, total_seconds: int):
        if settings.cur_time_stamp - start_ts > total_seconds:
            wk.record("超出设定时长，自动结束")
            return True
        if wk.cfg_plan_task["定时结束"]:
            if wk.cur_task_start_time_fmt > wk.cfg_plan_task["定时结束时间"]:
                wk.record("任务开始时间超过定时结束时间, 忽略定时结束")
                wk.cfg_plan_task["定时结束"] = False
                return False
            if settings.cur_time_fmt[:5] >= wk.cfg_plan_task["定时结束时间"]:
                wk.record("定时结束时间到")
                return True
        return False

    @classmethod
    def click_run_away(cls, wk: Worker):
        return wk.find_pic_click(*RECT_RUN_AWAY, "撤退.bmp|撤退2.bmp")
    
    @classmethod
    def fight_close_talk(cls, wk: Worker):
        if wk.cur_round <= 2:  # 燕南天 令狐冲
            cls.close_other_talk(wk)