from utils import *
from biz.task.__base import TaskBase
from biz.constants.constants import *
from biz.obj.worker import Worker

class TaskZhengZhan(TaskBase):
	TASK_NAME = "战魂竞技"

	@classmethod
	def before_run(cls, wk: Worker):
		# 重置任务类型和完成次数
		cls.set_task_config(wk)
		wk.done_count = 0

	@classmethod
	def run(cls, wk: Worker):
		cls.open_shang_zhjj_page(wk)
		if wk.find_str(*RECT_FULL, "尚未开启", COLOR_ORANGE, zk=ZK_ALL_9):
			wk.record("活动尚未开启")
			return
		if wk.find_pic_click(*RECT_FULL, "战魂奖励.bmp"):
			wk.record("领取战魂奖励")
		for _ in range(40):
			if cls.is_popup_show_info(wk, "挑战次数已经使用完"):
				cls.click_confirm(wk)
				wk.record("挑战次数已用完，退出")
				break
			else:
				cls.click_confirm(wk)
			idx_pos_list = wk.find_pic_ex(*RECT_FULL, "竞技挑战.bmp")
			if idx_pos_list:
				rnd_idx = rnd(0, len(idx_pos_list)-1)
				_, x, y = idx_pos_list[rnd_idx]
				wk.record(f"随机点击第{rnd_idx+1}个进行挑战")
				wk.move_click(x, y)
			msleep(600)
			if wk.find_pic_click(*RECT_FULL, "结果.bmp", timeout=600):
				msleep(600)
				cls.click_close_pic(wk)
		msleep(500)
		cls.close_pages(wk)
		msleep(500)
		cls.click_close_pic(wk, timeout=400)
		cls.use_thing(wk)

	@classmethod
	def open_shang_zhjj_page(cls, wk: Worker):
		if wk.find_pic(*RECT_FULL, "结果.bmp"):
			wk.record("已在竞技界面")
			return
		wk.record("正在打开竞技界面...")
		cls.close_pages(wk)
		cls.open_activity_page(wk)
		if not cls.is_activity_page_open(wk):
			wk.record("打开活动界面失败")
			return
		wk.move_click(*POS_ZHJJ, re_move=False)
		msleep(600)
		wk.move_click(*POS_LIJICANJIA)
		msleep(1000)

	@classmethod
	def use_thing(cls, wk: Worker):
		wk.record(f'正在使用 随从名望袋 和 新翠宝盒...')
		cls.bag_use_item(wk, "随从名望袋.bmp")
		cls.bag_use_item_all(wk, "新翠宝盒.bmp", count=3)
