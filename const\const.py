import os
from version import version

# 软件更新相关
CLIENT_VERSION = version
APP_NAME = "器灵"

# dll名称
DLL_NAME_DM = "xxdm.dll"
DLL_NAME_REGDM = "xxDmReg.dll"
DLL_NAME_LW = "lw.dll"
DLL_NAME_OP = "op_x86.dll"
DLL_NAME_REGOP = "tools.dll"

DIR_WORK = os.getcwd()
DIR_RES = os.path.join(DIR_WORK, "biz", "res")
DIR_DLL = os.path.join(DIR_WORK, "dll")

# TBE_COSOLE
TBE_CONSOLE_ROW = 60
TBE_CONSOLE_COL = 10
COL_HWND = 0
COL_NAME = 1
COL_PLAN = 2
COL_RUN = 3
COL_PAUSE = 4
COL_END = 5
COL_LOG = 6
COL_ACCOUNT = 7
COL_PASSWORD = 8
COL_SERVER = 9
SELECTED = "√"
HIDE_X = 9000

# TBE_PRICE
TBE_PRICE_COL = 4
COL_GOODS_NAME = 0
COL_GOODS_PRICE = 1
COL_GOODS_ONSHELF = 2
COL_GOODS_COMMENT = 3

SLEEP_TIME = 100  # 等待时间ms, 越大执行速度越慢, 占用CPU越少
SLEEP_AFTER_CLICK = 100  # 点击后的等待时间ms

PATH_DEV_CONFIG = "E:/code/yt"
PATH_SOFTWARE = PATH_DEV_CONFIG if os.path.exists(
    PATH_DEV_CONFIG + "/qiling.spec") else os.getcwd()
print(f"软件目录: {PATH_SOFTWARE}")
PATH_SOFTWARE_CONFIG = os.path.join(PATH_SOFTWARE, "config")  # 软件配置文件目录
PATH_SOFTWARE_LOG = os.path.join(PATH_SOFTWARE, "logs")  # 软件日志文件
PATH_SOFTWARE_PLAN = os.path.join(PATH_SOFTWARE_CONFIG, "plans.json")  # 不同方案的配置
PATH_SOFTWARE_COMMON = os.path.join(PATH_SOFTWARE_CONFIG, "common.json")  # 软件的通用配置


# ============================== 插件相关 ===========================
# 字库和图片密码
PWD_PIC = "2.71828"
PWD_ZK = "2.71828"
# 绑定模式
MODE_DISPLAY = "dx2"
MODE_MOUSE = "windows3"
MODE_KEYPAD = "windows"
MODE_BACK = 101
MODE_PUBLIC = ""
# 窗口状态
WND_STATE_NORMAL = 0
WND_STATE_ACTIVE = 1
WND_STATE_VISIBLE = 2
WND_STATE_MIN = 3
WND_STATE_MAX = 4
WND_STATE_TOP = 5
WND_STATE_NORESP = 6
