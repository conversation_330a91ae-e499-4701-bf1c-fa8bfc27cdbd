from datetime import datetime
from biz.constants.constants import *
from biz.task._jrw_xl_dg import TaskJrwXlDg
from biz.obj.worker import Worker
import settings
from utils.utils import get_week_day, msleep


class TaskBaiZhan(TaskJrwXlDg):
	TASK_NAME = "百战百胜"
	IS_ACTIVITY = True

	@classmethod
	def get_task_name(cls) -> str:
		return "百战百胜"

	@classmethod
	def get_task_publisher_name(cls) -> str:
		return "百败居士"
	
	@classmethod
	def check_the_week(cls, wk: Worker):
		weekday = get_week_day()
		if weekday != 7:
			wk.record(f"百战只能在星期天, 今天是星期{weekday}")
			return False
		return True

	@classmethod
	def get_task_setting_count(cls, wk: Worker) -> int:
		return wk.cfg_plan_task["次数"]

	@classmethod
	def end_condition(cls, wk: Worker) -> bool:
		if datetime.now().weekday() != 6:  # 今天不是周日直接结束
			return False
		if settings.cur_time_fmt > "17:00":
			return False
		if wk.cfg_plan["领双百战百胜"] and wk.check_ls:
			wk.check_ls = False
			db_time = wk.cfg_plan["领双时间百战百胜"]
			wk.record(f"打牛前领双:{db_time}")
			cls.get_double(wk, db_time)
		return True

	@classmethod
	def talk_recv_task(cls, wk: Worker) -> bool:
		if wk.find_str_click(*RECT_TALK, "挑战", COLOR_TALK_ITEM):
			cls.talk_click_specify_item(wk, "帮我联系一下", timeout=400)
			cls.talk_click_space(wk, timeout=600)
			cls.talk_click_specify_item(wk, "确定", timeout=0) # 环数不一致确定
			return True
		return False

	@classmethod
	def is_cur_task_desc(cls, wk: Worker):
		if cls.region_task_desc_ocr(wk) == '':
			wk.record("任务描述被遮挡")
			return True
		return cls.region_task_desc_find_str(wk, "要多加小心|拜访")

	@classmethod
	def change_to_origin_task(cls, wk: Worker):
		if cls.region_task_list_find_str_click(wk, "缉拿"):
			wk.record("已切回 原任务")
			msleep(400)
			return True
		return False

	@classmethod
	def find_way_fight(cls, wk: Worker):
		try:
			super().find_way_fight(wk)
		except:  # 只会有 任务已进入终态的异常
			cls.refresh_task_list(wk)

	@classmethod
	def wait_for_fight(cls, wk: Worker):
		if wk.find_str(*RECT_MAP_NAME, "开封", COLOR_WHITE):
			# 额外任务 不需要战斗
			return
		for _ in range(20):
			if wk.is_fight:
				return
			msleep(400)
		wk.record("等待进入战斗超时...")

	@classmethod
	def get_enemy_talk_item(cls):
		return "抓的就是你|送信函"

	@classmethod
	def task_npc_find_way(cls, wk: Worker, specify_item="", color=COLOR_TALK_ITEM, npc_name="", npc_map=""):
		color = color + "|" + COLOR_TALK_ITEM_TASK
		return super().task_npc_find_way(wk, specify_item, color, npc_name, npc_map)

	@classmethod
	def enable_task_fix_equip_bb(cls, wk: Worker):
		return wk.cfg_plan_task["修理忠诚"]

	@classmethod
	def get_default_biz_config(cls):
		return {
			"次数": 800,
			"修理忠诚": False,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.spin_count_bai_zhan.setValue(cls.CONFIG["次数"])
		settings.wnd_main.chk_bai_zhan_fix_bb.setChecked(cls.CONFIG["修理忠诚"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["次数"] = int(settings.wnd_main.spin_count_bai_zhan.value())
		cls.CONFIG["修理忠诚"] = settings.wnd_main.chk_bai_zhan_fix_bb.isChecked()
		super().cfg_save(plan_name)
