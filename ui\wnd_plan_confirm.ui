<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WndPlanConfirm</class>
 <widget class="QDialog" name="WndPlanConfirm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>201</width>
    <height>102</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>提示</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>将当前配置保存到方案</string>
     </property>
     <property name="margin">
      <number>2</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QComboBox" name="cmb_plan_list">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">/* 下拉列表的容器样式 */
/* 下拉列表的容器样式 */
            QComboBox QAbstractItemView {
                font-size: 10px;           /* 字体大小 */
                show-decoration-selected: 1; /* 选中项高亮 */
            }

            /* 下拉列表的每一项 */
            QComboBox QAbstractItemView::item {
                height: 11px;              /* 每项高度 */
                margin: 0px;               /* 移除默认外边距 */
                padding: 0px;              /* 项内边距 */
                border: none;
            }</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btn_confirm">
       <property name="minimumSize">
        <size>
         <width>60</width>
         <height>22</height>
        </size>
       </property>
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btn_cancel">
       <property name="minimumSize">
        <size>
         <width>60</width>
         <height>22</height>
        </size>
       </property>
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
