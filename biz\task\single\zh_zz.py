from utils import *
from biz.constants.constants import *
from biz.task.__base import TaskBase
from biz.obj.worker import Worker


class TaskZhengZhan(TaskBase):
    TASK_NAME = "征战江湖"
    NEED_PLAYER_NAME = False

    @classmethod
    def run(cls, wk: Worker):
        if not wk.find_pic(*RECT_FULL, "结果.bmp"):
            wk.record(f"请进到征战战斗页面后按鼠标右键启动...")
            wk.wait_key(VK_MOUSE_RIGHT)
        wk.record("开始征战...")
        
        for i in range(10000):
            if cls.click_confirm(wk, timeout=0):
                msleep(1000)
                cls.click_close_pic(wk)
                cls.close_pages(wk)
                break
            if wk.cfg_plan_task["偏好"] == "下一关":
                if wk.find_pic(
                    *RECT_FULL, "继续.bmp|继续2.bmp"
                ) and not wk.find_pic_click(*RECT_FULL, "下一关.bmp"):
                    if not wk.find_str(*RECT_POPUP, "战胜", COLOR_BLACK):
                        wk.record("征战失败了，暂停挑战，避免浪费次数")
                        wk.pause()
                    wk.record("没有找到下一关...")
                wk.record("点击 下一关")
            else:
                if wk.find_pic_click(*RECT_FULL, "继续.bmp|继续2.bmp"):
                    wk.record("点击 继续")
            wk.find_pic_click(*RECT_RIGHT, "结果.bmp", timeout=300)
            msleep(500)
        cls.click_close_pic(wk, timeout=400)

    @classmethod
    def after_run(cls, wk: Worker):
        msleep(800)
        cls.click_close_pic(wk, timeout=400)
        msleep(800)
        cls.close_pages(wk)
        super().after_run(wk)

    @classmethod
    def check_fight_condition(cls, wk: Worker):
        return super().check_fight_condition(wk) and not wk.find_pic(*RECT_RIGHT, "结果.bmp")

    @classmethod
    def is_zheng_zhan_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "界_征战.bmp", timeout=timeout)

    @classmethod
    def get_default_biz_config(cls):
        return {"偏好": "下一关"}

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_zheng_zhan, cls.CONFIG["偏好"]
        )

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["偏好"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_zheng_zhan
        )
        super().cfg_save(plan_name)


