import base64
import hashlib
import hmac
import time
import win32event
import win32api
import winerror
from Crypto.Cipher import DES
import psutil


def encrypt(plaintext):
    if not plaintext:
        return ""
    def _pad(text):
        # 填充字符使长度为8的倍数
        padding = 8 - len(text) % 8
        return text + chr(padding) * padding
    key = b'you4dsxb'
    # 使用ECB模式（没有iv），填充方式为PKCS5
    cipher = DES.new(key, DES.MODE_ECB)
    padded_text = _pad(plaintext)
    # 加密后以byte格式输出
    cipher_text = cipher.encrypt(padded_text.encode())
    return base64.b64encode(cipher_text).decode('utf-8')

def decrypt(cipher_text):
    def _unpad(text):
        # 去除填充字符
        padding = text[-1]
        return text[:-padding]
    try:
        key = b'you4dsxb'
        cipher = DES.new(key, DES.MODE_ECB)
        decrypted_text = cipher.decrypt(base64.b64decode(cipher_text))
        plaintext = _unpad(decrypted_text).decode()
        return plaintext
    except:
        return ""

# 检测多开, 检测到返回真
def detect_multi_run():
    global hMutex
    hMutex = win32event.CreateMutex(None, False, "QL_DETT_MR")  # 若存在, 会使该互斥体引用量加1
    if win32api.GetLastError() == winerror.ERROR_ALREADY_EXISTS:
        win32api.CloseHandle(hMutex)  # 使该互斥体引用量减1
        return True  # 返回真代表多开了
    return False


def get_check_sum(key, message):
    key = bytes(key, 'utf-8')
    message = bytes(message, 'utf-8')
    # print(f"key: {key}, value: {message}")
    return hmac.new(key, message, hashlib.sha256).hexdigest()

def calulate_md5(content):
    md5 = hashlib.md5(content).hexdigest()
    return md5

def get_parent_process_name():
    # 获取当前进程的信息
    current_process = psutil.Process()
    # 获取父进程的信息
    parent_process = current_process.parent()
    # 获取父进程的名称
    parent_process_name = parent_process.name() if parent_process else ""
    return parent_process_name


def get_current_process_name():
    # 获取当前进程的信息
    current_process = psutil.Process()
    # 获取当前进程的名称
    current_process_name = current_process.name()
    return current_process_name

def count_process_instances(process_name):
    count = 0
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == process_name:
            count += 1
    return count


if __name__ == '__main__':
    # 加密文本
    plaintext = "Hello, world!"
    print("Plaintext:", plaintext)

    ciphertext = encrypt(plaintext)
    print("Ciphertext:", ciphertext)

    # 解密文本
    decrypted_data = decrypt(ciphertext)
    print("Decrypted data:", decrypted_data)
