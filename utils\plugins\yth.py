import ctypes

my_dll = ctypes.WinDLL("dll/yth.dll")
anti_vm = my_dll.fn1
anti_debug1 = my_dll.fn2
anti_debug2 = my_dll.fn3
anti_debug3 = my_dll.fn4
anti_anti_debug4 = my_dll.fn5
show_task_bar_icon = my_dll.fn6

action_code_dict = {
    "正常": "*d#fl1I@34rt7%gh.",
    "检测到改数据": "*d#flI1@34rt7%gh.",
    "检测到虚拟机": "*d#flI2@34rt7%gh.",
    "检测到调试器": "*d#flI3@34rt7%gh.",
}

def get_user_action_info():
    ret1 = anti_vm()
    if ret1:
        return action_code_dict["检测到虚拟机"]
    ret2 = anti_debug1()
    ret3 = anti_debug2()
    ret4 = anti_debug3()
    ret5 = anti_anti_debug4()
    if any([ret2, ret3, ret4, ret5]):
        return action_code_dict["检测到调试器"]
    return action_code_dict["正常"]
