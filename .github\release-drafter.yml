name-template: 'v$RESOLVED_VERSION 🌈'
tag-template: 'v$RESOLVED_VERSION'
categories:
  - title: '🚀 新功能'
    labels:
      - 'feature'
      - 'feat'
      - 'enhancement'
      - 'kind/feature'
  - title: '🐛 Bug修复'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
      - 'regression'
      - 'kind/bug'
  - title: '👻 日常维护'
    label: 
      - 'refine'
      - 'refactor'
change-template: '- $TITLE'
change-title-escapes: '\<*_&'
version-resolver:
  major:
    labels:
      - 'version-major'
  minor:
    labels:
      - 'version-minor'
  patch:
    labels:
      - 'patch'
  default: patch
template: |
  $CHANGES
no-changes-template: |
  快来下载体验吧~
