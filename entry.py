# 导入pyd原本要导入的所有包
import sys

from PySide2.QtWidgets import QApplication
from PySide2.QtCore import Qt
from PySide2.QtWidgets import QStyleFactory

from biz.team.team import *
from biz.constants.constants import *
from const.const import *
from utils.com import create_com_obj, reg_com_to_system
import settings
from utils import *
from wnd_main_code import WndMain
from biz.obj.worker import Worker

# 再导入pyd
from biz.res import qiling  

if not (__name__ != '__main__'):
    qiling.qi_ling_main()