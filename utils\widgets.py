from PySide2.QtWidgets import QComboBox, QListWidget, QListView, QListWidgetItem, \
    QCheckBox, QLabel, QWidget, QHBoxLayout, QTableWidget, QApplication, QTableWidgetItem, \
    QAbstractItemView, QStyledItemDelegate, QTableWidgetItem

from PySide2.QtCore import QMimeData, Qt, QRegExp, Signal, Slot
from PySide2.QtGui import QRegExpValidator, QColor

from utils.utils import get_start_digit
from const.const import *
import settings


class CustomListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

    def addItem(self, item_text):
        super().addItem(item_text)
        self.custom_sort_items()

    def custom_sort_items(self):
        # 获取列表中的所有项
        items = [self.item(i).text()
                 for i in range(self.count())]
        # 使用内置的sorted函数对项列表进行排序
        sorted_items = sorted(items, key=get_start_digit)
        # 清空列表
        self.clear()
        # 重新按照排序后的顺序添加项
        for item in sorted_items:
            super().addItem(item)


class MiniItemComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setView(QListView())
        self.setStyleSheet("QComboBox QAbstractItemView::item{height:18px;}")

    def contextMenuEvent(self, event):
        return self.parent().contextMenuEvent(event)


class AutoSortComboBox(MiniItemComboBox):
    def addItem(self, item_text):
        super().addItem(item_text)
        cur_text = self.currentText()
        self.custom_sort_items()
        self.setCurrentText(cur_text)

    def custom_sort_items(self):
        # 获取列表中的所有项
        items = [self.itemText(i)
                 for i in range(self.count())]
        # 使用内置的sorted函数对项列表进行排序
        sorted_items = sorted(items, key=get_start_digit)
        # 清空列表
        self.clear()
        # 重新按照排序后的顺序添加项
        for item in sorted_items:
            super().addItem(item)


class CheckableListWidget(QListWidget):
    def addItem(self, item_text):
        item = CheckableItem(item_text, self)
        super().addItem(item)

    def addItems(self, items):
        for item_text in items:
            self.addItem(item_text)


class CheckableItem(QListWidgetItem):
    def __init__(self, text, parent=None):
        super().__init__(parent)
        self.checkbox = QCheckBox()
        self.checkbox.setChecked(True)
        self.label = QLabel(text)
        self.widget = QWidget()
        self.layout = QHBoxLayout()
        self.layout.addWidget(self.checkbox)
        self.layout.addWidget(self.label)
        self.layout.addStretch(0)
        self.layout.setContentsMargins(1, 0, 0, 0)
        self.widget.setLayout(self.layout)
        self.setSizeHint(self.widget.sizeHint())
        self.listWidget().setItemWidget(self, self.widget)

    def setChecked(self, checked):
        self.checkbox.setChecked(checked)

    def isChecked(self):
        return self.checkbox.isChecked()

    def text(self) -> str:
        return self.label.text()


class ExcelTableWidget(QTableWidget):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setEditTriggers(QAbstractItemView.DoubleClicked |
                             QAbstractItemView.SelectedClicked)

    def add_row_items(self, row: int):
        # 窗口句柄
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_HWND, item)
        # 角色名
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_NAME, item)
        # 方案选择
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_PLAN, item)
        cmb_plan = AutoSortComboBox(self)
        cmb_plan.setStyleSheet("QComboBox{border: 0px; border-radius: 0px;}")
        self.setCellWidget(row, COL_PLAN, cmb_plan)
        cmb_plan.setEnabled(False)
        # 运行
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_RUN, item)
        # 暂停
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_PAUSE, item)
        # 终止
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_END, item)
        # 日志
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_LOG, item)
        # 账号
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsEditable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_ACCOUNT, item)
        # 密码
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsEditable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_PASSWORD, item)
        # 线路
        item = QTableWidgetItem()
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
        self.setItem(row, COL_SERVER, item)
        cmb_server = MiniItemComboBox(self)
        cmb_server.setStyleSheet("QComboBox{border: 0px; border-radius: 0px;}")
        self.setCellWidget(row, COL_SERVER, cmb_server)
        # 设置颜色
        if 0 <= row % 10 < 5:
            color = QColor(230, 255, 245, 168)
        else:
            color = QColor(230, 245, 255, 168)
        for col in range(TBE_CONSOLE_COL):
            self.item(row, col).setBackgroundColor(color)
        return cmb_plan, cmb_server
        


    def copy(self):
        print('copy')
        selection = self.selectedIndexes()
        if not selection:
            return

        # 获取选区的边界
        rows = sorted({index.row() for index in selection})
        cols = sorted({index.column() for index in selection})

        # 构建文本数据
        text_data = []
        for row in range(rows[0], rows[-1] + 1):
            row_data = []
            for col in range(cols[0], cols[-1] + 1):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            text_data.append('\t'.join(row_data))

        # 设置剪贴板内容
        clipboard = QApplication.clipboard()
        mime_data = QMimeData()
        mime_data.setText('\n'.join(text_data))
        clipboard.setMimeData(mime_data)

    def paste(self):
        print('paste')
        clipboard = QApplication.clipboard()
        mime_data = clipboard.mimeData()

        if mime_data.hasText():
            text = mime_data.text()
            rows = text.split('\n')

            # 获取当前选中的起始单元格
            current_row = self.currentRow()
            current_col = self.currentColumn()

            for i, row in enumerate(rows):
                if not row.strip():
                    continue

                cells = row.split('\t')

                # 确保有足够的行和列
                if current_row + i >= self.rowCount():
                    self.insertRow(current_row + i)

                for j, cell in enumerate(cells):
                    if current_col + j >= self.columnCount():
                        self.insertColumn(current_col + j)

                    # 设置单元格内容
                    item = self.item(current_row + i, current_col + j)
                    if not item:
                        item = QTableWidgetItem()
                        self.setItem(current_row + i, current_col + j, item)
                    item.setText(cell.strip())

    def cut(self):
        print('cut')
        self.copy()  # 先复制
        self.clear_selection()  # 然后清空选中内容

    def clear_selection(self):
        selection = self.selectedIndexes()
        for index in selection:
            item = self.item(index.row(), index.column())
            if item:
                item.setText("")

    def keyPressEvent(self, event):
        # 监听Ctrl+C, Ctrl+V, Ctrl+X和Backspace快捷键
        if event.modifiers() == Qt.ControlModifier:
            if event.key() == Qt.Key_C:
                self.copy()
            elif event.key() == Qt.Key_V:
                self.paste()
            elif event.key() == Qt.Key_X:
                self.cut()
            else:
                super().keyPressEvent(event)
        elif event.key() == Qt.Key_Backspace:
            self.clear_selection()
        else:
            super().keyPressEvent(event)

    def wheelEvent(self, event):
        # 发送滚轮事件给事件过滤器
        QApplication.sendEvent(self.parent(), event)


class CompositeDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 初始化各个验证器
        self.no_space_validator = QRegExpValidator(QRegExp("[^\\s]*"))
        self.numeric_validator = QRegExpValidator(QRegExp(r"^\d*$"))

    def createEditor(self, parent, option, index):
        editor = super().createEditor(parent, option, index)
        if editor:
            if index.column() == 0:  # 第一列禁止空格
                editor.setValidator(self.no_space_validator)
            elif index.column() == 1:  # 第二列只允许数字
                editor.setValidator(self.numeric_validator)
        return editor
