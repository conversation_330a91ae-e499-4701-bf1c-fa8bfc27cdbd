syntax = "proto3";

package api.netauth.v1;  // proto的包名, 当被其它proto引用时才会用到
import "api/netauth/v1/enums.proto";

service Card {
	rpc GetCardInfoV2(GetCardInfoRequestV2) returns (GetCardInfoReplyV2) {
	};
    rpc UnBindCard(UnBindCardRequest) returns (UnBindCardReply) {
    }
    rpc GetUpdateInfo(GetUpdateInfoRequest) returns (GetUpdateInfoReply) {
    }
    rpc SetLatestVersionInfo(SetLatestVersionInfoRequest) returns (SetLatestVersionInfoReply) {
    }
    rpc GetCustomInfo(GetCustomInfoRequest) returns (GetCustomInfoReply) {
    }
}


message GetCardInfoRequestV2 {
	string card_number=1;
	string machine_code=2;
	optional uint32 card_extra_rights=3;
	string user_info=4;
    string req_ts=5;
    string client_version=6;
    string x_check_sum=7;
}
message GetCardInfoReplyV2 {
	CardType card_type=1;
	CardRights card_rights=2;
	uint32 card_extra_rights=3;
	string due_ts=4;
    string check_sum=5;
}

message UnBindCardRequest {
    string card_number=1;
    string machine_code=2;
}

message UnBindCardReply {
    
}

message GetUpdateInfoRequest {
    string card_number=1;
    string machine_code=2;
    string client_version=3;
}
message GetUpdateInfoReply {
    bool force_update = 1;
    string latest_version = 2;
    string patcher_download_url = 3;
    string installer_download_url = 4;
    string update_info = 5;
    string md5 = 6;
}

message GetCustomInfoRequest {
    string card_number=1;
    string machine_code=2;
    string client_version=3;
}
message GetCustomInfoReply {
    string custom_info = 1;
}

message SetLatestVersionInfoRequest {
    string latest_version = 1;
    string latest_installer_url = 2;
    string update_info = 3;
    string token = 4;
}
message SetLatestVersionInfoReply {
    
}