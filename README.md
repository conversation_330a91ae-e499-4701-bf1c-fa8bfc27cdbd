# 倚天辅助

## 1 介绍
一款倚天剑与屠龙刀的python游戏脚本, 基于Python3.8.10 + PySide2 5.15.2 + dm插件6.1544开发
这是一个基于Python开发的《倚天剑与屠龙刀》游戏辅助脚本，主要功能包括：

基础架构：
基于Python 3.8.10 + PySide2 5.15.2开发
使用大漠插件进行图像识别和模拟操作
采用面向对象设计，有良好的任务基类(TaskBase)封装

主要任务系统：
单人任务：
	押镖任务：支持游戏币/通宝两种方式，自动识别最高等级押镖
团队任务：
	四大名捕：挑战无情、铁手、追命、冷血等BOSS
	明教高手：挑战韦一笑、谢逊、殷天正等BOSS
	五绝：挑战黄药师、欧阳锋等BOSS
	紫禁高手：挑战花满楼、叶孤城等BOSS
	珍珑棋局：挑战苏星河、丁春秋等BOSS

核心功能：
自动战斗系统：支持人物和宝宝(BB)的自动战斗
自动寻路系统：可以自动在地图间移动
自动修理装备和宝宝
防外挂验证处理
自动完成任务对话
背包物品管理
多开号分组控制

安全特性：
支持防虚拟机检测
支持防调试器检测
支持防多开检测
自动处理验证码
这是一个功能完整的游戏辅助工具，可以帮助玩家自动完成游戏中的各种任务。

## 2 安装
> pip install -r requirements.txt

## 3 运行
> sudo python main.py

## 4 打包

### 4.1 打包主程序
> pyinstaller main.spec --clean -y

### 4.2 打包launcher
> pyinstaller launcher.spec --clean -y

### 4.3 打包安装程序
命令行操作:
> 1. 先把launcher.exe放入main文件夹
> 2. 执行命令: iscc /O+ launcher.iss

## 5 编绎proto
> python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. ./api/netauth/v1/enums.proto ./api/netauth/v1/card.proto

GUI操作:
![alt text](imgs/image.png)
![alt text](imgs/image2.png)