from biz.task.__base import TaskBase
from biz.task.single.jiao_jie_huo import TaskJieHuo
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker

class TaskPeach(TaskBase):
    TASK_NAME = "交桃子"
    IS_FREE = True
    POS_DELIVER = (645, 266)
    
    @classmethod
    def run(cls, wk: Worker):
        cls.run_to_map_run_away(wk, "桃花林")
        cls.deliver_peach(wk)
        if wk.cfg_plan_task["交完自动切接货"]:
            while True:
                wk.record("交桃子完成, 自动切接货")
                cls.big_map_click(wk, *cls.POS_DELIVER)
                msleep(1000)
                if wk.is_fight:
                    wk.should_run_away = True
                    cls.fight_operation(wk)
                    wk.should_run_away = False
                wk.cur_task = "接货"
                TaskJieHuo.run(wk)
                wk.record("接货满了, 自动切交桃")
                wk.cur_task = cls.TASK_NAME
                cls.deliver_peach(wk)
        
    @classmethod
    def deliver_peach(cls, wk: Worker):
        # color = COLOR_GREEN + "|" + COLOR_CYAN
        for _ in range(10000):
            wk.record("接桃子任务...")
            if cls.is_talk_open(wk, timeout=200):
                if cls.is_talk_show_info(wk, "你去找桃花林", COLOR_BLACK):
                    wk.record("没有桃子了")
                    break
                if wk.find_str_click(*RECT_TALK, "寻找桃子", COLOR_TALK_ITEM_TASK):
                    wk.record("交桃子")
                cls.close_other_talk(wk)
            if not cls.find_way_npc(wk, "独孤猴", ["桃子啊"], fight_run_away=True, close_find_way=False, close_talk=False):
                cls.talk_with_cur_map_npc(wk, "独孤猴", ["桃子啊"], fight_run_away=True, map_name='桃花林')
            # if not wk.find_str_offset_click(*RECT_FIND, "独孤猴", color, dx=20, dy=rnd(80, 110), re_move=False, is_sleep=False, limit_border=True, limit_y=Y_LIMIT_CLICK):
            #     wk.key_press(VK_F12)
            #     continue
        msleep(400)
        cls.close_other_talk(wk)
        wk.record("交桃子完成, 正在丢弃垃圾...")
        cls.throw_baggage(wk, thow_equip=True)
    
    @classmethod
    def get_default_biz_config(cls):
        return {
            "交完自动切接货": False,
            "接货包满飞回门派": False,
            "交接位置": "原地",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件 -> 控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_deliver_peach_jie_huo.setChecked(cls.CONFIG["交完自动切接货"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件 -> 文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["交完自动切接货"] = settings.wnd_main.chk_deliver_peach_jie_huo.isChecked()
        super().cfg_save(plan_name)