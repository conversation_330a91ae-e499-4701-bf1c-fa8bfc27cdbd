from biz.constants.constants import *
from biz.task.__base import TaskBase
from biz.task.team.fu_ben import TaskFuBen
from utils import *
from biz.obj.worker import Worker
import settings


class TaskDifficultSingle(TaskBase):
	NEED_AVOID_FIGHT = False
	IS_DIFFICULT_TASK = True
	BOSS_WU_XING_ORDER = ["木", "土", "水", "火", "金"]
	PEOPLE_WU_XING_ORDER = ["金", "木", "土", "水", "火"]

	@classmethod
	def before_run(cls, wk: Worker):
		# 重置任务类型和完成次数
		cls.set_task_config(wk)
		wk.done_count = 0
		cls.click_confirm(wk, timeout=0)
		cls.close_pages(wk)
		wk.is_stuck = True
		wk.record("开始执行")

	@classmethod
	def people_fight_action(cls, wk: Worker):
		if wk.cfg_plan["人物五行克制F1"]:  # 勾选了五行克怪
			# 根据上次怪使用的技能来决定使用哪个五行技能点杀
			if wk.boss_wu_xing_idx != -1:
				boss_wu_xing = cls.BOSS_WU_XING_ORDER[wk.boss_wu_xing_idx]
				player_wu_xing = cls.PEOPLE_WU_XING_ORDER[wk.boss_wu_xing_idx]
				wk.record(f"考官使用:{boss_wu_xing}招, 大侠使用:{player_wu_xing}招克制")
				wk.key_press(VK_F1+wk.boss_wu_xing_idx)
		super().people_fight_action(wk)

	@classmethod
	def is_enemy_hit_zhang_ai_meet(cls, wk: Worker):
		x, y = POS_ENENMY_LIST[IDX_BOSS]
		use_shi_hou = wk.find_pic(*RECT_SKILL, "技能图标_狮吼.bmp")
		if use_shi_hou:
			wk.enemy_ci_xue_hited = wk.is_region_color_count_enough(
				x-32, y-48, x-3, y, COLOR_SHIHOU, min_count=40, timeout=1400)
		else:
			wk.enemy_ci_xue_hited = wk.is_region_color_count_enough(
				x-25, y-68, x+19, y-21, COLOR_CIXUE, min_count=180, timeout=1400)
		return wk.enemy_ci_xue_hited

	@classmethod
	def fight_check_boss_wu_xing(cls, wk: Worker):
		if wk.boss_recognize_round == wk.cur_round:  # 当前回合已识别出五行
			return True
		# 识别五行技能
		wk.boss_wu_xing_idx = cls.get_boss_wu_xing(wk)
		if wk.boss_wu_xing_idx != -1:
			wk.boss_recognize_round = wk.cur_round
			return True
		return False

	@classmethod
	def get_boss_wu_xing(cls, wk: Worker):
		pos1 = (198, 149, 220, 171)  # 远
		pos2 = (500, 283, 519, 301)  # 打人
		pos3 = (422, 241, 441, 259)  # 打BB
		pos_list = [pos1, pos2, pos3]
		# 克招: 金 木 土 水 火
		for pos in pos_list:
			idx = wk.get_pic_idx(*pos, "木.bmp|土.bmp|水.bmp|火.bmp|金.bmp")
			if idx != -1:
				return idx
		return -1

	@classmethod
	def call_bb(cls, wk: Worker):  # 唤出BB
		if not cls.is_meet_call_bb_condition(wk):
			return
		bb_name = wk.cfg_plan["唤出BB名字"]
		cls.do_call_bb(wk, bb_name)


class TaskJiXian(TaskDifficultSingle):
	TASK_NAME = "极限挑战"
	RANDOM_FIGHT_ENEMY = False
	IS_TASK_FIX_EQUIP_BB_ENABLE = True

	@classmethod
	def before_run(cls, wk: Worker):
		super().before_run(wk)
		wk.need_query = True  # 需要查询当前环数

	@classmethod
	def run(cls, wk: Worker):
		cls.leave_team(wk)
		while wk.done_count < wk.cfg_plan_task["环数"]:
			msleep(600)
			try:
				if wk.is_fight:
					cls.chat_input_text(wk, f"第 {wk.done_count + 1} 环")
					wk.record(f"第 {wk.done_count + 1} 环 开始")
					cls.fight_operation(wk)  # 失败终止 会抛异常, 完成环数也会抛异常
			except:  # 抛出 失败终止 异常时, 如果地图在开封, 说明确实是挑战失败了, 直接跳出
				cur_map_name = cls.cur_map(wk, timeout=400)
				if cur_map_name in ["开封", ""]:
					break
			# 没有异常的话判断
			cur_map_name = cls.cur_map(wk)
			if cur_map_name == "":  # 没识别到地图名, 可能还在战斗
				msleep(1000)
				continue
			if cur_map_name == "开封":
				wk.record("找曹都尉极限挑战...")
				try:
					cls.start_ji_xian(wk)
				except:
					break
				continue
			
			try:
				if cur_map_name != "":
					wk.record(f"当前在{cur_map_name}, 正在跑回开封...")
					cls.run_to_map_run_away(wk)
			except:  # 野外遇到战斗不要触发失败终止异常
				cur_map_name = cls.cur_map(wk, timeout=400)
				if cur_map_name in ["开封", ""]:
					break
		wk.record("极限挑战完成")
		if wk.cfg_plan_task["结束后领奖"]:
			cls.get_award(wk)

	@classmethod
	def click_run_away(cls, wk: Worker):
		if wk.find_str(*RECT_BOSS_NAME, "考官", COLOR_GREEN):
			wk.record("正在和考官战斗...")
			return
		return super().click_run_away(wk)

	@classmethod
	def start_ji_xian(cls, wk: Worker):
		cls.do_fix_bb(wk)
		if wk.need_query:
			wk.record("需要查询当前环数...")
			cls.close_other_talk(wk)
			cls.find_way_npc(wk, "曹都尉", ["极限挑战", "查询环数"], close_talk=False, handle_fight=False)
			msleep(400)
			if wk.find_str(*RECT_FULL, "当前挑战环数为", COLOR_BLACK, timeout=600):
				wk.need_query = False
				cur_ring = wk.ocr(*RECT_JI_XIAN_CUR_RING, COLOR_BLACK, zk=ZK_DIGIT_11)
				wk.done_count = int(cur_ring or 0)
				fail_count_str = wk.ocr(
					*RECT_JI_XIAN_FAIL_COUNT, COLOR_GREEN, zk=ZK_DIGIT_11)
				fail_count = int(fail_count_str or 1)
				wk.record(f"本周失败次数: {fail_count}")
				wk.record(f"当前环数:{wk.done_count}")
				cls.close_pages(wk)
				setting_count = wk.cfg_plan_task["环数"]
				if wk.done_count >= setting_count:
					wk.record(f"达到设定环数{setting_count}")
					raise Exception("达到指定环数")
				if wk.cfg_plan_task["失败一次后终止"] and fail_count >= 1:
					raise Exception("本周失败次数达到上限")
		if cls.is_talk_open(wk, timeout=500) and wk.find_str_click(
			*RECT_TALK, "确定", COLOR_TALK_ITEM
		):
			wk.record("继续下一轮战斗...")
			msleep(800)
			return
		cls.find_way_npc(wk, "曹都尉", ["极限挑战|开始挑战", "开始挑战"],
						 close_talk=False, handle_fight=False)
		msleep(500)
		if cls.is_talk_show_info(wk, "本周不能再进行挑战|本周挑战次数已用完|现在不是活动时间"):
			wk.record("本周不能再挑战")
			wk.done_count = wk.cfg_plan_task["环数"]
		cls.talk_click_specify_item(wk, wk.cfg_plan_task["考官类型"])

	@classmethod
	def after_fight_switch_primary_bb(cls, wk: Worker):
		msleep(600)
		if cls.is_talk_show_info(wk, "确定|取消|继续挑战", color=COLOR_TALK_ITEM, timeout=800):
			# 达到指定环数结束
			if cls.cur_map(wk) == "开封":
				wk.done_count += 1
				count = wk.cfg_plan_task["环数"]
				wk.record(f"已完成 {wk.done_count}/{count}")
			if wk.done_count >= wk.cfg_plan_task["环数"]:
				wk.record("达到指定环数")
				cls.talk_click_specify_item(wk, "取消")
				msleep(600)
				cls.close_other_talk(wk)
				if wk.cfg_plan["人物切回首发BB"]:
					super().after_fight_switch_primary_bb(wk)
				raise Exception("达到指定环数")
			# 切首发, 点取消, 否则点确定
			if wk.cfg_plan["人物切回首发BB"] or wk.is_called_bb or wk.need_query:
				# print(f"wk.is_called_bb: {wk.is_called_bb}, wk.need_query: {wk.need_query}")
				# 极限要把考官的对话关掉，才能切
				if cls.talk_click_specify_item(wk, "取消"):
					if wk.is_called_bb:
						cls.do_rest_bb(wk)
					if wk.cfg_plan["人物切回首发BB"]:
						super().after_fight_switch_primary_bb(wk)
					cls.start_ji_xian(wk)
			elif wk.need_fix_equip_bb:
				wk.record("需要修理忠诚, 点击取消")
				cls.talk_click_specify_item(wk, "取消")
			else:  # 人物死了 宝宝把考官弄死了 出来以后  显示继续挑战
				cls.talk_click_specify_item(wk, "确定|继续挑战")
				return
		elif wk.cfg_plan_task["失败一次后终止"]:  # 没有出现对话框的话 且地图在开封 说明失败了
			if not wk.is_fight and cls.cur_map(wk) == "开封":
				wk.record("失败一次了, 自动终止")
				raise Exception("失败终止")

	@classmethod
	def get_award(cls, wk: Worker):
		wk.record("极限结束后领奖...")
		cls.close_pages(wk)
		cls.talk_with_cur_map_npc(
			wk, "曹都尉", ["极限挑战", "领取奖励"], close_talk=False, use_fast=False)
		if cls.is_talk_show_info(wk, "本周您挑战没有跨阶", timeout=400):
			wk.record("本周您挑战没有跨阶, 领奖失败")
			cls.close_other_talk(wk)
			return
		if not wk.find_str(*RECT_FULL, "称号等级", COLOR_BLACK):
			wk.record("奖励领取界面未打开, 领奖失败")
			return
		wk.move_click(*POS_JI_XIAN_CHENG_HAO_RED)
		wk.move_click(*POS_JI_XIAN_CHENG_HAO_BLUE)
		wk.move_click(*POS_JI_XIAN_AWARD_CONFIRM)
		msleep(600)
		if cls.click_confirm(wk):
			msleep(400)
			if cls.is_popup_show_info(wk, "包裹空间不足"):
				wk.record("包裹空间不足, 领奖失败")
				return
		wk.record("领奖成功")

	@classmethod
	def is_enemy_hit_lsgs(cls, wk: Worker):
		x, y = POS_ENENMY_LIST[IDX_BOSS]
		return wk.is_region_color_count_enough(
			x-41, y-83, x+47, y-33, COLOR_GESHI, min_count=200,
			color2=COLOR_SHIHOU, min_count2=160, timeout=900)

	@classmethod
	def ready_overlap_auth(cls, wk: Worker):
		delta_color = "202020"
		if cls.switch_to_taohuadao(wk):
			delta_color = "101010"
		return delta_color

	@classmethod
	def switch_to_taohuadao(cls, wk: Worker):
		super().switch_to_taohuadao(wk)
		# 可能因为出考官对话打不开
		if not cls.is_team_platform_page_open(wk):
			wk.record("打开组队平台失败")
			return False
		return True

	@classmethod
	def adjust_color(cls, wk: Worker, pic_list_length: int, delta_color: str):
		return TaskFuBen.adjust_color(wk, pic_list_length, delta_color)
	
	@classmethod
	def is_should_fix_equip_bb(cls, wk: Worker):
		if wk.cfg_plan_task["修理装备"]:
			return super().is_should_fix_equip_bb(wk)
		if wk.need_fix_equip_bb or wk.need_fix_equip:
			return
		if rnd(1, 5) != 5:  # 不用太频繁
			return
		if cls.is_system_show_fix_bb(wk):
			wk.need_fix_equip_bb = True
			wk.record("需要修理忠诚")
			return
		if cls.is_system_show_fix_equib(wk):
			wk.need_fix_equip = True
			wk.record("需要修理装备")
			return

	@classmethod
	def get_default_biz_config(cls):
		return {
			"考官类型": "外",
			"环数": 330,
			"失败一次后终止": False,
			"结束后领奖": False,
			"修理装备": True,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.spin_count_ji_xian.setValue(cls.CONFIG["环数"])
		settings.wnd_main.chk_ji_xian_fail_stop.setChecked(
			cls.CONFIG["失败一次后终止"])
		settings.wnd_main.chk_ji_xian_get_award.setChecked(cls.CONFIG["结束后领奖"])
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_ji_xian, cls.CONFIG["考官类型"]
		)
		settings.wnd_main.chk_ji_xian_fix_equip.setChecked(cls.CONFIG["修理装备"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["考官类型"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_ji_xian
		)
		cls.CONFIG["环数"] = settings.wnd_main.spin_count_ji_xian.value()
		cls.CONFIG["失败一次后终止"] = settings.wnd_main.chk_ji_xian_fail_stop.isChecked()
		cls.CONFIG["结束后领奖"] = settings.wnd_main.chk_ji_xian_get_award.isChecked()
		cls.CONFIG["修理装备"] = settings.wnd_main.chk_ji_xian_fix_equip.isChecked()
		super().cfg_save(plan_name)


class TaskDianFeng(TaskDifficultSingle):
	TASK_NAME = "巅峰之塔"
	RANDOM_FIGHT_ENEMY = True  # 要随机封怪的

	@classmethod
	def run(cls, wk: Worker):
		cls.leave_team(wk)
		if cls.cur_map(wk) != "开封":
			cls.run_to_map(wk)
		while wk.done_count < wk.cfg_plan_task["次数"]:
			if wk.is_fight:
				cls.fight_operation(wk)
			msleep(600)
			try:
				cls.recv_task(wk)
			except:
				break
		

	@classmethod
	def recv_task(cls, wk: Worker):
		for i in range(1000):
			if wk.find_str_click(*RECT_TALK, "巅峰之塔", COLOR_TALK_ITEM):
				msleep(800)
				break
			if i == 0 or wk.is_stuck:
				cls.search_click_npc(wk, "冯知府")
			if wk.is_fight:
				break
			msleep(500)
		if not wk.find_pic_click(*RECT_FULL, "挑战.bmp", timeout=900):
			return
		msleep(600)
		if cls.is_popup_show_info(wk, "战斗将从第"):
			cls.click_confirm(wk)
			msleep(600)
		if cls.is_popup_show_info(wk, "您已经挑战过了"):
			wk.record("本周已经挑战过了")
			cls.click_confirm(wk)
			if wk.cfg_plan_task["结束后领奖"]:
				wk.record("已勾选结束后领奖, 正在领取...")
				wk.find_pic_click(*RECT_FULL, "领奖爬塔.bmp", timeout=600)
				msleep(400)
				cls.click_confirm(wk)
			raise Exception("本周已经挑战过了")
		cls.click_confirm(wk)
		msleep(600)

	@classmethod
	def fight_do_something(cls, wk: Worker):
		super().fight_do_something(wk)
		if cls.is_talk_show_info(wk, "是否继续挑战"):
			wk.done_count += 1
			cls.init_fight_flag(wk)
			count = wk.cfg_plan_task["次数"]
			wk.record(f"已完成 {wk.done_count}/{count}")
			if wk.done_count < wk.cfg_plan_task["次数"]:
				cls.talk_click_specify_item(wk, "继续挑战")
			else:
				if cls.talk_click_specify_item(wk, "结束挑战"):
					msleep(600)
					cls.talk_click_specify_item(wk, "确定退出")
				else:
					wk.record("到达指定次数, 但没有找到 结束挑战")

	@classmethod
	def is_enemy_hit_zhang_ai_meet(cls, wk: Worker):
		cur_percent = 0
		setting_percent = wk.cfg_plan["人物刺怪比例"]
		use_shi_hou = wk.find_pic(*RECT_SKILL, "技能图标_狮吼.bmp")
		for _ in range(12):
			msleep(150)
			count = 0
			not_hited_pos_list = []
			for x, y in wk.survive_enemy_pos_list:
				if use_shi_hou:
					enemy_hited = wk.is_region_color_count_enough(
						x-32, y-52, x-3, y-3, COLOR_SHIHOU, min_count=50)
				else:
					enemy_hited = wk.is_region_color_count_enough(
						x-25, y-68, x+19, y-21, COLOR_CIXUE, min_count=170)
				if enemy_hited:  # 被刺穴
					count += 1
				else:
					not_hited_pos_list.append((x, y))	
			if count:
				break
		# 找not_hited_pos_list中y最大的
		if not_hited_pos_list:
			wk.not_hit_ci_xue_pos = max(not_hited_pos_list, key=lambda pos: pos[1])
		if not wk.survive_enemy_pos_list:  # 没有识别到敌人, 那就当刺中了
			wk.enemy_ci_xue_hited = True
			return True
		survive_count = len(wk.survive_enemy_pos_list) or 1
		cur_percent = int((count / survive_count) * 100)
		setting_percent = wk.cfg_plan["人物刺怪比例"]
		if cur_percent >= setting_percent:
			# wk.record(f"当前刺怪比例:{cur_percent}% >= 设定的比例:{setting_percent}%")
			wk.enemy_ci_xue_hited = True
			return True
			
		wk.record(f"当前刺怪比例:{cur_percent}% < 设定的比例:{setting_percent}%")
		wk.enemy_ci_xue_hited = False
		return False
	
	@classmethod
	def get_cur_enemy_count(cls, wk: Worker):
		monster_name = "小喽喽|双剑快刀手|茅山小道|翠林隐侠|金甲铁卫|带刀卫士|江湖术士|蓝衣灵女|异域霸主|巅峰王者"
		color = COLOR_GREEN + "|" + COLOR_QI_LING3
		wk.survive_enemy_pos_list = []
		for x, y in POS_ENENMY_LIST:
			if wk.find_str(x-40, y-113, x+45, y-75, monster_name, color):
				# 把这些存活敌人的坐标存储下来
				wk.survive_enemy_pos_list.append((x, y))
		# print(f"wk.survive_enemy_pos_list: {wk.survive_enemy_pos_list}")
		# print(f'当前怪数:{len(wk.survive_enemy_pos_list)}')
		return len(wk.survive_enemy_pos_list)

	@classmethod
	def fight_check_boss_wu_xing(cls, wk: Worker):
		return True
	
	@classmethod
	def get_enemy_color_list(cls, wk: Worker):
		return [COLOR_GREEN]

	@classmethod
	def get_default_biz_config(cls):
		return {
			"次数": 4,
			"结束后领奖": False,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.spin_count_pa_ta.setValue(cls.CONFIG["次数"])
		settings.wnd_main.chk_dian_feng_get_award.setChecked(cls.CONFIG["结束后领奖"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["次数"] = settings.wnd_main.spin_count_pa_ta.value()
		cls.CONFIG["结束后领奖"] = settings.wnd_main.chk_dian_feng_get_award.isChecked()
		super().cfg_save(plan_name)


class TaskDaNei(TaskDifficultSingle):
	TASK_NAME = "大内高手"
	IS_TASK_FIX_EQUIP_BB_ENABLE = True

	@classmethod
	def run(cls, wk: Worker):
		if cls.get_teammate_count(wk) > 1:
			wk.record("检测到有队伍, 进入组队大内模式")
			wk.team.reset_all_signal()
			wk.team.task_name = cls.TASK_NAME
			wk.team.is_difficult_task = cls.IS_DIFFICULT_TASK
			wk.team.is_switch_primary_bb_enable = cls.IS_DIFFICULT_TASK
			wk.team.IS_TASK_FIX_EQUIP_BB_ENABLE = cls.IS_TASK_FIX_EQUIP_BB_ENABLE
		else:
			wk.record("检测到单人, 进入单人大内模式")
			cls.leave_team(wk)
			cls.switch_line(wk, "二")
		cls.back_to_kai_feng(wk)
		while True:
			msleep(600)
			if cls.is_talk_show_info(wk, "不接受任何"):
				wk.record("大内高手不接受指点了!")
				break
			else:
				cls.close_other_talk(wk)
			if wk.is_fight:
				cls.fight_operation(wk)
				msleep(400)
			if cls.talk_click_specify_item(wk, "继续指点"):
				continue
			if not cls.find_way_npc(wk, "钦差大臣", ["我要挑战"], close_talk=False):
				cls.talk_with_cur_map_npc(
					wk, "钦差大臣", ["我要挑战"], close_talk=False)
		wk.should_run_away = False

	@classmethod
	def after_fight_switch_primary_bb(cls, wk: Worker):
		msleep(600)
		if cls.is_talk_show_info(wk, "继续|休息", color=COLOR_TALK_ITEM, timeout=800):
			if wk.cfg_plan["人物切回首发BB"]:
				if cls.talk_click_specify_item(wk, "休息"):
					super().after_fight_switch_primary_bb(wk)
					return
			else:
				cls.talk_click_specify_item(wk, "继续")
				return

	@classmethod
	def recognize_enemy(cls, wk: Worker):
		wk.should_run_away = False
		if wk.cur_round > 1:
			return
		boss_name = wk.ocr(*RECT_BOSS_NAME, COLOR_GOLD, timeout=600)
		if boss_name.endswith(("_", "一")):
			msleep(500)  # 过一会再识别
			boss_name = wk.ocr(*RECT_BOSS_NAME, COLOR_GOLD, timeout=600)
		if not wk.cfg_plan_task["不打小怪"]:
			wk.record(f"正在打 {boss_name}...")
			return
		if boss_name in ["大内高手", "神箭手", "剑客"]:
			if cls.get_teammate_count(wk) > 1:
				wk.record("勾了不打小怪, 但队伍里还有队友, 放弃逃跑")
			else:
				wk.record(f"不打小怪 {boss_name}")
				wk.should_run_away = True
			return
		wk.record(f"遇到 {boss_name}")
		if boss_name.startswith("大内"):
			wk.beep(800, 400)
			return
		if boss_name.startswith("皇家"):
			wk.beep(1000, 600)
			return
		if boss_name.startswith("绝世"):
			wk.beep(1500, 1200)
			return

	@classmethod
	def get_default_biz_config(cls):
		return {
			"不打小怪": False,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.chk_da_nei_run_away.setChecked(cls.CONFIG["不打小怪"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["不打小怪"] = settings.wnd_main.chk_da_nei_run_away.isChecked()
		super().cfg_save(plan_name)


class TaskTuLong(TaskDifficultSingle):
	TASK_NAME = "屠龙"

	@classmethod
	def run(cls, wk: Worker):
		cls.leave_team(wk)
		if not wk.is_fight and cls.cur_map(wk) != "屠龙战场":
			wk.record("未进入屠龙战场")
			return
		while True:
			msleep(600)
			if cls.is_popup_show_info(wk, "巨龙已经逃走了"):
				wk.record("巨龙已经逃走了!")
				cls.click_confirm(wk)
				break
			if wk.is_fight:
				cls.fight_operation(wk)
				msleep(400)
			if cls.talk_click_specify_item(wk, "继续"):
				continue
			cls.talk_with_cur_map_npc(wk, "龙公子", ["我要挑战"], map_name='屠龙战场')
		wk.record("屠龙结束, 切换到一线")
		cls.switch_line(wk, "一")

	@classmethod
	def fight_check_boss_wu_xing(cls, wk: Worker):
		# 屠龙在首回合判定怪名决定五行即可
		return False
	
	@classmethod
	def get_boss_wu_xing(cls, wk: Worker):
		bosses = ["木虬", "土蜃", "雨蛟", "赤螭", "玉龙"]
		color = COLOR_GREEN
		boss_name = wk.ocr(*RECT_BOSS_NAME, color)
		try:
			wk.boss_wu_xing_idx = bosses.index(boss_name)
		except:
			wk.boss_wu_xing_idx = -1
	
	@classmethod
	def fight_do_something(cls, wk: Worker):
		cls.fight_check_pf(wk)
		if wk.boss_wu_xing_idx != -1:  # 已识别出五行
			return
		cls.get_boss_wu_xing(wk)



class TaskNianShou(TaskDaNei):
	TASK_NAME = "年兽来袭"
	'''
	在2线燕将军旁边
	夕 嗜血  狡猾  是小怪，没啥奖励，，除开这三种 都是大的
	暴躁 狂暴
	凶悍 好像给7金刚，神圣金，紫色星珠
	'''

	@classmethod
	def run(cls, wk: Worker):
		if cls.get_teammate_count(wk) > 1:
			wk.record("检测到有队伍, 进入组队年兽模式")
			wk.team.reset_all_signal()
			wk.team.task_name = cls.TASK_NAME
			wk.team.is_difficult_task = cls.IS_DIFFICULT_TASK
			wk.team.is_switch_primary_bb_enable = cls.IS_DIFFICULT_TASK
			wk.team.IS_TASK_FIX_EQUIP_BB_ENABLE = cls.IS_TASK_FIX_EQUIP_BB_ENABLE
		else:
			wk.record("检测到单人, 进入单人年兽模式")
			cls.leave_team(wk)
			cls.switch_line(wk, "二")
		cls.back_to_kai_feng(wk)
		while True:
			msleep(600)
			if cls.is_talk_show_info(wk, "不接受"):
				wk.record("年兽不接受挑战了!")
				break
			else:
				cls.close_other_talk(wk)
			if wk.is_fight:
				cls.fight_operation(wk)
				msleep(400)
			if cls.talk_click_specify_item(wk, "继续"):
				continue
			cls.talk_with_cur_map_npc(
					wk, "年兽", ["畜受死"], close_talk=False)
		wk.should_run_away = False
		

	@classmethod
	def recognize_enemy(cls, wk: Worker):
		wk.should_run_away = False
		if wk.cur_round > 1:
			return
		color = COLOR_GOLD
		boss_name = wk.ocr(*RECT_BOSS_NAME, color, timeout=600)
		if boss_name.endswith(("_", "一")):
			msleep(800)  # 过一会再识别
			boss_name = wk.ocr(*RECT_BOSS_NAME, color, timeout=600)
		if not wk.cfg_plan_task["不打小怪"]:
			wk.record(f"正在打 {boss_name}...")
			return
		if boss_name == '' or any(substring in boss_name for substring in ["夕", "嗜", "狡"]):
			if cls.get_teammate_count(wk) > 1:
				wk.record("勾了不打小怪, 但队伍里还有队友, 放弃逃跑")
			else:
				wk.record(f"不打小怪 {boss_name}")
				wk.should_run_away = True
			return
		wk.record(f"遇到 {boss_name}")
		wk.beep(1000, 600)
		return
