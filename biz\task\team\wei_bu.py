from datetime import datetime

from utils import *
from biz.constants.constants import *
from biz.task._jrw_xl_dg import TaskJrwXlDg
from biz.obj.worker import Worker


class TaskWeibu(TaskJrwXlDg):
    TASK_NAME = "围捕大盗"
    IS_ACTIVITY = True

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(hour=19, minute=30, second=0, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=20, minute=30, second=0, microsecond=0).timestamp()
        )
        
    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday != 3:
            wk.record(f"围捕大盗只能在星期三, 今天是星期{weekday}")
            return False
        return True

    @classmethod
    def handle_final_exception(cls, wk: Worker):
        cls.refresh_task_list(wk)
        return "pass"

    @classmethod
    def handle_other_exception(cls, wk: Worker):
        cls.refresh_task_list(wk)
        return "pass"

    @classmethod
    def check_target_npc_name(cls, wk: Worker, npc_name: str):
        return npc_name == "大盗"

    @classmethod
    def get_task_name(cls) -> str:
        return "围捕大盗"

    @classmethod
    def get_task_publisher_name(cls) -> str:
        return "守城校尉"

    @classmethod
    def talk_recv_task(cls, wk: Worker) -> bool:
        if wk.find_str_click(*RECT_TALK, "正是", COLOR_TALK_ITEM):
            cls.close_other_talk(wk)
            return True
        return False

    @classmethod
    def is_cur_task_desc(cls, wk: Worker):
        return cls.region_task_desc_find_str(wk, "大盗", COLOR_GOLD)

    @classmethod
    def get_task_setting_count(cls, wk: Worker) -> int:
        return 9999

    @classmethod
    def end_condition(cls, wk: Worker) -> bool:
        if settings.cur_time_stamp < cls.START_TS:
            wk.record("未到开始时间")
            return False
        if settings.cur_time_stamp > cls.END_TS:
            wk.record("已到结束时间")
            return False
        if wk.cfg_plan["领双围捕大盗"] and wk.check_ls:
            wk.check_ls = False
            db_time = wk.cfg_plan["领双时间围捕大盗"]
            wk.record(f"围捕大盗前领双:{db_time}")
            cls.get_double(wk, db_time)
        return True
