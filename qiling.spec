# -*- mode: python ; coding: utf-8 -*-

block_cipher = pyi_crypto.PyiBlockCipher(key='csbt34.ydhl12s')

a = Analysis(['entry.py'],
             pathex=[],
             binaries=[],
             datas=[
                ("biz/res/*.*", "biz/res"),
                ("dll/*", "dll"),
             ],
             hiddenimports=["PySide2.QtXml"],
             hookspath=[],
             runtime_hooks=[],
             excludes=["Cython", "opencv-python", "cv2", "qiniu"],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)
exe = EXE(pyz,
          a.scripts,
          [],
          exclude_binaries=True,
          name="qiling",  # 程序名.exe
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          console=False,
          icon="ui/xxx.ico")
coll = COLLECT(exe,
               a.binaries,
               a.zipfiles,
               a.datas,
               strip=False,
               upx=False,
               upx_exclude=[],
               name='qiling')
