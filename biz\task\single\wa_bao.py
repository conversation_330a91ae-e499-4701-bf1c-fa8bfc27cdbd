from biz.constants.constants import *
from biz.task.__base import TaskBase
from utils import *
from biz.obj.worker import Worker
import settings


class TaskWaBao(TaskBase):
    TASK_NAME = "挖宝图"
    NEED_AVOID_FIGHT = True
    IS_TASK_FIX_EQUIP_BB_ENABLE = True

    @classmethod
    def run(cls, wk: Worker):
        cls.leave_team(wk)
        cls.wa_bao(wk)

    @classmethod
    def wa_bao(cls, wk: Worker):
        cls.open_bag_page(wk)
        cls.click_tidy_up(wk, timeout=300)
        last_use_time = 0
        fail_count = 0
        for i in range(10000000):
            if wk.is_fight:
                cls.fight_operation(wk)
                cls.do_fix_bb(wk)
            cls.fight_ma_zei(wk)
            if (i == 0 or wk.is_stuck) and (
                settings.cur_time_stamp - last_use_time
            ) > 3:
                wk.record("正在使用藏宝图...")
                cls.handle_pass_map(wk)
                if cls.bag_use_item(wk, "九州重宝图.bmp|存_超级藏宝图.bmp"):
                    fail_count = 0
                    last_use_time = settings.cur_time_stamp
                    wk.is_stuck = True
                    msleep(200)
                    cls.click_confirm(wk)
                    cls.close_pages(wk)
                else:
                    fail_count += 1
                    if fail_count >= 2:
                        wk.record("没有藏宝图了，挖宝结束")
                        break
            msleep(800)
            
    @classmethod
    def fight_ma_zei(cls, wk: Worker):
        pass
    
    @classmethod
    def people_run_away(cls, wk: Worker):
        if wk.cfg_plan_task['遇盗墓贼逃跑'] and wk.find_str(*RECT_FIGHT_ENEMY, "盗墓贼", COLOR_RED):
            wk.record('遇到盗墓贼, 逃跑...')
            return cls.click_run_away(wk)
        
    @classmethod
    def get_default_biz_config(cls):
        return {
            '遇盗墓贼逃跑': False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_run_away_dao_mu.setChecked(cls.CONFIG["遇盗墓贼逃跑"])
        
    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["遇盗墓贼逃跑"] = settings.wnd_main.chk_run_away_dao_mu.isChecked()
        super().cfg_save(plan_name)
