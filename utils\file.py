import json
import os

def path_exist(path: str):
	# 路径是否存在
	if os.path.exists(path):
		return True
	return False


def dir_create(dir: str):
	# 创建目录, 不存在才创建
	if not path_exist(dir):
		os.makedirs(dir)


def dir_get_files(dir: str):
	# 获取目录中的文件
	ret = ""
	for root, dirs, files in os.walk(dir):
		ret = files
		break
	return ret


def file_create(path: str, content=""):
    # 创建文件，自动创建不存在的目录
    dir_path = os.path.dirname(path)
    if dir_path and not os.path.exists(dir_path):
        os.makedirs(dir_path)
    with open(path, "w") as f:
        f.write(content)


def file_remove(path: str):
	# 删除文件, 存在才删除
	if path_exist(path):
		os.remove(path)


def file_rename(dir: str, old_file: str, new_file: str):
	# 文件重命名
	try:
		os.rename(f"{dir}\\{old_file}", f"{dir}\\{new_file}")
	except:
		print("file_rename error")
		raise OSError


def file_clear_content(path: str):
	# 清空文件内容, 若没有文件会自动创建文件
	with open(path, "w+") as f:  # 打开文件并将光标置于开头
		f.truncate()  # 截断文件光标后的内容


def file_read_content(path: str, encoding="utf-8") -> str:
	# 读取文件内容
	if not path_exist(path):
		file_create(path)
	content = ""
	f = open(path, "r", encoding=encoding)
	if f:
		try:
			content = f.read()
		except:  # 清空文件
			f.seek(0)  # 移动文件指针到文件开头
			f.truncate()  # 清空文件
		finally:
			f.close()
	return content


def file_read_json(path: str, encoding="utf-8") -> dict:
    # 读取 JSON 文件内容并返回字典
    if not path_exist(path):
        file_create(path)
        return {}  # 如果文件不存在，返回空字典

    content = ""
    with open(path, "r", encoding=encoding) as f:
        try:
            content = f.read()
            return json.loads(content)  # 将 JSON 字符串解析为字典
        except json.JSONDecodeError:
            # 如果 JSON 解析失败，返回空字典
            return {}
        except Exception as e:
            # 处理其他异常
            print(f"读取 JSON 文件时出错: {e}")
            return {}

def file_read_char(path: str, count=4, encoding="utf-8") -> str:
	# 读取文件前几个字符
	if not path_exist(path):
		return ""
	with open(path, 'r', encoding=encoding) as file:
		first_four_characters = file.read(count)
	return first_four_characters

def file_append_content(path: str, content: str):
	# 添加文件内容, 若没有文件会自动创建文件
	with open(path, "a", encoding="utf-8") as f:
		f.write(content)


def remove_first_1000_lines_inplace(filename: str):
	# 定义临时文件名
	temp_filename = filename + ".temp"
	# 打开原文件和临时文件
	with open(filename, "r", encoding="utf-8", errors='ignore') as input_file, \
		open(temp_filename, "w", encoding="utf-8") as temp_file:
		# 跳过前1000行
		for _ in range(1000):
			try:
				next(input_file)
			except StopIteration:
				break
		# 将剩余的内容写入临时文件
		for line in input_file:
			temp_file.write(line)
	# 删除原文件
	os.remove(filename)
	# 将临时文件重命名为原文件名
	os.rename(temp_filename, filename)


# json文件 -> py字典
def json_file_to_dict(path_cfg: str):
	try:
		with open(path_cfg, "r", encoding="utf-8") as f:
			cfg_load = json.load(f) or {}
	except Exception as e:  # 文件不存在会异常
		cfg_load = {}
	return cfg_load


# json字符串 -> py字典
def json_str_to_dict(json_str: str):
	try:
		cfg_load = json.loads(json_str)
	except:
		cfg_load = {}
	return cfg_load


# py对象 -> json文件
def dict_to_json_file(py_dict: dict, path_cfg: str):
	try:
		with open(path_cfg, "w", encoding="utf-8") as f:
			json.dump(py_dict, f, ensure_ascii=False, sort_keys=True, indent=4)
	except:
		print(f"json encode error! {py_dict} {path_cfg}")


# py对象 -> json字符串
def dict_to_json_str(py_dict: dict):
	try:
		json_str = json.dumps(py_dict, ensure_ascii=False, sort_keys=True)
	except:
		print(f"json encode error! {py_dict}")
		json_str = "{}"
	return json_str
