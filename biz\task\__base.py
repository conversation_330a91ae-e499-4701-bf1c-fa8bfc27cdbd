from typing import Callable
from biz.exception import TaskFinalStatusException
from biz.team.team import Team
from biz.obj.worker import Worker
from utils import *
from biz.constants.constants import *
import settings


class TaskBase:
    TASK_NAME = ""
    IS_ACTIVITY = False
    IS_FREE = False
    IS_VALUE = False
    VALUE_ID = 0
    NEED_AVOID_FIGHT = False
    IS_DIFFICULT_TASK = False
    IS_TASK_FIX_EQUIP_BB_ENABLE = False
    IS_CALL_BB_ENABLE = False
    NEED_ANSWER_QUESTION = False
    IS_TEAM_TASK = False
    RANDOM_FIGHT_ENEMY = True
    SEAL_MAIN_FIRST = True  # 优先封主
    NEED_PLAYER_NAME = True
    CONFIG = {}
    AUTH_PIC = ""
    DEFEND_BB_PICS = "bb_四袋.bmp|bb_霸下.bmp|bb_丹青.bmp|bb_杨过.bmp|bb_小龙女.bmp|bb_都统.bmp|bb_坚竹.bmp|bb_白龙.bmp|bb_映雪.bmp|bb_骠骑.bmp|bb_风雪.bmp|bb_兔子.bmp"

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        if cls.TASK_NAME:
            settings.task_dict[cls.TASK_NAME] = cls
            biz_config = cls.get_default_biz_config()
            if biz_config:
                settings.default_cfg_plan[cls.TASK_NAME] = biz_config

    @classmethod
    def before_run(cls, wk: Worker):
        # 重置任务类型和完成次数
        cls.set_task_config(wk)
        wk.done_count = 0
        wk.fail_count = 0
        wk.check_ls = True
        if cls.IS_TEAM_TASK and cls.TASK_NAME != "队员挂机":
            wk.team.reset_all_signal()
            wk.team.task_name = cls.TASK_NAME
            wk.team.is_difficult_task = cls.IS_DIFFICULT_TASK
            wk.team.is_switch_primary_bb_enable = cls.IS_DIFFICULT_TASK
            wk.team.IS_TASK_FIX_EQUIP_BB_ENABLE = cls.IS_TASK_FIX_EQUIP_BB_ENABLE
            wk.team.is_call_bb_enable = cls.IS_CALL_BB_ENABLE
            wk.team.seal_main_first = cls.SEAL_MAIN_FIRST

        cls.close_pages(wk)
        cls.cancel_auto(wk)
        # 在战斗就等战斗结束
        if cls.check_fight_condition(wk):
            cls.fight_operation(wk)
        elif cls.NEED_AVOID_FIGHT:
            cls.use_xmx(wk)
        wk.is_stuck = True
        wk.should_run_away = False
        wk.cur_task_start_time_fmt = settings.cur_time_fmt
        wk.record("开始执行")

    @classmethod
    def check_run(cls, wk: Worker):
        if not cls.IS_FREE and settings.is_free:
            # 未付费用户 使用收费功能
            return False
        if cls.IS_VALUE and ((2**cls.VALUE_ID) & settings.extra_rights) == 0:
            # 没开通增值服务的用户 使用增值功能
            return False
        return True

    @classmethod
    def run(cls, wk: Worker):
        raise Exception("子类必须实现该方法")

    @classmethod
    def after_run(cls, wk: Worker):
        msleep(500)
        cls.close_pages(wk)
        wk.record("执行完成")

    @classmethod
    def check_fight_condition(cls, wk: Worker):
        return wk.is_fight

    @classmethod
    def exit_game(cls, wk: Worker):
        cls.open_system_page(wk)
        if wk.find_pic_click(*RECT_FULL, "退出游戏.bmp", timeout=400):
            wk.record("点击退出游戏成功")
            cls.click_confirm(wk, timeout=300)
        else:
            wk.record("点击退出游戏失败")

    @classmethod
    def return_login(cls, wk: Worker):
        cls.open_system_page(wk)
        if wk.find_pic_click(*RECT_FULL, "返回登录.bmp|返回登录2.bmp", timeout=400):
            wk.record("点击返回登录成功")
            cls.click_confirm(wk, timeout=300)
        else:
            wk.record("点击返回登录失败")

    @classmethod
    def check_the_week(cls, wk: Worker):
        return True

    @classmethod
    def set_task_config(cls, wk: Worker, specify_task_name=""):
        wk.cur_task = specify_task_name or cls.TASK_NAME
        wk.sub_task = ""
        wk.cur_task_cls = cls
        wk.cfg_plan_task = cls.get_default_biz_config()
        wk.cfg_plan_task.update(wk.cfg_plan.get(cls.TASK_NAME, {}))

    @classmethod
    def get_default_biz_config(cls):
        return {}

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件 -> 控件
        cls.CONFIG = cls.get_default_biz_config()
        # if cls.TASK_NAME == "拦截商旅":
        #     print(f"plan_name: {plan_name}, cls:{cls}, cls.CONFIG:{cls.CONFIG}")
        cls.CONFIG.update(
            settings.cfg_plan_dict.get(plan_name, {}).get(cls.TASK_NAME, {})
        )
        # if cls.TASK_NAME == "拦截商旅":
        #     print(f"after cls.CONFIG:{cls.CONFIG}")

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件 -> 文件
        if plan_name == "":
            settings.cur_cfg_plan[cls.TASK_NAME] = cls.CONFIG
        else:
            plan_cfg = settings.cfg_plan_dict.get(plan_name, {})
            plan_cfg[cls.TASK_NAME] = cls.CONFIG

    # ========================== 下面是一些通用的业务逻辑方法 ==========================

    @classmethod
    def is_fight(cls, wk: Worker):
        POS_LOADING = (440, 285)
        POS_BAG = (569, 596)
        if wk.get_pos_color(*POS_LOADING) == COLOR_BLACK:  # 地图加载中是黑色
            return False
        return wk.get_pos_color(*POS_BAG) == "5b585c"

    @classmethod
    def is_people_action(cls, wk: Worker, timeout=0):
        return wk.find_pic(
            *RECT_PEOPLE_ACTION, "界_战斗_人物.bmp|界_战斗_人物2.bmp", timeout=timeout
        )

    @classmethod
    def is_bb_action(cls, wk: Worker, timeout=0):
        return wk.find_multi_color(
            *RECT_RIGHT, MCOLOR_BB_ACTION
        )

    @classmethod
    def is_avoid_fight(cls, wk: Worker):
        return cls.is_have_buff(wk, "熊猫香")

    @classmethod
    def is_have_buff(cls, wk: Worker, buff_name: str, timeout=0):
        if wk.find_str(*RECT_LEFT_TOP, "人物属性|血量|内力|气势", COLOR_WHITE):
            wk.move_to(40, 40)
            wk.move_relative(1, 1)
            msleep(400)
            wk.re_move()
            msleep(200)
        return wk.find_pic(*RECT_BUFF, f"buff_{buff_name}.bmp", timeout=timeout)

    @classmethod
    def in_abnormal_fight(cls, wk: Worker):
        return not wk.is_fight and wk.find_pic(*RECT_RUN_AWAY, "撤退.bmp|撤退2.bmp")

    @classmethod
    def click_run_away(cls, wk: Worker):
        return wk.find_pic_click(*RECT_RUN_AWAY, "撤退.bmp|撤退2.bmp")

    @classmethod
    def is_system_broad_show(
            cls, wk: Worker, msg: str, color=COLOR_RED, timeout=0
    ):
        return wk.find_str(*RECT_SYSTEM_BROADCAST, msg, color, timeout=timeout)

    @classmethod
    def is_popup_show_info(
            cls, wk: Worker, msg: str, color=COLOR_BLACK, timeout=0
    ):
        return wk.find_str(*RECT_POPUP, msg, color, timeout=timeout)

    @classmethod
    def throw_things(cls, wk: Worker, additional_pics=""):
        wk.record("开始丢东西...")
        cls.stop_auto_find_way(wk, force=True)
        cls.open_bag_page(wk)
        x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
        if wk.is_fight:
            return False
        if x < 0:
            return False
        wk.re_move()  # 鼠标移开避免挡到
        begin_x, begin_y = x + 18, y - 300
        RECT_BAG = (begin_x, begin_y, begin_x + 260, begin_y + 300)
        throw_pic = wk.match_pic("丢_*.bmp")
        if additional_pics:
            throw_pic += "|" + additional_pics

        res = False

        def throw_cur_page_things():
            nonlocal res
            for i in range(20):
                cls.close_other_talk(wk)
                # 先确保卸下之前点了的物品
                wk.move_r_click(begin_x-10, begin_y+150, re_move=False)
                if not wk.find_pic_click(
                        *RECT_BAG, throw_pic
                ):
                    break
                res = True
                if wk.find_pic_click(*RECT_FULL, "清除.bmp", timeout=300, re_move=False):
                    if cls.click_confirm(wk, RECT=RECT_RIGHT):
                        time.sleep(0.3)
                    cls.click_confirm(wk)
                time.sleep(0.3)

        pos_list = cls.get_bag_page_pos_list(wk, RECT_FULL, timeout=300)
        for pos in pos_list:
            _, x, y = pos
            wk.move_click(x, y)  # 换页
            time.sleep(0.2)
            throw_cur_page_things()
        # 最后再确保卸下之前点了的物品
        wk.move_r_click(begin_x-10, begin_y+150, re_move=False)
        time.sleep(0.2)
        cls.close_pages(wk)
        wk.record("丢东西完成")
        return res

    @classmethod
    def throw_baggage(cls, wk: Worker, thow_equip=False):
        additional_pics = "金针.bmp|玲珑生肌膏.bmp|静意碎片.bmp|考题卷.bmp|小增寿丹.bmp|跌打白药.bmp|金创药.bmp|生津弥灵膏.bmp"
        if thow_equip:
            additional_pics += "|" + wk.match_pic("装备_*.bmp")
        cls.throw_things(wk, additional_pics=additional_pics)

    @classmethod
    def get_goods_price(cls, wk: Worker, thing_name: str):
        price_dict = settings.cfg_common["商品名称价格表"]
        return price_dict.get(thing_name, 0)

    @classmethod
    def get_popup_info(cls, wk: Worker, timeout=0):
        if not cls.is_popup_confirm_show(wk):
            return ""
        return wk.ocr(*RECT_POPUP, COLOR_BLACK, timeout=timeout)

    @classmethod
    def pause_leave_team(cls, wk: Worker):
        if not cls.get_teammate_count(wk):
            return
        wk.record("正在暂离队伍...")
        cls.open_team_page(wk)
        if wk.find_pic_click(*RECT_FULL, "暂时离队.bmp|暂时离队2.bmp", timeout=600, re_move=False):
            wk.record("暂离队伍成功")
        else:
            wk.record("暂离队伍失败")
        cls.close_pages(wk)

    @classmethod
    def leave_team(cls, wk: Worker):
        if cls.check_fight_condition(wk):
            cls.fight_operation(wk)
        if not cls.get_teammate_count(wk):  # 如果是九里桥里面, 队长离队左边就没了
            return
        wk.record("正在离开队伍...")
        cls.open_team_page(wk)
        if wk.find_pic_click(*RECT_FULL, "离开队伍.bmp|离开队伍2.bmp", timeout=600, re_move=False):
            wk.record("离开队伍成功")
        else:
            wk.record("离开队伍失败")
        cls.close_pages(wk)

    @classmethod
    def back_to_team(cls, wk: Worker):
        if cls.check_fight_condition(wk):
            return
        if not wk.mate_check_leave:
            return
        if not cls.is_team_mate_pause_leave(wk):
            return
        cls.close_other_talk(wk)
        cls.open_team_page(wk)
        x, y = wk.get_pic_pos(*RECT_FULL, "离开队伍.bmp|离开队伍2.bmp")
        if x < 0:
            wk.record("队伍界面打开失败")
            return
        if not cls.is_self_pause_leave(wk):
            cls.close_pages(wk)
            return
        wk.record("正在归队...")
        # 识别队长的线路
        line = wk.ocr(x-60, y-220, x+55, y-197, COLOR_TEAM_SERVER)
        if line != "":
            line = line[0]
            cls.close_pages(wk)
            wk.record(f"队长在{line}线, 需要换线...")
            cls.switch_line(wk, line, leave_team=False)
        for _ in range(1000):
            cls.close_pages(wk)
            # 然后再打开 队伍 页面
            cls.open_team_page(wk)
            # 识别队长的地图
            leader_map = wk.ocr(x-60, y-220, x+55, y-197,
                                COLOR_BLACK).rstrip("_一")
            if leader_map:
                mate_map = cls.cur_map(wk)
                if mate_map != leader_map:
                    wk.record(f"当前地图:{mate_map}, 跑到队长地图:{leader_map}...")
                    if mate_map in WILD_MAP_LIST and leader_map in SAFE_MAP_LIST:
                        wk.key_press(VK_F8)
                        msleep(600)
                    cls.close_pages(wk)
                    cls.run_to_map_run_away(wk, leader_map)
                else:  # 地图相同, 点击归队
                    wk.record("来到了队长所在地图, 点击归队")
                    wk.find_pic_click(
                        *RECT_FULL, "回归队伍.bmp|回归队伍2.bmp", timeout=400)
                    wk.record("归队成功")
                    wk.mate_check_leave = False  # 成功归队就不用检查了
                    break
            msleep(600)
        cls.close_pages(wk)

    @classmethod
    def switch_line(cls, wk: Worker, line: str, leave_team=True) -> bool:
        wnd_title = wk.get_window_title(wk.hwnd)
        if f"{line}线" in wnd_title:
            wk.record(f"当前就在{line}线, 不需要换线")
            return True
        wk.sub_task = "换线"
        if leave_team:
            cls.leave_team(wk)
        msleep(400)
        if not cls.open_switch_line(wk):
            wk.sub_task = ""
            return
        color = "|".join([COLOR_SERVER_FREE, COLOR_SERVER_BUSY])
        for _ in range(40):
            msleep(600)
            if wk.find_str_db_click(*RECT_FULL, f"{line}线", color, sim=1.0):
                wk.record(f"点击换线：{line}线")
                break
        for _ in range(40):
            msleep(600)
            if not wk.find_str(*RECT_POPUP, "正在登陆中", COLOR_LOADING):
                break
        wk.sub_task = ""

    @classmethod
    def lead_team_switch_line(cls, wk: Worker, line='一'):
        if not wk.cfg_plan["带队换线"]:
            return
        # 换线
        wk.team.signal_switch_line.leader_set(line)
        cls.switch_line(wk, line)
        wk.team.signal_switch_line.mate_resp(wk.row)
        # 组队
        cls.make_team(wk)

    @classmethod
    def is_switch_line_open(cls, wk: Worker) -> bool:
        return wk.find_pic(*RECT_LEFT, "登录确定.bmp")

    @classmethod
    def open_switch_line(cls, wk: Worker) -> bool:
        for i in range(5):
            if cls.is_switch_line_open(wk):
                return True
            else:
                wk.key_press_combo(VK_ALT, VK_V)
            msleep(600)
        return False

    @classmethod
    def fix_equip(cls, wk: Worker):
        if wk.cfg_plan["修理忠诚方式"] == '全局禁用':
            return
        cls.back_to_kai_feng(wk)
        wk.record("正在修理装备...")
        if cls.cur_map(wk) != "开封":
            wk.record("回到开封失败, 取消修理装备")
            return
        if not cls.talk_with_cur_map_npc(wk, "程小弟", ["修理全部|修理", "确定|修理全部", "确定"]):
            wk.record("修理失败")
            return
        wk.record("修理成功")
        cls.close_other_talk(wk)
        cls.click_close_pic(wk)

    @classmethod
    def fix_bb(cls, wk: Worker):
        if wk.cfg_plan["修理忠诚方式"] == '全局禁用':
            return
        cls.back_to_kai_feng(wk)
        wk.record("正在教化BB...")
        if cls.cur_map(wk) != "开封":
            wk.record("回到开封失败, 取消教化BB")
            return
        if not cls.talk_with_cur_map_npc(wk, "开封随从教员", ["教化随从", "教化全部", "给我教化"]):
            return
        cls.talk_with_cur_map_npc(wk, "开封随从教员", ["治疗随从", "治疗全部", "治疗我的"])
        wk.record("教化治疗成功")
        cls.close_other_talk(wk, timeout=400)
        cls.click_close_pic(wk)
        # 出随从房间的小门, 避免后面创建队伍失败
        cls.big_map_click(wk, 647 + rnd(-2, 2), 173 + rnd(-2, 2))

    @classmethod
    def people_use_skill(cls, wk: Worker):
        if not wk.cfg_plan["人物使用技能"]:
            return
        if wk.cfg_plan["人宠使用技能"] == "自动判断" and not cls.is_difficult_task(wk):
            return
        if not cls.is_people_action(wk):
            return
        if cls.is_system_broad_show(wk, "没有足够的物品"):
            wk.record("人物技能使用失败, 没有足够的物品")
            return
        if wk.pf_round and wk.cur_round == wk.pf_round + 1 and cls.is_have_buff(wk, "破釜"):
            wk.record("上回合用了破釜, 为避免浪费, 人物跳过护心和刺穴")
            return
        if wk.cfg_plan["人物护心F6"] and cls.is_fight_self_need_hu_xin(wk):
            if wk.find_pic(*RECT_SKILL, "技能图标_护心.bmp"):
                if cls.use_skill_to_self(wk, "护心"):
                    wk.record("血内低于设定阈值, 人物使用护心成功")
                    return
            else:
                wk.record("人物使用护心失败,技能栏F6未放置护心")
        if wk.cfg_plan["人物刺穴F7"]:
            # 如果勾选了刺穴未命中继续, 要识别怪是否被刺中
            if wk.cfg_plan["人物未命中继续刺"]:
                if wk.find_pic(*RECT_SKILL, "技能图标_刺穴.bmp"):
                    if not cls.is_enemy_hit_zhang_ai_meet(wk):
                        wk.record("刺穴命中百分比未达标, 继续刺穴")
                        return cls.use_ci_xue_shi_hou(wk)
                if wk.find_pic(*RECT_SKILL, "技能图标_狮吼.bmp"):
                    if not cls.is_enemy_hit_zhang_ai_meet(wk):
                        wk.record("狮吼命中百分比未达标, 继续狮吼")
                        return cls.use_ci_xue_shi_hou(wk)
            # 检查是否到了释放障碍技能的回合数
            if wk.cur_round % wk.cfg_plan["人物技能使用频率"] != 1:
                return
            # 正常触发刺穴狮吼
            if cls.use_ci_xue_shi_hou(wk):
                return True
        cls.cancel_use_skill(wk)

    @classmethod
    def is_enemy_hit_zhang_ai_meet(cls, wk: Worker):
        return True

    @classmethod
    def is_enemy_hit_lsgs(cls, wk: Worker):
        return False

    @classmethod
    def use_ci_xue_shi_hou(cls, wk: Worker):
        if wk.find_pic(*RECT_SKILL, "技能图标_刺穴.bmp") and cls.use_skill_to_enemy(wk, "刺穴"):
            return True
        if wk.find_pic(*RECT_SKILL, "技能图标_狮吼.bmp") and cls.use_skill_to_enemy(wk, "狮吼"):
            return True
        return False

    @classmethod
    def people_fight_action(cls, wk: Worker):
        if not cls.is_people_action(wk):
            return
        wk.right_click()  # 先取消下避免之前有技能没按出来
        if wk.cfg_plan["人物战斗"] == "攻":
            wk.record("人物战斗: 攻")
            wk.key_press_combo(VK_ALT, VK_A)
        else:
            wk.record("人物战斗: 防")
            wk.key_press_combo(VK_ALT, VK_D)
        wk.re_move()

    @classmethod
    def bb_fight_action(cls, wk: Worker):
        if not cls.is_bb_action(wk):
            return
        wk.right_click()  # 先取消下避免之前有技能没按出来
        if wk.cfg_plan["BB攻防"] == "自动判断":
            if wk.is_defend_bb:
                wk.record("BB战斗: 防")
                wk.key_press_combo(VK_ALT, VK_D)
            else:
                wk.record("BB战斗: 攻")
                wk.key_press_combo(VK_ALT, VK_A)
        elif wk.cfg_plan["BB攻防"] == "攻":
            wk.record("BB战斗: 攻")
            wk.key_press_combo(VK_ALT, VK_A)
        elif wk.cfg_plan["BB攻防"] == "防":
            wk.record("BB战斗: 防")
            wk.key_press_combo(VK_ALT, VK_D)
        wk.re_move()

    @classmethod
    def recognize_enemy(cls, wk: Worker):
        cls.get_cur_enemy_count(wk)

    @classmethod
    def get_action_str(cls, wk: Worker, action: int):
        if action == 1:
            return "补红"
        elif action == 2:
            return "补蓝"
        elif action == 3:
            return "补双加"

    @classmethod
    def people_fight_save(cls, wk: Worker):
        if not wk.cfg_plan["人物战斗救人"] and not wk.cfg_plan["人物战斗救BB"]:
            return
        if not cls.is_enemy_count_less_than_people_setting(wk):
            return
        need_sleep = True
        # 救人
        if wk.cfg_plan["人物战斗救人"]:
            action_list = cls.is_fight_teammate_need_red_blue(
                wk, from_obj=SAVE_PEOPLE, save_obj=SAVE_PEOPLE
            )
            for action, action_idx in action_list:
                action_str = cls.get_action_str(wk, action)
                wk.record(f"人物需要救队友 {action_idx}, 操作为 {action_str}")
                if cls.fight_use_drug(wk, action, action_idx, save_obj=SAVE_PEOPLE, need_sleep=need_sleep):
                    return True
                need_sleep = False
        # 救BB
        if wk.cfg_plan["人物战斗救BB"]:
            action_list = cls.is_fight_teammate_need_red_blue(
                wk, from_obj=SAVE_PEOPLE, save_obj=SAVE_BB
            )
            for action, action_idx in action_list:
                action_str = cls.get_action_str(wk, action)
                wk.record(f"人物需要救BB {action_idx}, 操作为{action_str}")
                if cls.fight_use_drug(wk, action, action_idx, save_obj=SAVE_BB):
                    return True
                need_sleep = False
        # 没有药，取消使用道具
        if not need_sleep:
            wk.right_click()  # 取消使用道具
            wk.right_click()  # 取消使用道具
            msleep(300)
        return False

    @classmethod
    def bb_fight_save(cls, wk: Worker):
        # print("wk.is_defend_bb:", wk.is_defend_bb)
        is_save_people = wk.cfg_plan["防守类BB战斗救人"] if wk.is_defend_bb else wk.cfg_plan["进攻类BB战斗救人"]
        is_save_bb = wk.cfg_plan["防守类BB战斗救BB"] if wk.is_defend_bb else wk.cfg_plan["进攻类BB战斗救BB"]
        if not is_save_people and not is_save_bb:
            return
        if not cls.is_enemy_count_less_than_bb_setting(wk):
            return
        need_sleep = True
        # 救人
        if is_save_people:
            action_list = cls.is_fight_teammate_need_red_blue(
                wk, from_obj=SAVE_BB, save_obj=SAVE_PEOPLE
            )
            for action, action_idx in action_list:
                action_str = cls.get_action_str(wk, action)
                wk.record(f"BB需要救人{action_idx}, 操作为 {action_str}")
                if cls.fight_use_drug(wk, action, action_idx, save_obj=SAVE_PEOPLE, need_sleep=need_sleep):
                    return True
                need_sleep = False
        # 救BB
        if is_save_bb:
            action_list = cls.is_fight_teammate_need_red_blue(
                wk, from_obj=SAVE_BB, save_obj=SAVE_BB
            )
            for action, action_idx in action_list:
                action_str = cls.get_action_str(wk, action)
                wk.record(f"BB需要救BB{action_idx}, 操作为 {action_str}")
                if cls.fight_use_drug(wk, action, action_idx, save_obj=SAVE_BB, need_sleep=need_sleep):
                    return True
                need_sleep = False
        # 没有药，取消使用道具
        if not need_sleep:
            wk.right_click()  # 取消使用道具
            wk.right_click()  # 取消使用道具
            msleep(300)
        return False

    @classmethod
    def is_enemy_count_less_than_people_setting(cls, wk: Worker):
        if wk.cfg_plan["人物救人宠前提"]:
            setting_count = wk.cfg_plan["人物拉药怪数"]
            cur_count = len(wk.survive_enemy_pos_list)
            if cur_count == 0:
                return True
            if cur_count >= setting_count:
                return False
            # wk.record(f"当前怪数目:{cur_count} < 设定人物拉药怪数:{setting_count}, 满足救人宠条件")
        return True

    @classmethod
    def is_enemy_count_less_than_bb_setting(cls, wk: Worker):
        if wk.cfg_plan["BB救人宠前提"]:
            setting_count = wk.cfg_plan["BB拉药怪数"]
            cur_count = len(wk.survive_enemy_pos_list)
            if cur_count == 0:
                return True
            # wk.record(f"设定BB拉药怪数:{setting_count}, 当前怪数目:{cur_count}")
            if cur_count >= setting_count:
                return False
            # wk.record(f"当前怪数目:{cur_count} < 设定人物拉药怪数:{setting_count}, 满足救人宠条件")
        return True

    @classmethod
    def fight_use_drug(
            cls, wk: Worker, action: str, idx: int, save_obj=SAVE_PEOPLE, need_sleep=True
    ):
        obj_name = "队友" if save_obj == SAVE_PEOPLE else "BB"
        # 开始使用药
        wk.find_pic_click(*RECT_RIGHT, "道具.bmp|道具2.bmp")
        if need_sleep:
            msleep(600)
        x, y = POS_MATE_LIST[idx] if save_obj == SAVE_PEOPLE else POS_BB_LIST[idx]
        y = y - rnd(20, 30)
        if action == ACTION_BLOOD + ACTION_INTERNAL:  # 血蓝都要加
            wk.record(f"给{idx}号{obj_name}加血蓝...")
            if wk.find_pic_click(
                    *RECT_DAOJU,
                    "血蓝药4.bmp|血蓝药3.bmp|血蓝药2.bmp|血蓝药1.bmp",
                    order=ORDER_RLDU,
                    re_move=False,
                    timeout=200,
            ):
                wk.move_to(x, y)
                wk.move_relative(rnd(1, 2), rnd(1, 2))
                msleep(50, 100)
                wk.left_click()
                wk.record(f"给{idx}号{obj_name}加血蓝成功")
                return True
            wk.record("没找到血蓝药")

        def add_blood():
            if action & ACTION_BLOOD:  # 加血
                wk.record(f"给{idx}号{obj_name}加血...")
                if wk.find_pic_click(
                        *RECT_DAOJU,
                        "血药9.bmp|血药8.bmp|血药7.bmp|血药6.bmp|血药5.bmp|血药4.bmp|血药3.bmp|血药2.bmp|血药1.bmp|血蓝药4.bmp|血蓝药3.bmp|血蓝药2.bmp|血蓝药1.bmp",
                        order=ORDER_RLDU,
                        re_move=False,
                        timeout=200,
                ):
                    wk.move_to(x, y)
                    wk.move_relative(rnd(1, 2), rnd(1, 2))
                    msleep(50, 100)
                    wk.left_click()
                    wk.record(f"给{idx}号{obj_name}加血成功")
                    return True
                wk.record("没找到血药")
                return False

        def add_internal():
            if action & ACTION_INTERNAL:  # 加蓝
                wk.record(f"给{idx}号{obj_name}加蓝...")
                if wk.find_pic_click(
                        *RECT_DAOJU,
                        "蓝药9.bmp|蓝药8.bmp|蓝药7.bmp|蓝药6.bmp|蓝药5.bmp|蓝药4.bmp|蓝药3.bmp|蓝药2.bmp|蓝药1.bmp|血蓝药4.bmp|血蓝药3.bmp|血蓝药2.bmp|血蓝药1.bmp",
                        order=ORDER_RLDU,
                        re_move=False,
                        timeout=200,
                ):
                    wk.move_to(x, y)
                    wk.move_relative(rnd(1, 2), rnd(1, 2))
                    msleep(50, 100)
                    wk.left_click()
                    wk.record(f"给{idx}号{obj_name}加蓝成功")
                    return True
                wk.record("没找到蓝药")
                return False

        if rnd(0, 1):  # 加血蓝随机
            if add_blood():
                return True
            if add_internal():
                return True
        else:
            if add_internal():
                return True
            if add_blood():
                return True
        return False

    @classmethod
    def get_enemy_color_list(cls, wk: Worker):
        return [COLOR_GREEN, COLOR_GOLD]

    @classmethod
    def get_cur_enemy_count(cls, wk: Worker):
        color_list = cls.get_enemy_color_list(wk)
        color = "|".join(color_list)
        wk.survive_enemy_pos_list = []
        for x, y in POS_ENENMY_LIST:
            if wk.find_color(x-7, y-95, x+7, y-81, color, order=ORDER_RLDU):
                # 把这些存活敌人的坐标存储下来
                wk.survive_enemy_pos_list.append((x, y))
        # print(f"wk.survive_enemy_pos_list: {wk.survive_enemy_pos_list}")
        # print(f'当前怪数:{len(wk.survive_enemy_pos_list)}')
        return len(wk.survive_enemy_pos_list)

    @classmethod
    def cancel_auto(cls, wk: Worker):
        return wk.find_pic_click(*RECT_FULL, "取消自动.bmp|取消自动2.bmp")

    @classmethod
    def init_fight_flag(cls, wk: Worker):
        wk.player_idx = -1
        wk.boss_wu_xing_idx = -1
        wk.fight_e_ren = False
        wk.team.fight_random = False
        wk.team.true_enemy_pos = (-1, -1)
        wk.fight_yan = False
        wk.fight_start_ts = settings.cur_time_stamp
        wk.cur_round = 1
        wk.is_called_bb = False
        wk.fight_meet_bb = False
        wk.fight_meet_name = False
        wk.pf_round = 0
        wk.jzz_round = 0
        wk.tbs_round = 0
        wk.hx_round = 0
        wk.not_hit_ci_xue_pos = (-1, -1)  # 还没命中刺穴的怪物位置
        wk.mate_check_leave = True  # 每一次战斗完都要检查是不是离队了
        wk.is_die = False

    @classmethod
    def fight_operation(cls, wk: Worker, check_xmx=True):
        if wk.find_pic_click(*RECT_ZH_FIGHT_CLOSE, "战魂关闭1.bmp|战魂关闭2.bmp", order=ORDER_RLUD):
            wk.record("发现卡战魂战斗页了")
            return
        wk.record("进入战斗")
        cls.init_fight_flag(wk)
        wk.find_pic_drag_to(*RECT_FULL, "五行轮盘.bmp", *
                            POS_WUXING_LUNPAN, timeout=200)
        cls.cancel_auto(wk)
        last_add_round_ts = 0
        should_add_round = False
        while wk.is_fight:
            cls.fight_do_something(wk)
            cls.fight_close_talk(wk)
            if cls.is_people_action(wk):
                wk.check_pf_round = False
                should_add_round = True
                cls.click_close_pic(wk)
                cls.get_self_name_in_team_idx(wk, timeout=200)
                cls.recognize_enemy(wk)
                wk.record(f"第{wk.cur_round}回合")
                cls.call_bb(wk)  # 唤出BB
                if cls.people_fight_save(wk):  # 救
                    # 用完药有时还恢复到人物操作, 延迟等客户端刷新一下
                    cls.is_bb_action(wk, timeout=600)
                    continue
                cls.people_catch_bb(wk)  # 捉麒麟
                if cls.people_run_away(wk):  # 撤退
                    msleep(600)
                    continue
                if cls.people_use_skill(wk):  # 人物使用技能
                    cls.is_bb_action(wk, timeout=600)
                    continue
                cls.people_fight_action(wk)  # 正常人物操作
                msleep(200)
            timeout = 500 if should_add_round else 0
            if cls.is_bb_action(wk, timeout=timeout):
                wk.is_defend_bb = cls.is_defend_bb(wk)
                cls.bb_use_jzz_tbs(wk)  # BB用金钟罩/铁布衫
                cls.bb_fight_save(wk)  # 救
                cls.bb_use_skill(wk)  # BB使用技能
                cls.bb_fight_action(wk)  # 正常BB操作
                msleep(1000)
            # 增加回合数, 加个时间避免错误增加环数
            if should_add_round and settings.cur_time_stamp - last_add_round_ts > 2:
                wk.cur_round += 1
                last_add_round_ts = settings.cur_time_stamp
                should_add_round = False
            msleep(400)
        wk.record("战斗结束")
        cls.after_fight_supply(wk)
        if check_xmx:
            cls.after_fight_use_xmx(wk)
        cls.after_fight_rest_call_bb(wk)
        cls.after_fight_switch_primary_bb(wk)
        wk.is_stuck = True

    @classmethod
    def guard_do_something(cls, wk: Worker):
        if rnd(0, 5) != 1:
            return
        if wk.find_pic_click(*RECT_XPAGE, "商城离开.bmp|商城离开2.bmp|新区活动离开.bmp|新区活动离开2.bmp"):
            wk.record("离开商城")
        if wk.find_pic_click(*RECT_XPAGE, "充值关闭.bmp"):
            wk.record("离开充值页")
        if wk.find_pic_click(*RECT_XPAGE, "天命关闭.bmp"):
            wk.record("离开天命页")
        if cls.is_team_platform_page_open(wk):
            wk.record("关闭组队平台")
            cls.close_pages(wk)

    @classmethod
    def after_fight_rest_call_bb(cls, wk: Worker):
        pass

    @classmethod
    def call_bb(cls, wk: Worker):
        pass

    @classmethod
    def do_call_bb(cls, wk: Worker, bb_name: str):
        wk.is_called_bb = True
        if not wk.find_pic_click(*RECT_CALL_BB, "唤出.bmp|唤出2.bmp"):
            wk.record("没点到唤出按钮")
            return
        wk.record("正在唤出BB...")
        x, y = wk.get_pic_pos(*RECT_CALL_BB_LIST, "上.bmp", timeout=200)
        if x > 0:
            print('点上!!!!!!!!!!!!!!!!!!')
            wk.move_click(x, y, 10)
        for i in range(2):
            if wk.find_str_click(*RECT_CALL_BB, bb_name, COLOR_ENABLE, timeout=200):
                break
            if i == 1:
                wk.record(f"唤出失败, 未找到配置的BB名字:{bb_name}")
                wk.right_click()
                return
            x, y = wk.get_pic_pos(*RECT_CALL_BB_LIST, "下.bmp", timeout=200)
            # print(f"下: {x}, {y}")
            if x > 0:
                wk.record("继续往下翻...")
                wk.move_click(x, y, 8)
                msleep(400)
        wk.record(f"唤出{bb_name}成功")
        msleep(400)

    @classmethod
    def do_rest_bb(cls, wk: Worker):
        wk.record("正在休息当前BB...")
        cls.close_other_talk(wk)
        cls.open_bb_page(wk)
        x, _ = wk.get_pic_pos(*RECT_FULL, "界_BB.bmp")
        if x < 0:
            wk.record("休息BB失败, 未能打开随从界面")
            return
        wk.find_pic_click(*RECT_FULL, "bb休息.bmp")
        cls.close_bb_page(wk)
        wk.record("休息BB完成")

    @classmethod
    def get_self_name_in_team_idx(cls, wk: Worker, timeout=0):
        if wk.player_idx != -1:  # 已经识别到了就直接返回
            return
        if cls.get_teammate_count(wk) <= 1:
            wk.player_idx = 0
            return
        player_name_x, player_name_y = wk.get_str_pos(
            *RECT_FIGHT_SELF, wk.player_name, COLOR_WHITE+"|"+COLOR_NAME_VIP, zk=ZK_NAME_11, timeout=timeout)
        if player_name_y <= 0 and len(wk.player_name) > 1:
            player_name_x, player_name_y = wk.get_str_pos(
                *RECT_FIGHT_SELF, wk.player_name[1:], COLOR_WHITE+"|"+COLOR_NAME_VIP, zk=ZK_NAME_11)
        if player_name_y > 0:  # 识别出自己是队伍中的几号队员
            if player_name_x >= POS_MATE_LIST[2][0]:
                wk.player_idx = 4
            elif player_name_x >= POS_MATE_LIST[0][0]:
                wk.player_idx = 2
            elif player_name_x >= POS_MATE_LIST[1][0]:
                wk.player_idx = 0
            elif player_name_x >= POS_MATE_LIST[3][0]:
                wk.player_idx = 1
            else:
                wk.player_idx = 3
        # wk.record(f"自己是队伍中的第{wk.player_idx}号队员")
        
    @classmethod
    def fight_check_pf(cls, wk: Worker):
        if wk.check_pf_round and wk.player_idx >= 0:
            x, y = POS_BB_LIST[wk.player_idx]
            rect = (x-35, y-135, x+35, y-90)
            if wk.find_pic(*rect, "破釜.bmp"):
                wk.pf_round = wk.cur_round
                wk.record(f"BB在第{wk.pf_round}回合使用破釜成功")
                wk.check_pf_round = False

    @classmethod
    def fight_do_something(cls, wk: Worker):
        cls.fight_check_pf(wk)
        if rnd(0, 4) == 1 and wk.find_pic_click(*RECT_FIGHT_DIE, "死亡回桃花岛.bmp"):
            wk.record("人物死亡回桃花岛")
            wk.is_die = True
        if cls.enable_task_fix_equip_bb(wk):
            cls.is_should_fix_equip_bb(wk)

    @classmethod
    def chat_input_text(cls, wk: Worker, text: str):
        wk.move_click(*POS_CHAT)
        wk.key_press(VK_BACK, num=10, delay=8)
        msleep(100)
        wk.send_string(text)

    @classmethod
    def fight_check_boss_wu_xing(cls, wk: Worker):
        # 只有极限才要用, 其它情况默认是查到了, 防止继续 空耗守护线程
        return True

    @classmethod
    def people_run_away(cls, wk: Worker):
        if wk.should_run_away:
            return cls.click_run_away(wk)

    @classmethod
    def fight_close_talk(cls, wk: Worker):
        if wk.cur_round == 1:
            cls.close_other_talk(wk)

    @classmethod
    def is_defend_bb(cls, wk: Worker):
        # 防止BB头像被遮挡
        wk.move_to(X_PEOPLE_BLOOD_INTERNAL_HIGH-3,
                   Y_PEOPLE_BLOOD, is_delay=False)
        wk.move_to(X_PEOPLE_BLOOD_INTERNAL_HIGH-3,
                   Y_PEOPLE_INTERNAL, is_delay=False)
        wk.re_move()
        return wk.find_pic(*RECT_BB_AVATAR, cls.DEFEND_BB_PICS)

    @classmethod
    def after_fight_switch_primary_bb(cls, wk: Worker):
        if not wk.cfg_plan["人物切回首发BB"]:
            return
        if not cls.is_difficult_task(wk):
            return
        cls.switch_primary_bb(wk)

    @classmethod
    def switch_primary_bb(cls, wk: Worker):
        cls.close_other_talk(wk)
        cls.open_bb_page(wk)
        x, y = wk.get_pic_pos(*RECT_FULL, "界_BB.bmp")
        if x < 0:
            return
        if wk.find_pic(x+170, y-174, x+199, y-151, "bb首发.bmp", timeout=400):
            # wk.record("已经是首发宝宝, 不需要切换")
            cls.close_bb_page(wk)
            return
        wk.record("正在切换为首发宝宝...")
        msleep(100)
        wk.move_click(x+213, y-165)  # 切到首发宠
        msleep(100)
        wk.find_pic_click(*RECT_FULL, "bb出战.bmp|bb出战2.bmp", timeout=800)
        cls.close_bb_page(wk)
        wk.record("切换为首发宝宝完成")

    @classmethod
    def get_teammate_count(cls, wk: Worker, count_pause_leave=True):
        pic = "组队人物框.bmp|组队人物框2.bmp" if count_pause_leave else "组队人物框.bmp"
        # 第二个是暂离
        return len(wk.find_pic_ex(*RECT_AVATAR_TEAM, pic))

    @classmethod
    def get_pause_leave_teammate_count(cls, wk: Worker):
        # 第二个是暂离
        return len(wk.find_pic_ex(*RECT_AVATAR_TEAM, "组队人物框2.bmp"))

    @classmethod
    def is_difficult_task(cls, wk: Worker):
        return cls.IS_DIFFICULT_TASK

    @classmethod
    def bb_use_jzz_tbs(cls, wk: Worker):
        if not wk.cfg_plan["BB使用技能"]:
            return
        if wk.cfg_plan["人宠使用技能"] == "自动判断" and not cls.is_difficult_task(wk):
            return
        if not cls.is_bb_action(wk):
            return
        if not wk.find_pic(*RECT_BB_AVATAR, "bb_多情.bmp|bb_歪歪.bmp|bb_大圣.bmp"):  # 只有这两个才能用
            return
        if not cls.click_skill(wk):
            return
        msleep(300)
        if wk.cfg_plan["BB铁布衫"] and wk.find_str(*RECT_BB_SKILL, "铁布衫", COLOR_WHITE):
            if cls.use_skill_to_self(wk, "铁布衫"):
                return
        if wk.cfg_plan["BB金钟罩"] and wk.find_str(*RECT_BB_SKILL, "金钟罩", COLOR_WHITE):
            if cls.use_skill_to_self(wk, "金钟罩"):
                return
        cls.cancel_use_skill(wk)

    @classmethod
    def bb_use_skill(cls, wk: Worker):
        if not wk.cfg_plan["BB使用技能"]:
            return
        if wk.cfg_plan["人宠使用技能"] == "自动判断" and not cls.is_difficult_task(wk):
            return
        if not cls.is_bb_action(wk):
            return
        if cls.is_system_broad_show(wk, "不能使用技能"):
            wk.record("被断脉, 不能使用技能")
            return
        if not cls.click_skill(wk):
            return
        msleep(300)
        if wk.cfg_plan["BB厚积"] and wk.find_str(*RECT_BB_SKILL, "厚积", COLOR_WHITE):
            if cls.use_skill_to_self(wk, "厚积"):
                return
        if wk.cfg_plan["BB激将"] and wk.find_str(*RECT_BB_SKILL, "激将", COLOR_WHITE):
            if cls.use_skill_to_self(wk, "激将"):
                return
        if wk.cfg_plan["BB破釜"] and wk.find_str(*RECT_BB_SKILL, "破釜", COLOR_WHITE):
            if wk.cur_round > wk.cfg_plan["BB破釜回合数"]:
                if wk.cur_round == wk.cfg_plan["BB破釜回合数"]+1:
                    wk.record("超过破釜设定回合数，可以破釜沉舟了")
                if cls.use_skill_to_self(wk, "破釜"):
                    return
        if wk.cfg_plan["BB乱神隔世"] and wk.find_str(*RECT_BB_SKILL, "乱神|隔世", COLOR_WHITE):
            if not cls.is_enemy_hit_lsgs(wk):
                if cls.bb_lsgs(wk):
                    return
        cls.cancel_use_skill(wk)

    @classmethod
    def bb_lsgs(cls, wk: Worker):
        if cls.use_skill_to_enemy(wk, "乱神"):
            return True
        if cls.use_skill_to_enemy(wk, "隔世"):
            return True
        cls.cancel_use_skill(wk)
        return False

    @classmethod
    def click_skill(cls, wk: Worker):
        return wk.find_pic_click(*RECT_FULL, "技能.bmp|技能2.bmp|技能3.bmp", re_move=False)

    @classmethod
    def use_skill_to_self(cls, wk: Worker, skill_name: str, timeout=0):
        if wk.player_idx == -1:
            return False
        if skill_name == "破釜":  # 打破 至少隔4回合
            if cls.is_have_buff(wk, "破釜", timeout=200):
                if wk.pf_round != 0 and wk.cur_round < wk.pf_round + 4:
                    wk.record(
                        f"当前回合数:{wk.cur_round} < 上次破釜回合数:{wk.pf_round}+4,暂时不打破釜")
                    return False
        elif skill_name in ["厚积", "激将", "护心"]:  # 这几个技能要看有没有BUFF
            if cls.is_have_buff(wk, skill_name):  # 有buff就不打
                wk.record(f"有{skill_name}buff, 跳过{skill_name}")
                return False
            if skill_name == "护心":  # 护心buff可能被挡
                if wk.hx_round != 0 and wk.cur_round < wk.hx_round + 3:
                    wk.record(f"护心冷却中, 上次回合:{wk.hx_round} 当前回合:{wk.cur_round}")
                    return False
        elif skill_name == "金钟罩":
            if wk.jzz_round != 0 and wk.cur_round <= wk.jzz_round + 3:
                wk.record("本回合数<=上次金钟罩回合数+3, 跳过")
                return False
        elif skill_name == "铁布衫":
            if wk.tbs_round != 0 and wk.cur_round <= wk.tbs_round + 3:
                wk.record("本回合数<=上次金钟罩回合数+3, 跳过")
                return False

        if skill_name in ["金钟罩", "铁布衫"]:  # 这两个技能要给BB打
            x, y = POS_BB_LIST[wk.player_idx]
        else:
            x, y = POS_MATE_LIST[wk.player_idx]

        if skill_name == "护心":
            wk.key_press(VK_F6)
            wk.record(f"人物使用技能 {skill_name}")
        else:
            if not wk.find_str_click(*RECT_BB_SKILL, skill_name, COLOR_WHITE, re_move=False, timeout=timeout):
                return False
            wk.record(f"BB使用技能 {skill_name}")

        wk.move_relative_click(x + rnd(-2, 2), y - rnd(20, 30))
        if skill_name == "破釜":
            wk.check_pf_round = True
        elif skill_name == "金钟罩":
            wk.jzz_round = wk.cur_round
        elif skill_name == "铁布衫":
            wk.tbs_round = wk.cur_round
        elif skill_name == "护心":
            wk.hx_round = wk.cur_round
        return True

    @classmethod
    def use_skill_to_enemy(cls, wk: Worker, skill_name=""):
        if cls.RANDOM_FIGHT_ENEMY and len(wk.survive_enemy_pos_list) > 1:
            idx_list = list(range(10))
        else:
            idx_list = [IDX_BOSS]
        random.shuffle(idx_list)
        if skill_name in ["刺穴", "狮吼"]:
            wk.key_press(VK_F7)
        else:
            if not wk.find_str_click(*RECT_FULL, skill_name, COLOR_WHITE, re_move=False):
                return False
        if skill_name in ["刺穴", "狮吼"]:
            wk.record(f"人物使用技能 {skill_name}")
        else:
            wk.record(f"BB使用技能 {skill_name}")
        for idx in idx_list:
            if not wk.find_pic(*RECT_FULL, f"技能图标_{skill_name}.bmp", timeout=400):
                return True
            if wk.team.seal_main_first and wk.cur_round == 1:  # 封主怪
                x, y = POS_ENENMY_LIST[IDX_BOSS]
            elif wk.not_hit_ci_xue_pos != (-1, -1):  # 然后封没有命中刺穴的怪
                x, y = wk.not_hit_ci_xue_pos
            else:  # 最后随机封
                x, y = POS_ENENMY_LIST[idx]
            wk.move_click(x, y-rnd(30, 60), re_move=False)  # 这里y必须往上偏才能点到
            msleep(100)
        if not wk.find_pic(*RECT_FULL, f"技能图标_{skill_name}.bmp"):
            return True
        cls.cancel_use_skill(wk)
        return False

    @classmethod
    def after_fight_supply(cls, wk: Worker):
        if wk.cfg_plan["人物战后补充"] and wk.after_fight_people_add_x != -1:
            if cls.people_need_supply(wk):
                wk.record("人物血内低于设定阈值，正在补血蓝")
                wk.move_r_click(*POS_PEOPLE_AVATAR, re_move=False)
        if wk.cfg_plan["人物战后补充"] and wk.after_fight_bb_add_x != -1:
            if cls.bb_need_supply(wk):
                wk.move_r_click(*POS_BB_AVATAR, re_move=False)

    @classmethod
    def people_need_supply(cls, wk: Worker):
        if wk.find_pic_and_move(*RECT_PEOPLE_BB_AVATAR, "描述边框.bmp", dy=2):
            wk.re_move()
        blood_color = wk.get_pos_color(
            wk.after_fight_people_add_x, Y_PEOPLE_BLOOD)[:2]
        internal_color = wk.get_pos_color(
            wk.after_fight_people_add_x, Y_PEOPLE_INTERNAL)[4:]
        # print(f"人物血内补充x: {wk.after_fight_people_add_x}, 血色: {blood_color}, 内色: {internal_color}")
        if blood_color < "65" or internal_color < "69":
            return True
        return False

    @classmethod
    def bb_need_supply(cls, wk: Worker):
        if wk.find_pic_and_move(*RECT_PEOPLE_BB_AVATAR, "描述边框.bmp", dy=2):
            wk.re_move()
        blood_color = wk.get_pos_color(wk.after_fight_bb_add_x, Y_BB_BLOOD)[:2]
        internal_color = wk.get_pos_color(
            wk.after_fight_bb_add_x, Y_BB_INTERNAL)[4:]
        # print(f"BB血内补充x: {wk.after_fight_bb_add_x}, 血色: {blood_color}, 内色: {internal_color}")
        if blood_color < "65" or internal_color < "65":
            return True
        return False

    @classmethod
    def after_fight_use_xmx(cls, wk: Worker):
        if not cls.NEED_AVOID_FIGHT:
            return
        cls.use_xmx(wk)

    @classmethod
    def use_xmx(cls, wk: Worker):
        # 不需要使用 和 使用成功 返回True, 否则返回False
        res = True
        case1 = wk.last_use_xmx_ts and settings.cur_time_stamp - wk.last_use_xmx_ts > 23*60
        case2 = cls.is_avoid_fight(wk)
        if case1 or not case2:
            if case1:
                wk.record("距上次熊猫香使用超过23分钟，正在自动使用...")
            if case2:
                wk.record("熊猫香效果消失，正在自动使用...")
            cls.stop_auto_find_way(wk, force=True)
            res = cls.bag_use_item(wk, "熊猫香.bmp")
            if res:
                wk.last_use_xmx_ts = settings.cur_time_stamp
            else:
                wk.lack_xmx = True
            cls.close_pages(wk)
        return res

    @classmethod
    def use_hls(cls, wk: Worker):
        if cls.is_avoid_fight(wk):  # 有熊猫香效果，先去掉
            cls.bag_use_item(wk, "花露水.bmp")
            wk.is_stuck = True

    @classmethod
    def create_team(cls, wk: Worker):
        wk.record("正在创建队伍...")
        cls.close_big_map(wk)
        cls.off_ride(wk)
        for i in range(4):
            if cls.get_teammate_count(wk) >= 1:
                wk.record("创建队伍成功")
                cls.on_ride(wk)
                return True
            wk.move_click(*POS_SMALL_MAP_CENTER)
            wk.key_press(VK_F12)
            msleep(300 * (i+1))
            wk.key_press_combo(VK_ALT, VK_T)
            msleep(600)
            wk.move_click(CX, CY)
            msleep(400)
        wk.record("创建队伍失败")
        wk.right_click()  # 取消ALT+T
        msleep(600)
        cls.close_pages(wk)
        return False

    @classmethod
    def cancel_use_skill(cls, wk: Worker):
        wk.right_click()
        wk.right_click()
        wk.re_move()

    @classmethod
    def get_da_nei_award(cls, wk: Worker):
        cls.switch_line(wk, "二")
        cls.back_to_kai_feng(wk)
        npc_name = "钦差大臣"
        wk.record("正在前往领取大内奖励...")
        cls.talk_with_cur_map_npc(wk, npc_name, ["领取奖励", "领取战斗奖励"])
        cls.click_confirm(wk)
        cls.talk_with_cur_map_npc(wk, npc_name, ["领取奖励", "领取全区奖励"])

    @classmethod
    def run_to_map(cls, wk: Worker, target_map_name="开封"):
        if wk.is_fight:
            cls.fight_operation(wk)
        cls.walk_to_run(wk)
        cur_map_name = cls.cur_map(wk)
        if target_map_name == "当前地图" or cur_map_name == target_map_name:
            return

        if target_map_name == "古墓三层":
            if cur_map_name != "秦陵一层":
                cls.run_to_map(wk, "古墓二层")
            cls.cross_map(wk, "古墓三层")
            return
        elif target_map_name == "古墓二层":
            if cur_map_name != "古墓三层":
                cls.run_to_map(wk, "古墓一层")
            cls.cross_map(wk, "古墓二层")
            return
        elif target_map_name == "秦陵二层":
            cls.run_to_map(wk, "秦陵一层")
            cls.cross_map(wk, "秦陵二层")
            return
        elif target_map_name == "武林盟":
            if cur_map_name != "开封":
                cls.run_to_map(wk, "开封")
            cls.cross_map(wk, "武林盟")
            return
        elif target_map_name == "帮会领地":
            if cls.is_in_bang_hui(wk):
                return
            if cur_map_name != "开封":
                cls.back_to_kai_feng(wk)
            cls.cross_map(wk, "帮会领地")
            return
        elif cur_map_name not in OUTSIDE_MAP_LIST:
            if cur_map_name == "矿洞":
                cls.from_kuang_dong_to_bang_hui(wk)
            if cls.is_in_bang_hui(wk):
                wk.record("在帮会领地中, 正在返回开封...")
                cls.from_bang_hui_to_kai_feng(wk)

        cls.stop_auto_find_way(wk)  # 停止跑动
        wk.record(f"正在跑步到 {target_map_name}")
        x, y = WORLD_MAP_POS_DICT.get(target_map_name, (-1, -1))
        if x < 0:
            wk.record(f"世界地图上没有: {target_map_name}")
            msleep(10*1000)  # 过10秒后再试
            return
        click_count = 0
        for i in range(rate(1000)):
            cls.handle_pass_map(wk)
            if wk.is_fight:
                cls.fight_operation(wk)
            if cls.cur_map(wk) == target_map_name:
                wk.is_stuck = True
                wk.record(f"已到达 {target_map_name}")
                break
            if i % 40 == 0 or wk.is_stuck:
                if click_count > 25:  # 点击次数达到后, 点下远处地板
                    cls.handle_find_way_often(wk)
                    click_count -= 5
                click_count += 1
                cls.click_world_map_to(wk, x, y)
                wk.is_stuck = False
            msleep(600)
        if target_map_name == "开封" and cls.cur_map(wk) == "开封":
            if cls.people_need_supply(wk):
                wk.record("人物血内低于设定阈值, 到客栈恢复...")
                cls.go_to_ke_zhan_supply(wk)

    @classmethod
    def run_to_map_run_away(cls, wk: Worker, map_name="开封"):
        wk.should_run_away = True
        cls.run_to_map(wk, map_name)
        wk.should_run_away = False

    @classmethod
    def cross_map(cls, wk: Worker, target_map: str):
        cls.close_pages(wk)
        from_map = cls.cur_map(wk)
        wk.record(f"执行过图: {from_map} -> {target_map}")
        method = MAP_CROSS_METHOD.get((from_map, target_map))
        if not method:
            wk.record(f"未找到过图方法 {from_map} -> {target_map}")
            return False
        if method.startswith("跨多图"):
            mid_maps = method.split("-")[1:]
            for mid_map in mid_maps:
                cls.cross_map(wk, mid_map)
            cls.cross_map(wk, target_map)
        elif method == "传送点":
            cls.cross_map_by_way(wk, from_map=from_map, target_map=target_map)
        else:
            cls.cross_map_by_talk(wk, target_map=target_map, npc_name=method)
        return True

    @classmethod
    def cross_map_by_way(
            cls, wk: Worker, from_map: str, target_map=""
    ):  # 走路过图
        pos_big_map, pos_transfer = BIG_MAP_POS_DICT.get(
            (from_map, target_map), [(), ()]
        )
        if not pos_big_map:
            wk.record(f"未找到过图点 {from_map} -> {target_map}")
            return
        flag = False
        for i in range(rate(1000)):
            msleep(500)
            cur_map_name = cls.cur_map(wk)
            if target_map == "" and cur_map_name != from_map:
                wk.record(f"成功过图到 {cur_map_name}")
                wk.is_stuck = True
                return
            if target_map != "" and cls.cur_map(wk) == target_map:
                wk.record(f"成功过图到 {target_map}")
                wk.is_stuck = True
                return
            if wk.is_fight:
                cls.fight_operation(wk)
            if cls.is_talk_open(wk):
                cls.close_other_talk(wk)
            if cls.click_treasure(wk):
                continue
            if i == 0 or flag == True:
                cls.big_map_click(wk, *pos_big_map)
                flag = False
            # 停下来说明人已经走到传送点了，然后点一下传送点就过去了
            if i != 0 and (wk.is_stuck or i % 100 == 50):
                if cls.click_treasure(wk):
                    continue
                x, y = pos_transfer
                rand_hide = rnd(0, 1)
                if y > 543 and rand_hide:
                    wk.key_press_combo(VK_ALT, VK_K)
                    msleep(800)
                wk.key_press(VK_F12)
                cls.click_multi_cancel(wk)
                wk.move_relative_click(x+rnd(-2, 2), y+rnd(-2, 2))  # 点传送点
                if y > 543 and rand_hide:
                    msleep(800)
                    wk.key_press_combo(VK_ALT, VK_K)
                msleep(1400)
                flag = True

    @classmethod
    def cross_map_by_talk(
            cls, wk: Worker, target_map="", npc_name="送使", talk_items=["是|去|进|有劳|回本帮会"]
    ):
        for i in range(3):
            cur_map_name = cls.cur_map(wk)
            if target_map == "帮会领地" and cur_map_name != "开封":
                return
            if cur_map_name == target_map:
                return
            if target_map.endswith("层"):
                print(
                    f"target_map: {target_map}, target_map[-2:]: {target_map[-2:]}")
                if target_map[-2:] in ["三层", "四层", "五层", "六层", "七层", "八层"]:
                    talk_items = [target_map[-2:]]
            cls.find_way_npc(wk, npc_name, talk_items)
            msleep(800)

    @classmethod
    def restore_default_cursor(cls, wk: Worker):
        if wk.get_cursor_shape() in [SHAPE_CURSOR_TRADE, SHAPE_CURSOR_TEAM, SHAPE_CURSOR_ATTACK]:
            wk.record("检测到鼠标指针异常, 恢复默认")
            wk.move_r_click(CX, CY)

    @classmethod
    def big_map_click(cls, wk: Worker, x: int, y: int):
        cls.restore_default_cursor(wk)
        cls.click_close_pic(wk)  # 防止ESC干扰
        cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)
        cls.walk_to_run(wk)
        cls.open_big_map(wk)
        if not cls.is_big_map_open(wk):
            return False
        if wk.find_pic_click(*RECT_FULL, "TAB开.bmp"):
            wk.record("关闭寻路TAB开")
            cls.click_close_pic(wk)
        wk.move_r_click(x, y, is_delay=False, re_move=False)  # 取消选中物品
        wk.move_click(x, y)
        cls.close_big_map(wk)
        wk.is_stuck = False
        return True

    @classmethod
    def big_map_find_pic_click(cls, wk: Worker, pic_name: str, delta_color="101010", sim=0.95, timeout=0):
        cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)
        cls.open_big_map(wk)
        if not cls.is_big_map_open(wk):
            return False
        x, y = wk.get_pic_pos(*RECT_FULL, pic_name, delta_color, sim)
        if x < 0:
            return False
        wk.move_click(x, y)
        cls.close_big_map(wk)
        return True

    @classmethod
    def is_in_bang_hui(cls, wk: Worker):
        cls.restore_default_cursor(wk)
        cls.open_find_way_page(wk)
        if not cls.is_find_way_page_open(wk):
            wk.record("打开寻路页失败")
            wk.move_r_click(26, 211)
            msleep(600)
            cls.close_pages(wk)
            cls.open_find_way_page(wk)
        return wk.find_str(*RECT_LEFT, "林冲|陶宗旺|宋江", COLOR_TASK_NAME)

    @classmethod
    def from_bang_hui_to_kai_feng(cls, wk: Worker):
        cls.cross_map_by_way(wk, "帮会领地", "开封")

    @classmethod
    def go_to_bang_hui(cls, wk: Worker):
        cur_map_name = cls.cur_map(wk)
        if cur_map_name == "矿洞":
            wk.record("从矿洞回帮会...")
            cls.from_kuang_dong_to_bang_hui(wk)
        elif not cls.bag_use_item(wk, "帮会票.bmp"):
            if cur_map_name != "开封":
                wk.record("先到开封再回帮会...")
            cls.back_to_kai_feng(wk)
            wk.record("正在前往帮会领地...")
            cls.talk_with_cur_map_npc(wk, "帮会领地管理员", ["回本帮会"])
        return cls.is_in_bang_hui(wk)

    @classmethod
    def from_kuang_dong_to_bang_hui(cls, wk: Worker):
        cls.cross_map_by_way(wk, "矿洞")

    @classmethod
    def back_to_kai_feng(cls, wk: Worker):
        cls.stop_auto_find_way(wk)
        cur_map_name = cls.cur_map(wk)
        if cur_map_name == "开封":  # 回到开封
            return
        if wk.is_fight:
            cls.fight_operation(wk)

        wk.record("正在返回开封...")
        cls.on_ride(wk)

        if cur_map_name in ["", "武当山", "峨嵋山", "光明顶", "唐家堡", "少林"]:
            wk.record("在门派地图, 直接跑步到开封...")
            cls.run_to_map(wk)
            return
        # 如果是单人任务
        if cls.get_teammate_count(wk) == 0 and cur_map_name in OUTSIDE_MAP_LIST:
            cls.back_to_school(wk, kai_feng_ok=True)
            cls.run_to_map(wk)
            return
        # 用票
        back_city = wk.cfg_plan["回城"]
        wk.record(f"回城策略:{back_city}")
        if back_city == "超级遁甲":
            npc_name = cls.get_task_publisher_name()
            if cls.fast_fly_kaifeng(wk, npc_name):
                return
        if wk.cfg_plan["回城"] == "小遁甲" or back_city == "超级遁甲":
            if cls.bag_use_item(wk, "小遁甲.bmp", is_close=False):
                # 用完小遁甲, 还要点击开封
                msleep(600)
                name = wk.cur_task
                if wk.cur_task == "带队平乱":
                    name += "|曹都尉"
                elif wk.cur_task == "围捕大盗":
                    name += "|守城校尉"
                elif wk.cur_task == "百战百胜":
                    name += "|百败居士"
                if wk.find_str_db_click(*RECT_LEFT, name, COLOR_BLACK):
                    wk.record(f"小遁甲 直接飞到接任务处:{wk.cur_task}")
                else:
                    wk.record("小遁甲 回开封")
                    wk.move_db_click(*POS_DUNJIA_KAIFENG)
                msleep(600)
                return
            wk.cfg_plan["回城"] = "开封票"
            wk.record("没有小遁甲了，自动降级为 开封票")
        if wk.find_pic_db_click(*RECT_SKILL, "归马.bmp"):
            wk.record("发现归马恋栈技能, 使用技能回城")
            msleep(600)
            return
        if wk.cfg_plan["回城"] == "开封票":
            if not cls.bag_use_item(wk, "回城马车票.bmp"):
                wk.cfg_plan["回城"] = "门派票"
                wk.record("没有开封票了，自动降级为 门派票")
            msleep(400)
            wk.is_stuck = True
        if wk.cfg_plan["回城"] == "门派票":
            if cls.cur_map(wk) not in [
                    "武当山",
                    "峨嵋山",
                    "光明顶",
                    "唐家堡",
                    "少林",
            ]:
                # 不能作关闭页面操作，不然会把回师门对话框关掉
                if cls.bag_use_item(wk, "回门派马车票.bmp", is_close=False):
                    msleep(100)
                    wk.find_str_click(*RECT_TALK, "师门任务",
                                      COLOR_TALK_ITEM, timeout=800)
                    msleep(400)
                else:
                    wk.cfg_plan["回城"] = "跑步"
                    wk.record("没有门派票了，自动降级为 跑步")
                    cls.close_pages(wk)
                    wk.is_stuck = True
        # 跑步
        if cls.cur_map(wk, timeout=800) != "开封":
            cls.run_to_map(wk, "开封")

    @classmethod
    def back_to_school(cls, wk: Worker, kai_feng_ok=False):
        if wk.is_fight:
            return False
        # 如果在门派, 直接返回
        school_list = ["武当山", "峨嵋山", "光明顶", "唐家堡", "少林"]
        if kai_feng_ok:
            school_list.append("开封")
        cur_map_name = cls.cur_map(wk)
        if cur_map_name in school_list:
            return True
        wk.record("回门派...")
        if cur_map_name not in OUTSIDE_MAP_LIST:
            if cur_map_name == "矿洞":
                cls.from_kuang_dong_to_bang_hui(wk)
            if cls.is_in_bang_hui(wk):
                cls.from_bang_hui_to_kai_feng(wk)
        # 如果单人任务, 且坐骑有游马, 优先用F8
        if wk.have_ride and cls.get_teammate_count(wk) == 0:
            cls.on_ride(wk)
            if cls.is_ride_have_skill(wk, "游马"):
                wk.record("有游马，直接用F8")
                wk.key_press(VK_F8)
                msleep(1000)
                return True
        # 如果在开封, 直接走回去
        if cls.cur_map(wk) == "开封":
            wk.record("在开封，直接走回去门派...")
            if cls.talk_with_cur_map_npc(wk, "门派使者", ["回本门派"]):
                msleep(1000)
                return True
            else:
                wk.record("没找到门派使者, 回门派失败")  # 继续往下尝试用F8回去
        # 没有游马，则背包使用门派票
        if wk.have_school_ticket:
            wk.record("正在使用 归马或门派票或开封票...")
            if wk.find_pic_db_click(*RECT_SKILL, "归马.bmp"):
                wk.record("有归马优先用归马")
                msleep(800)
                return cls.back_to_school(wk)
            elif cls.bag_use_item(wk, "回门派马车票.bmp|回城马车票.bmp", is_close=False):
                if wk.find_str_click(*RECT_TALK, "师门任务", COLOR_TALK_ITEM, timeout=500):
                    wk.record("使用 门派票成功!")
                    msleep(1000)
                    return True
                if cls.cur_map(wk) == "开封":
                    wk.record("使用 开封票成功!")
                    if not kai_feng_ok:
                        return cls.back_to_school(wk)
                    return True
            else:
                wk.have_school_ticket = False
        # 如果是团队任务, 由于不能F8
        if cls.IS_TEAM_TASK:
            wk.record("团队任务，不能直接F8")
            return False
        # 没有门派票，直接用F8
        wk.record("没有门派票，直接用F8")
        wk.key_press(VK_F8)
        msleep(1000)
        if cls.is_popup_confirm_show(wk):
            msg = wk.ocr(*RECT_POPUP, COLOR_BLACK)
            wk.record(f"回门派失败:{msg}")
            cls.click_confirm(wk)
            if "正在战斗中" in msg:
                wk.right_click()
                cls.fight_operation(wk)
                cls.stop_auto_find_way(wk, force=True)
                return cls.back_to_school(wk, kai_feng_ok=kai_feng_ok)
            if "先补充一下" in msg:
                wk.record("血内不足, 正在前往开封客栈补充...")
                cls.run_to_map(wk, "开封")
                if cls.go_to_ke_zhan_supply(wk):
                    return cls.back_to_school(wk, kai_feng_ok=kai_feng_ok)
            return False
        # 直接F8后, 若血内 < 战后阈值, 先到客栈补充
        if cls.people_need_supply(wk):
            wk.record("血内低于战后补充阈值, 前往开封客栈补充...")
            cls.run_to_map(wk, "开封")  # 这里会自动补充
            return cls.back_to_school(wk, kai_feng_ok=kai_feng_ok)
        return True

    @classmethod
    def go_to_ke_zhan_supply(cls, wk: Worker, fight_run_away=True):
        if cls.cur_map(wk) != "开封":
            wk.record("回到开封失败, 取消客栈回复")
            return
        cls.talk_with_cur_map_npc(wk, "银湘玉", ["休息恢复血内", "休息"])

    @classmethod
    def people_catch_bb(cls, wk: Worker):
        pass

    @classmethod
    def is_talk_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_TALK, "界_对话框.bmp", timeout=timeout)

    @classmethod
    def is_talk_show_info(cls, wk: Worker, info: str, color=COLOR_BLACK, timeout=0):
        return wk.find_str(*RECT_TALK, info, color=color, timeout=timeout)

    @classmethod
    def get_talk_name(cls, wk: Worker):
        if not cls.is_talk_open(wk):
            return ""
        return wk.ocr(*RECT_TALK_NAME, COLOR_NPC_NAME).rstrip("一_")

    @classmethod
    def talk_click_first_item(cls, wk: Worker, timeout=0):
        wk.record("对话中...")
        for i in range(14):
            if not cls.is_talk_open(wk, timeout=200):
                break
            # 点任务项
            if wk.find_color_click(*RECT_TALK_CONTENT, COLOR_TALK_ITEM_TASK):
                msleep(200)
                continue
            # 点第一项
            if wk.find_color_click(*RECT_TALK_CONTENT, COLOR_TALK_ITEM):
                msleep(200)
                continue
            # 都没有就点空白区域
            cls.talk_click_space(wk)
            msleep(timeout)

    @classmethod
    def talk_click_items(cls, wk: Worker, talk_items: list, close_talk=True, use_fast=True, check_twice=False):
        if wk.find_str_click(*RECT_TALK, talk_items[0], COLOR_TALK_ITEM, timeout=200):
            if len(talk_items) > 1:
                msleep(300)
                for item in talk_items[1:]:
                    if item.isdigit():
                        res = wk.find_str_click(
                            *RECT_TALK, item, COLOR_TALK_ITEM, zk=ZK_DIGIT_11, timeout=700, use_fast=use_fast
                        )
                    else:
                        res = wk.find_str_click(
                            *RECT_TALK, item, COLOR_TALK_ITEM, timeout=700, use_fast=use_fast)
                    if not res:  # 对话中途可能突然弹出奇遇
                        cls.refuse_qi_yu(wk)
                    msleep(500)
            elif check_twice:  # 确保第一级对话点到了
                msleep(100)
                wk.find_str_click(
                    *RECT_TALK, talk_items[0], COLOR_TALK_ITEM, timeout=200)

            if close_talk:
                cls.close_other_talk(wk, timeout=300)
            return True
        if close_talk:
            cls.close_other_talk(wk)
        return False

    @classmethod
    def talk_click_last_item(cls, wk: Worker):
        if cls.is_talk_open(wk):
            return wk.find_color_click(*RECT_TALK, COLOR_TALK_ITEM, order=ORDER_RLDU)
        return False
    
    @classmethod
    def refuse_qi_yu(cls, wk: Worker):
        if cls.get_talk_name(wk) not in ["震关西", "剑客", "士兵", "肉商", "江湖客", "将军亲卫", "村夫", "差官", "猎人", "杨不忆", "美食家", "程铁牛", "黄药师", "富商", "求道人", "凤天情", "占卜道士", "老头", "江湖侠客"]:
            return
        wk.record("拒绝接受奇遇任务")
        cls.talk_click_space(wk)
        msleep(400)
        if cls.is_talk_open(wk):
            wk.find_color_click(*RECT_TALK_SMALL, COLOR_TALK_ITEM, order=ORDER_RLDU)
        msleep(400)
        cls.talk_click_space(wk)


    @classmethod
    def talk_click_specify_item(cls, wk: Worker, item_text: str, color=COLOR_TALK_ITEM, close_talk=True, timeout=200):
        if cls.is_talk_open(wk):
            if wk.find_str_click(*RECT_TALK, item_text, color=color, timeout=timeout):
                return True
            if close_talk:
                cls.close_other_talk(wk)
        return False

    @classmethod
    def talk_npc_in_place(cls, wk: Worker, npc_name: str, timeout=0):
        if not npc_name:
            return
        if "|" in npc_name:
            npc_names = npc_name.split("|")
            min_len, max_len = 7, 0
            for name in npc_names:
                if len(name) > max_len:
                    max_len = len(name)
                if len(name) < min_len:
                    min_len = len(name)
            offset_x = 7 * rnd(min_len, max_len)
        else:
            offset_x = 7*len(npc_name) + rnd(-7, 7)
        offset_y = rnd(80, 100)
        color = "|".join([COLOR_GREEN, COLOR_GOLD, COLOR_CYAN])
        return wk.find_str_offset_click(*RECT_FULL, npc_name, color, dx=offset_x, dy=offset_y, timeout=timeout, relative=True)

    @classmethod
    def talk_click_space(cls, wk: Worker, timeout=0):
        if cls.is_talk_open(wk, timeout=0):
            # wk.find_pic_click(*RECT_TALK, "界_对话框空白.bmp", delta_color="121212", re_move=False)
            # x, y = wk.get_pic_pos(*RECT_TALK, "界_对话框空白.bmp", delta_color="121212")
            # wk.record("点击对话框空白")
            wk.move_click(300 + rnd(0, 300), 280 + rnd(0, 70), re_move=False)

    @classmethod
    def close_other_talk(cls, wk: Worker, timeout=0):
        for i in range(12):
            timeout = 0 if i == 0 else 300
            if not cls.is_talk_open(wk, timeout=timeout):
                break
            wk.is_stuck = True
            if cls.fight_yan_nan_tian(wk):
                wk.record("战斗遇到 燕南天")
                break
            cls.refuse_qi_yu(wk)
            if not wk.find_color_click(
                    *RECT_TALK, COLOR_TALK_ITEM, order=ORDER_RLDU
            ):  # 点最后一个选项关掉
                cls.talk_click_space(wk)

    # 打开系统面板，寻路接任务NPC
    @classmethod
    def open_system_npc_click(
            cls, wk: Worker, task_name: str, is_activity=False, enable_white=False
    ):
        cls.close_pages(wk)
        cls.open_system_page(wk)
        # 如果打开就显示了这个NPC, 就直接点
        npc_name = cls.get_task_publisher_name()
        if npc_name and wk.find_str_click(
                *RECT_SYSTEM_TASK_DESC, npc_name, COLOR_GOLD, timeout=400
        ):
            cls.close_page(wk)
            wk.is_stuck = False
            return True
        if not cls.is_system_page_open(wk):
            return False
        # 先找任务
        flag = False
        for i in range(6):
            if cls.click_task_name(wk, task_name, is_activity, enable_white):
                flag = True
                break
            wk.move_click(*POS_SYSTEM_DOWN, click_count=12)
        if not flag:
            wk.record("未找到任务:" + task_name)
            return False
        # 点任务NPC
        wk.move_click(
            *POS_TASK_DESC_DOWN, click_count=6
        )  # 先往下移到任务NPC名字显示出来
        if not wk.find_color_click(*RECT_SYSTEM_TASK_DESC, COLOR_GOLD, timeout=400):
            wk.record("未找到接任务NPC")
            return False
        cls.close_page(wk)
        wk.is_stuck = False
        return True

    @classmethod
    def click_task_name(
            cls, wk: Worker, task_name: str, is_activity=False, enable_white=False, timeout=400
    ):
        if is_activity:
            return wk.find_str_click(
                *RECT_SYSTEM_ACTIVITY, task_name, COLOR_WHITE, timeout=timeout
            )
        else:
            if not enable_white and wk.find_str(*RECT_SYSTEM_TASK, task_name, COLOR_WHITE):
                cls.close_system_page(wk)
                raise Exception("任务完成达到上限")
            return wk.find_str_click(
                *RECT_SYSTEM_TASK, task_name, COLOR_GREEN + "|" + COLOR_WHITE, timeout=timeout
            )

    @classmethod
    def get_task_publisher_name(cls) -> str:
        return ""

    @classmethod
    def fight_yan_nan_tian(cls, wk: Worker):
        return False

    @classmethod
    def click_treasure(cls, wk: Worker):  # 开宝箱
        pass

    @classmethod
    def is_cjdj_enable(cls, wk: Worker, timeout=0):
        return wk.find_pic(563, 538, 612, 582, "超级遁甲.bmp", timeout=timeout)

    @classmethod
    def is_fast_mode_back_city(cls, wk: Worker):
        if wk.cfg_plan["回城"] != "超级遁甲":
            return False
        return cls.is_cjdj_enable(wk)

    @classmethod
    def is_fast_mode_go_out(cls, wk: Worker):
        if wk.cfg_plan["出城"] != "超级遁甲":
            return False
        return cls.is_cjdj_enable(wk)

    @classmethod
    def receive_task(cls, wk: Worker):
        wk.record("回城接任务中...")
        # 回城超级遁甲
        npc_name = cls.get_task_publisher_name()
        if cls.IS_TEAM_TASK and cls.is_fast_mode_back_city(wk) and cls.cur_map(wk) != "开封":
            if not cls.fast_fly_kaifeng(wk, npc_name):
                cls.goto_recv_task_place(wk)
        else:
            cls.goto_recv_task_place(wk)
        cls.do_fix_bb(wk)
        cls.do_buy_xmx(wk)
        cls.do_receive_task(wk)

    @classmethod
    def do_receive_task(cls, wk: Worker, npc_name=""):
        wk.record("正在接任务...")
        for i in range(rate(1000)):
            if cls.talk_recv_task(wk):
                wk.record("接任务成功")
                break
            if wk.is_fight:
                cls.fight_operation(wk)
            if i % 40 == 0 or wk.is_stuck:
                cls.click_system_task_name(wk)
                if i != 0 and not cls.is_talk_open(wk):
                    cls.talk_npc_in_place(wk, npc_name)
            msleep(600)
        cls.close_other_talk(wk)

    @classmethod
    def click_system_task_name(cls, wk: Worker, enable_white=False):
        cls.restore_default_cursor(wk)
        return cls.open_system_npc_click(wk,
                                         cls.get_task_name(),
                                         cls.IS_ACTIVITY,
                                         enable_white=enable_white)

    @classmethod
    def get_task_name(cls) -> str:
        return cls.TASK_NAME

    @classmethod
    def goto_recv_task_place(cls, wk: Worker):
        cls.back_to_kai_feng(wk)
        cls.wait_mate_return_team(wk)

    @classmethod
    def enable_task_fix_equip_bb(cls, wk: Worker):
        return cls.IS_TASK_FIX_EQUIP_BB_ENABLE

    @classmethod
    def do_fix_bb(cls, wk: Worker, force=False):
        if not cls.enable_task_fix_equip_bb(wk):
            return False
        if not force:
            if cls.IS_TEAM_TASK:
                if not wk.team.need_fix_equip_bb:
                    return False
            else:
                if not wk.need_fix_equip_bb:
                    return False

        fix_method = wk.cfg_plan["修理忠诚方式"]
        wk.record(f"修理忠诚方式: {fix_method}")
        if fix_method == "会员快捷":
            cls.fix_bb_by_member(wk)
        elif wk.need_fix_equip and not wk.need_fix_equip_bb:
            cls.fix_equip(wk)
        else:
            cls.fix_equip(wk)
            cls.fix_bb(wk)
        if cls.IS_TEAM_TASK:
            wk.team.need_fix_equip_bb = False
        else:
            wk.need_fix_equip_bb = False
            wk.need_fix_equip = False
        return True

    @classmethod
    def fix_bb_by_member(cls, wk: Worker):
        cls.open_quick_func_page(wk)
        if wk.find_pic_click(*RECT_LEFT, "会员.bmp", timeout=300):
            wk.record("切换到会员快捷功能页")
        if wk.find_pic_click(*RECT_LEFT, "快捷修理.bmp", timeout=200):
            wk.record("正在修理...")
            msleep(400)
            if wk.find_str_click(*RECT_LEFT, "全部修理", COLOR_BLACK, timeout=600):
                msleep(400)
                if cls.talk_click_specify_item(wk, "确定"):
                    wk.record("修理成功!")
                    msleep(600)
        cls.close_other_talk(wk, timeout=600)
        msleep(200)
        if wk.find_pic_click(*RECT_LEFT, "快捷随从.bmp", timeout=200):
            wk.record("正在教化...")
            msleep(400)
            if wk.find_str_click(*RECT_LEFT, "教化全部随从", COLOR_BLACK, timeout=600):
                msleep(400)
                if cls.talk_click_specify_item(wk, "好好的给我教化"):
                    wk.record("教化成功!")
                    msleep(600)
                    cls.close_other_talk(wk, timeout=600)
        cls.close_pages(wk)

    @classmethod
    def do_buy_xmx(cls, wk: Worker):
        if not wk.lack_xmx:
            return
        wk.lack_xmx = False
        if cls.use_xmx(wk):
            return
        wk.record("缺少熊猫香, 前往商店购买熊猫香...")
        cls.buy_thing(wk, "熊猫香", 10)
        cls.use_xmx(wk)

    @classmethod
    def wait_mate_return_team(cls, wk: Worker):
        if not cls.IS_TEAM_TASK:
            return
        # 团队任务如果有队员暂离, 等待归队
        if not cls.is_team_mate_pause_leave(wk):
            return
        cls.close_big_map(wk)
        for i in range(rate(500)):
            if not cls.is_team_mate_pause_leave(wk, timeout=500):
                wk.record("等待暂离队员归队成功")
                return
            if i % 20 == 0:
                wk.record("正在等待暂离队员归队...")
            msleep(1000)
        wk.record("等待暂离队员归队超时!")

    @classmethod
    def is_team_mate_pause_leave(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_TEAM_AVATAR, "组队人物框2.bmp", timeout=timeout)

    @classmethod
    def talk_recv_task(cls, wk: Worker) -> bool:
        raise NotImplementedError

    @classmethod
    def is_fight_teammate_need_red_blue(
            cls, wk: Worker, from_obj=SAVE_PEOPLE, save_obj=SAVE_PEOPLE
    ):
        """
        (x - 下限) / (上限 - 下限) * 100 = 用户设置百分比阈值
        然后看下，下限处有血内颜色，说明阈值对应的x处的血内是否是空的
        返回值是 [(加血蓝ACTION, idx)...]
        """
        # 1 读取阈值x列表
        if from_obj == SAVE_PEOPLE:
            arr_idx_x = (
                wk.people_save_people_idx_x
                if save_obj == SAVE_PEOPLE
                else wk.people_save_bb_idx_x
            )
        else:
            arr_idx_x = (
                (wk.defend_bb_save_people_idx_x if wk.is_defend_bb else wk.attack_bb_save_people_idx_x)
                if save_obj == SAVE_PEOPLE
                else (wk.defend_bb_save_bb_idx_x if wk.is_defend_bb else wk.attack_bb_save_bb_idx_x)
            )
        # 2 遍历顺序轮转，避免所有人同时给一个人加血蓝, 记得只截取现在队伍人数的
        team_mate_count = cls.get_teammate_count(
            wk, count_pause_leave=False) or 1
        arr_idx_x = shuffle_list(arr_idx_x[:team_mate_count])
        # 3 然后挨个判断该位置的血蓝是否空了
        fight_pb_info = FIGHT_PEOPLE_INFO if save_obj == SAVE_PEOPLE else FIGHT_BB_INFO
        ret_list = []
        for idx, x in arr_idx_x:
            ret_action, ret_idx = ACTION_NONE, -1
            _, low_x, high_x, red_y, blue_y = fight_pb_info[idx]
            # 先定位血蓝条Y轴位置, 分别是飞凤22, 统领30, 紫郡36-40
            border_color = wk.get_pos_color(low_x-1, red_y)
            if save_obj == SAVE_PEOPLE:
                for dy in [0, 22, 30, 36, 38, 40]:
                    temp_y = red_y - dy
                    border_color = wk.get_pos_color(low_x-1, temp_y)
                    if border_color in ["a4a4b0", "737672"]:
                        red_y = temp_y
                        break  # 定位到了血蓝边界位置
                if border_color not in ["a4a4b0", "737672"]:
                    wk.record(f"未识别到队员{idx}的血条")
                    continue
            blue_y = red_y + 3

            if low_x <= x <= high_x:  # 读取阈值点颜色是否空血蓝
                blood_color = wk.get_pos_color(x, red_y)
                internal_color = wk.get_pos_color(x, blue_y)
                # print(f"统领时装, idx={idx}, x={x}, blood_color={blood_color}, internal_color={internal_color}")
                if color_in_range(blood_color, COLOR_FIGHT_EMPTY_BLOOD, 5):
                    ret_action += ACTION_BLOOD
                if color_in_range(internal_color, COLOR_FIGHT_EMPTY_INTERNAL, 5):
                    ret_action += ACTION_INTERNAL
            else:  # 如果设定阈值超过最高血内, 强制加血内
                # print(f"idx={idx}, x={x}, low_x={low_x}, high_x={high_x}, 超过最高血内, 强制加血蓝")
                ret_action += ACTION_BLOOD
                ret_action += ACTION_INTERNAL

            if ret_action != ACTION_NONE:
                ret_idx = idx
                ret_list.append((ret_action, ret_idx))
        return ret_list

    @classmethod
    def is_fight_self_need_hu_xin(cls, wk: Worker):
        HU_XIN_THRESHOLD = wk.cfg_plan["人物护心百分比"]
        _, lower_x, upper_x, red_y, blue_y = FIGHT_PEOPLE_INFO[wk.player_idx]
        # 护心找下面的血条位置
        border_color = wk.get_pos_color(lower_x-1, red_y)
        # 先定位血蓝条Y轴位置, 分别是飞凤22, 统领30, 紫郡36-40
        for dy in [0, 22, 30, 36, 38, 40]:
            temp_y = red_y - dy
            border_color = wk.get_pos_color(lower_x-1, temp_y)
            if border_color in ["a4a4b0", "737672"]:
                red_y = temp_y
                break  # 定位到了血蓝边界位置
                # print(f"角色的时装导致血条上升, row={wk.row}, low_x={low_x}, red_y={red_y}, bclor={border_color}")
        if border_color not in ["a4a4b0", "737672"]:  # 说明 角色的时装导致血条上升
            wk.record("角色的时装导致血条上升, 识别失败, 跳过护心")
            return False
        blue_y = red_y + 3
        x, _ = wk.get_color_pos(lower_x, red_y, upper_x,
                                red_y+1, COLOR_BLOOD, order=ORDER_RLDU)
        blood_percent = int((x - lower_x) / 37 * 100)
        if x > 0 and blood_percent < HU_XIN_THRESHOLD:
            wk.record(f"当前血条百分比:{blood_percent}% < 设定的百分比:{HU_XIN_THRESHOLD}%")
            return True
        # 找下面的蓝条位置
        x, _ = wk.get_color_pos(lower_x, blue_y, upper_x,
                                blue_y+1, COLOR_INTERNAL, order=ORDER_RLDU)
        internal_percent = int((x - lower_x) / 37 * 100)
        if x > 0 and internal_percent < HU_XIN_THRESHOLD:
            wk.record(
                f"当前蓝条百分比:{internal_percent}% < 设定的百分比:{HU_XIN_THRESHOLD}%")
            return True
        return False

    @classmethod
    def calc_fighting_threshold_x(cls, setting_count, save_obj=SAVE_PEOPLE):
        idx_x_list = []
        info = FIGHT_PEOPLE_INFO if save_obj == SAVE_PEOPLE else FIGHT_BB_INFO
        for idx, low_x, _, _, _ in info:
            x = int((setting_count / 100) * 37 + low_x)
            if setting_count == 100:
                x += 1  # 说明人家在满血满蓝下仍然打药, 故意+1让它超过上限, 强行触发打药
            idx_x_list.append((idx, x))
        return idx_x_list

    @classmethod
    def calc_after_fight_threshold_x(cls, setting_count, save_obj=SAVE_PEOPLE):
        if save_obj == SAVE_PEOPLE:
            x = int(
                (setting_count / 100)
                * (X_PEOPLE_BLOOD_INTERNAL_HIGH - X_PEOPLE_BLOOD_INTERNAL_LOW)
                + X_PEOPLE_BLOOD_INTERNAL_LOW
            )
        else:
            x = int(
                (setting_count / 100)
                * (X_BB_BLOOD_INTERNAL_HIGH - X_BB_BLOOD_INTERNAL_LOW)
                + X_BB_BLOOD_INTERNAL_LOW
            )
        return x

    @classmethod
    def update_wk_fight_pb_info(cls, wk: Worker):
        wk.people_save_people_idx_x = cls.calc_fighting_threshold_x(
            wk.cfg_plan["人物战斗救人比例"], save_obj=SAVE_PEOPLE
        )

        wk.defend_bb_save_people_idx_x = cls.calc_fighting_threshold_x(
            wk.cfg_plan["防守类BB战斗救人比例"], save_obj=SAVE_PEOPLE
        )

        wk.attack_bb_save_people_idx_x = cls.calc_fighting_threshold_x(
            wk.cfg_plan["进攻类BB战斗救人比例"], save_obj=SAVE_PEOPLE
        )

        wk.people_save_bb_idx_x = cls.calc_fighting_threshold_x(
            wk.cfg_plan["人物战斗救BB比例"], save_obj=SAVE_BB
        )

        wk.defend_bb_save_bb_idx_x = cls.calc_fighting_threshold_x(
            wk.cfg_plan["防守类BB战斗救BB比例"], save_obj=SAVE_BB
        )

        wk.attack_bb_save_bb_idx_x = cls.calc_fighting_threshold_x(
            wk.cfg_plan["进攻类BB战斗救BB比例"], save_obj=SAVE_BB
        )

        wk.after_fight_people_add_x = cls.calc_after_fight_threshold_x(
            wk.cfg_plan["人物战后补充比例"], save_obj=SAVE_PEOPLE
        )

        wk.after_fight_bb_add_x = cls.calc_after_fight_threshold_x(
            wk.cfg_plan["人物战后补充比例"], save_obj=SAVE_BB
        )

    @classmethod
    def click_close_pic(cls, wk: Worker, order=ORDER_RLUD, RECT=RECT_FULL, timeout=0):
        return wk.find_pic_click(*RECT, "关闭.bmp|关闭2.bmp|关闭3.bmp", order=order, timeout=timeout)

    @classmethod
    def close_page(cls, wk: Worker):
        if not wk.find_pic(*RECT_DOWN, "界外边框.bmp"):
            return
        if not cls.click_close_pic(wk):
            wk.key_press(VK_ESC)

    @classmethod
    def is_page_open(cls, wk: Worker):
        return wk.find_pic(*RECT_DOWN, "界外边框.bmp|界外边框2.bmp|界外边框3.bmp")

    @classmethod
    def close_pages(cls, wk: Worker):
        cls.restore_default_cursor(wk)
        cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)
        for i in range(5):
            cls.close_other_talk(wk)
            if not cls.is_page_open(wk):
                break
            if not cls.click_close_pic(wk, order=rnd(0, 3)):
                # 有可能是自动框, 要取消自动
                if not cls.cancel_auto(wk):
                    wk.key_press(VK_ESC)
            msleep(500)

    @classmethod
    def get_bag_page_pos_list(cls, wk: Worker, RECT: tuple, timeout=0):
        pics = "行囊3.bmp|行囊.bmp|行囊2.bmp"
        if wk.cfg_plan["查找灰色行囊页"]:
            pics += "|行囊灰.bmp"
        return wk.find_pic_ex(*RECT, pics, timeout=timeout)

    @classmethod
    def bag_use_item(cls, wk: Worker, item_bmp: str, is_close=True, check_twice=False, is_confirm=True, item_name="", bag_open_fail_true=False):
        cls.click_confirm(wk)  # 避免弹窗干扰
        cls.close_system_page(wk)
        if not wk.is_stuck:
            cls.stop_auto_find_way(wk)
        cls.open_bag_page(wk)
        cls.close_other_talk(wk)  # 这里避免突然的对话框干扰
        x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
        if wk.is_fight:
            return True
        if x < 0:
            wk.record("背包使用物品失败, 背包未打开")
            if bag_open_fail_true:
                return True
            return False
        wk.re_move()  # 鼠标移开避免挡到
        begin_x, begin_y = x + 18, y - 300
        RECT_BAG = (begin_x, begin_y, begin_x + 260, begin_y + 300)
        pos_list = cls.get_bag_page_pos_list(wk, RECT_BAG)
        for pos in pos_list:
            _, x, y = pos
            wk.move_click(x, y)
            if wk.find_pic_r_click(*RECT_BAG, item_bmp, timeout=300):
                if is_confirm:
                    msleep(100)
                    cls.click_confirm(wk)
                if is_close:
                    cls.close_pages(wk)
                return True

        if wk.is_fight:  # 取消操作
            wk.right_click()
            wk.right_click()
            return True
        if check_twice:
            if cls.click_tidy_up(wk):
                wk.record("没找到，整理背包再找一遍")
                msleep(1000)
            return cls.bag_use_item(wk, item_bmp, is_close=is_close, check_twice=False)
        if is_close:
            cls.close_pages(wk)
        item_name = item_name or item_bmp.split("|")[0].rstrip(".bmp")
        wk.record(f"找不到 {item_name}")
        if item_name == "花露水":
            ori_map = cls.cur_map(wk)
            if ori_map in OUTSIDE_MAP_LIST:
                wk.record("正在前往购买花露水...")
                cls.buy_thing(wk, item_name, 99)
                wk.record(f"正在返回原地图:{ori_map}")
                cls.run_to_map(wk, ori_map)
        return False

    @classmethod
    def bag_item_action(cls, wk: Worker, item_bmp: str, func: Callable, is_close=True):
        cls.close_system_page(wk)
        cls.open_bag_page(wk)
        cls.close_other_talk(wk)  # 这里避免突然的对话框干扰
        x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
        if x < 0 and not wk.is_fight:
            return False
        begin_x, begin_y = x + 18, y - 300
        RECT_BAG = (begin_x, begin_y, begin_x + 260, begin_y + 300)

        pos_list = cls.get_bag_page_pos_list(wk, RECT_BAG)
        res = func(wk, item_bmp, RECT=RECT_BAG)
        if not res:
            for pos in pos_list:
                _, x, y = pos
                wk.move_click(x, y)
                time.sleep(0.2)
                res = func(wk, item_bmp, RECT=RECT_BAG)
                if res:
                    break
        if is_close:
            cls.close_pages(wk)
        if wk.is_fight or res:
            return True
        return False

    @classmethod
    def bag_use_item_all(cls, wk: Worker, item_bmp: str, is_close=True, count=2000):
        def use_cur_page_item(wk: Worker, item_bmp: str, RECT: tuple):
            for i in range(count):
                cls.do_sth_before_use_thing(wk)
                if wk.find_pic_r_click(*RECT, item_bmp, timeout=200):
                    cls.do_sth_after_use_thing(wk)
                else:
                    break
                msleep(200)
        cls.bag_item_action(wk, item_bmp, use_cur_page_item, is_close=is_close)

    @classmethod
    def do_sth_before_use_thing(cls, wk: Worker):
        pass

    @classmethod
    def do_sth_after_use_thing(cls, wk: Worker):
        cls.click_confirm(wk)

    @classmethod
    def bag_have_item(cls, wk: Worker, item_bmp: str):
        def cur_page_have_item(wk: Worker, item_bmp: str, RECT: tuple):
            return wk.find_pic(*RECT, item_bmp, timeout=800)
        return cls.bag_item_action(wk, item_bmp, cur_page_have_item, is_close=False)

    @classmethod
    def click_tidy_up(cls, wk: Worker, timeout=0):
        if not wk.cfg_plan["点击整理"]:
            return False
        if wk.find_pic_click(*RECT_FULL, "整理.bmp", timeout=timeout):
            msleep(600)
            cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)
            return True
        return False

    @classmethod
    def auto_meet_enemy_by_shortcut(cls, wk: Worker):
        wk.record("自动遇敌中...")
        cls.close_page(wk)
        cls.close_other_talk(wk)
        cls.click_confirm(wk)
        cls.use_hls(wk)
        yudi_list = wk.find_str_ex(
            *RECT_SYSTEM_BROADCAST, "鼠标点击地面退出", COLOR_QI_LING2)
        if len(yudi_list) > 3:  # 可能是卡点地图bug了, 要点地板
            wk.record("可能卡地图BUG了, 点地板解除卡点")
            wk.move_click(CX-20, CY)
            wk.move_click(CX+20, CY)
        wk.key_press_combo(VK_ALT, VK_O)
        wk.is_stuck = False

    @classmethod
    def is_system_show_fix_equib_bb(cls, wk: Worker):
        return cls.is_system_broad_show(
            wk, "将完全损坏|没有选择招式|武器和招式不匹配|忠诚度太低了"
        )

    @classmethod
    def is_system_show_fix_equib(cls, wk: Worker):
        return cls.is_system_broad_show(
            wk, "将完全损坏|没有选择招式|武器和招式不匹配"
        )

    @classmethod
    def is_system_show_fix_bb(cls, wk: Worker):
        return cls.is_system_broad_show(
            wk, "忠诚度太低了"
        )

    @classmethod
    def is_should_fix_equip_bb(cls, wk: Worker):
        if cls.IS_TEAM_TASK:
            if wk.team.need_fix_equip_bb:
                return
        else:
            if wk.need_fix_equip_bb:
                return
        if rnd(1, 5) != 5:  # 不用太频繁
            return
        if cls.is_system_show_fix_equib_bb(wk):
            if cls.IS_TEAM_TASK:
                wk.team.need_fix_equip_bb = True
            else:
                wk.need_fix_equip_bb = True
            wk.record("需要修理忠诚")

    @classmethod
    def is_popup_confirm_show(cls, wk: Worker):
        return wk.find_pic(*RECT_POPUP, "确定.bmp|确定2.bmp")

    @classmethod
    def click_confirm(cls, wk: Worker, timeout=200, RECT=RECT_FULL):
        return wk.find_pic_offset_click(*RECT, "确定.bmp|确定2.bmp|确认.bmp|确认亮.bmp", timeout=timeout, dx=-8, dy=8)

    @classmethod
    def click_cancel(cls, wk: Worker, timeout=200, RECT=RECT_POPUP):
        return wk.find_pic_click(*RECT, "取消.bmp", timeout=timeout)

    @classmethod
    def click_multi_cancel(cls, wk: Worker, timeout=0, RECT=RECT_POPUP):
        for i in range(5):
            timeout = 0 if i == 0 else 200
            if not wk.find_pic_click(*RECT, "取消.bmp", timeout=timeout):
                break

    @classmethod
    def is_ride(cls, wk: Worker):
        return wk.find_pic(*RECT_SKILL, "界_坐骑.bmp")

    @classmethod
    def pay_fine(cls, wk: Worker):
        wk.record("正在去桃花岛交罚款...")
        cls.run_to_map_run_away(wk, "桃花岛")
        if cls.find_way_npc(wk, "范歪瓜", ["交纳"], close_talk=False):
            wk.record("交罚款成功")
        else:
            wk.record("交罚款失败")
        cls.close_pages(wk)
        wk.need_pay_fine = False

    @classmethod
    def on_ride(cls, wk: Worker):
        wk.record("上坐骑")
        if not wk.have_ride:
            return
        if cls.is_ride(wk):
            wk.record("上坐骑成功")
            return
        wk.key_press(VK_F9)
        msleep(800)
        if cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP) or cls.is_system_broad_show(wk, "饱食度已经为"):
            wk.record("上坐骑失败, 未检测到坐骑技能")
            wk.have_ride = False
            return
        wk.record("上坐骑成功")

    @classmethod
    def off_ride(cls, wk: Worker):
        wk.record("下坐骑")
        wk.key_press(VK_F9)
        msleep(800)
        if cls.is_ride(wk):
            wk.key_press(VK_F9)
            msleep(800)
        if cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP):
            wk.have_ride = False

    @classmethod
    def make_team(cls, wk: Worker):
        mate_count = cls.get_teammate_count(wk)
        if mate_count == 5:
            wk.record("已经组满队伍了")
            return
        if mate_count == 0:
            cls.create_team(wk)
        if wk.cfg_plan["组满第五页"]:
            wk.record("已配置 组满第五页, 开始循环邀请...")
            while not cls.invite_mate(wk):
                wk.record("邀请队友失败, 等待1分钟再试...")
                time.sleep(60)
        else:
            wk.record("未配置 组满第五页, 只邀请一次...")
            cls.invite_mate(wk)

    @classmethod
    def invite_mate(cls, wk: Worker):
        wk.record("正在邀请队友...")
        if not cls.open_mate_page(wk):
            return False
        idx_x_y_list = wk.find_pic_ex(
            *RECT_FULL, "好友男.bmp|好友女.bmp|好友男2.bmp|好友女2.bmp")
        mate_count = 4 if len(idx_x_y_list) >= 4 else len(idx_x_y_list)
        for _, x, y in idx_x_y_list:
            wk.move_r_click(x+60, y, re_move=False)
            msleep(400)
            if not wk.find_str_click(*RECT_FULL, "邀请加队", COLOR_PLATFORM_MAP, timeout=400):
                break
            msleep(400)
        wk.record("邀请队友完毕, 等待队员同意...")
        cls.close_pages(wk)
        for _ in range(4):
            time.sleep(1)
            if cls.get_teammate_count(wk) == mate_count+1:
                wk.record("邀请队友成功!")
                return True
        wk.record("邀请队友失败!")
        return False
        
    @classmethod
    def cur_map(cls, wk: Worker, timeout=0, sleep=True):
        if wk.is_fight:
            return ""
        wk.re_move()
        wk.re_move()
        if sleep:
            msleep(100)
        map_name = wk.ocr(*RECT_CUR_MAP, COLOR_WHITE, timeout=timeout)
        return map_name

    @classmethod
    def click_world_map_to(cls, wk: Worker, x, y: int):
        cls.restore_default_cursor(wk)
        cls.close_other_talk(wk)
        cls.click_multi_cancel(wk)
        cls.open_world_map_page(wk)
        if cls.is_world_map_open(wk):
            wk.move_r_click(x, y, is_delay=False, re_move=False)
        if cls.is_world_map_open(wk):
            wk.move_click(x, y, is_delay=False)
        cls.close_pages(wk)

    @classmethod
    def search_click_npc(cls, wk: Worker, name: str, input=True):
        cls.close_other_talk(wk)
        cls.click_multi_cancel(wk)
        cls.open_find_way_page(wk)
        x, y = wk.get_pic_pos(*RECT_FULL, "搜索.bmp|搜索2.bmp", timeout=300)
        if x < 0:
            wk.record("寻路页面打开失败")
            return False
        if wk.find_str_db_click(*RECT_FULL, name, COLOR_TASK_NAME):
            return True
        if input:
            wk.move_click(x - 20, y + 6)  # 激活输入框
            wk.key_press(VK_BACK, num=4)
            msleep(400)
            wk.send_string(name)
            msleep(400)
            wk.move_click(x, y)
            return wk.find_str_db_click(*RECT_FULL, name, COLOR_TASK_NAME, timeout=800)
        return False

    @classmethod
    def is_in_fu_ben(cls, wk: Worker):
        return wk.find_str(*RECT_CHANNEL, "副本", COLOR_TASK_NAME)

    @classmethod
    def is_self_pause_leave(cls, wk: Worker, is_close=True):
        # 名字长了 队伍上会少识别前面一个字
        player_name = wk.player_name if len(
            wk.player_name) <= 6 else wk.player_name[1:]
        x, y = wk.get_str_pos(*RECT_FULL, player_name,
                              COLOR_BLACK, zk=ZK_NAME_11, timeout=400)
        if x < 0:
            wk.record("没找到自己的名字")
            if is_close:
                cls.close_pages(wk)
            return False
        if not wk.find_pic(x-70, y-3, x, y+17, "名号暂离.bmp"):
            if is_close:
                cls.close_pages(wk)
            wk.mate_check_leave = False  # 说明不是自己暂离
            return False
        return True

    @classmethod
    def get_team(cls, row: int) -> Team:
        return settings.team_list[row // 5]

    @classmethod
    def is_system_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "界_系统.bmp", timeout=timeout)

    @classmethod
    def open_system_page(cls, wk: Worker):
        for i in range(3):
            timout = 0 if i == 0 else 600
            if cls.is_system_page_open(wk, timout):
                break
            else:
                wk.key_press(VK_ESC)

    @classmethod
    def close_system_page(cls, wk: Worker, timeout=0):
        for i in range(3):
            if cls.is_system_page_open(wk):
                wk.key_press(VK_ESC)
            else:
                break
            msleep(600)

    @classmethod
    def is_ride_have_skill(cls, wk: Worker, skill_name: str):
        return wk.find_pic(*RECT_SKILL, f"{skill_name}.bmp")

    @classmethod
    def is_task_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "任务状态.bmp", timeout=timeout)

    @classmethod
    def open_task_page(cls, wk: Worker):
        if cls.is_task_page_open(wk):
            return True
        cls.close_system_page(wk)
        cls.close_page(wk)
        cls.close_other_talk(wk)
        for i in range(2):
            if cls.is_talk_open(wk) or cls.is_task_page_open(wk, 600):
                return True
            else:
                wk.key_press_combo(VK_ALT, VK_Q)
                msleep(200)
        return False

    @classmethod
    def open_task_page_quick(cls, wk: Worker):
        if cls.is_task_page_open(wk):
            return True
        cls.close_other_talk(wk)
        cls.close_page(wk)
        wk.key_press_combo(VK_ALT, VK_Q)

    @classmethod
    def region_task_desc_find_str(cls, wk: Worker, info: str, color=COLOR_TASK_NAME, zk=0, timeout=0):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp", timeout=timeout)
        if x < 0:
            return False
        return wk.find_str(x - 40, y - 130, x + 272, y - 16, info, color=color, zk=zk)

    @classmethod
    def region_task_desc_ocr(cls, wk: Worker, color=COLOR_TASK_NAME, zk=ZK_ALL_11, timeout=0):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp", timeout=timeout)
        if x < 0:
            return ""
        return wk.ocr(x - 40, y - 130, x + 272, y - 16, color=color, zk=zk).strip('_一')

    @classmethod
    def region_task_desc_ocr_one_line(cls, wk: Worker, color=COLOR_TASK_NAME, zk=ZK_ALL_11):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return ""
        return wk.ocr(x - 30, y - 130, x + 250, y - 96, color=color, zk=zk).strip('_')

    @classmethod
    def region_task_desc_get_str_pos(cls, wk: Worker, info: str, color=COLOR_TASK_NAME, zk=0):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return -1, -1
        return wk.get_str_pos(x - 40, y - 130, x + 272, y - 16, info, color=color, zk=zk)

    @classmethod
    def region_task_desc_thing_num(cls, wk: Worker):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return -1, -1
        done_count = wk.ocr(x+35, y-104, x+96, y-84,
                            COLOR_GREEN, zk=ZK_DIGIT_11)
        need_count = wk.ocr(x-32, y-125, x+54, y-103,
                            COLOR_GREEN, zk=ZK_DIGIT_11)
        return int(done_count or 0), int(need_count or 0)

    @classmethod
    def region_task_status_get_status(cls, wk: Worker, timeout=0):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp", timeout=timeout)
        if x < 0:
            return ""
        return wk.ocr(x - 34, y + 14, x+39, y+36, COLOR_TASK_NAME)

    @classmethod
    def region_task_status_click_npc(cls, wk: Worker):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return False
        # fix汪刚的刚字上面多一个像素, 导致点不到
        x, y = wk.get_color_pos(x, y, x + 141, y + 103, COLOR_GOLD)
        if x < 0:
            return False
        return wk.find_color_click(x - 10, y + rnd(1, 4), x + rnd(10, 30), y + rnd(6, 12), COLOR_GOLD, order=rnd(0, 3))

    @classmethod
    def region_task_status_get_npc_name(cls, wk: Worker, timeout=0):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp", timeout=timeout)
        if x < 0:
            return ""
        # 这个识别不了带任务截止时间的NPC名
        return wk.ocr(x+21, y+33, x + 130, y + 54, COLOR_GOLD).rstrip("一_").lstrip()

    @classmethod
    def region_task_status_get_npc_name_pro(cls, wk: Worker, timeout=0):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp", timeout=timeout)
        if x < 0:
            return ""
        name = wk.ocr(x+21, y+33, x + 130, y + 92, COLOR_GOLD)
        if not name:
            return ""
        # 这个能识别带任务截止时间的NPC名
        name = name.split("一一")[0]
        return name.strip()

    @classmethod
    def region_task_status_find_str(cls, wk: Worker, info: str, color=COLOR_TASK_NAME, zk=0):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return False
        return wk.find_str(x-35, y, x+220, y + 110, info, color=color, zk=zk)

    @classmethod
    def region_task_list_find_str(
            cls, wk: Worker, info: str, color=COLOR_TASK_NAME, zk=0
    ):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return ""
        return wk.find_str(x - 221, y - 117, x - 49, y + 227, info, color=color, zk=zk)

    @classmethod
    def region_task_list_find_str_click(
            cls, wk: Worker, info: str, color=COLOR_TASK_NAME, zk=0
    ):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return False
        return wk.find_str_click(x - 221, y - 117, x - 49, y + 227, info, color=color, zk=zk)

    @classmethod
    def region_task_list_find_pic_click(
            cls, wk: Worker, pic: str
    ):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return False
        return wk.find_pic_click(x - 221, y - 117, x - 49, y + 227, pic)

    @classmethod
    def region_task_list_find_pic_offset_click(
            cls, wk: Worker, pic: str, timeout=0
    ):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return False
        return wk.find_pic_offset_click(x - 221, y - 117, x - 49, y + 180, pic, dx=40, dy=30, timeout=timeout)

    @classmethod
    def region_task_list_get_str_pos(
            cls, wk: Worker, info: str, color=COLOR_TASK_NAME, zk=0
    ):
        x, y = wk.get_pic_pos(*RECT_FULL, "任务状态.bmp")
        if x < 0:
            return -1, -1
        return wk.get_str_pos(
            x - 221, y - 117, x - 49, y + 227, info, color=color, zk=zk
        )

    @classmethod
    def click_refresh_task_pic(cls, wk: Worker):
        return wk.find_pic_click(*RECT_FULL, "任务刷新.bmp|任务刷新2.bmp|任务刷新3.bmp")

    @classmethod
    def refresh_task_list(cls, wk: Worker):
        cls.open_task_page(wk)
        if cls.click_refresh_task_pic(wk):
            wk.record("任务刷新成功")
            msleep(600)

    @classmethod
    def do_task_query_jhwst(cls, wk: Worker):
        wk.record("询问江湖万事通...")
        cls.close_pages(wk)
        cls.from_cur_map_to_kai_feng(wk)
        if not cls.ask_jhwst(wk, "找寻|单挑|购买"):
            wk.record("询问江湖万事通失败")
            return
        wk.record("询问江湖万事通成功")
        cls.close_pages(wk)
        cls.open_task_page(wk)

    @classmethod
    def from_cur_map_to_kai_feng(cls, wk: Worker):
        cls.run_to_map(wk, "开封")

    @classmethod
    def open_task_npc_click(cls, wk: Worker, condition_func_dict={}):
        # 打开任务面板，寻路任务NPC
        cls.open_task_page(wk)
        if not cls.avoid_other_task_disturb(wk):
            wk.record("切回任务失败,按终态处理")
            raise TaskFinalStatusException("切回任务失败,按终态处理")
        task_status = cls.region_task_status_get_status(wk, timeout=200)
        if not cls.is_cur_task_desc(wk):
            cls.close_pages(wk)
            return False
        if task_status in ["成功", "失败", "过期"]:
            wk.record("任务已进入终态")
            cls.close_pages(wk)
            raise TaskFinalStatusException()
        for condition_str, func in condition_func_dict.items():
            if cls.region_task_list_find_str(wk, condition_str):
                return func(wk)
        if cls.region_task_status_click_npc(wk):
            wk.record("寻路到任务NPC...")
            wk.is_stuck = False
            return True
        wk.record("寻路任务NPC失败!")
        cls.close_pages(wk)
        return False

    @classmethod
    def avoid_other_task_disturb(cls, wk: Worker):
        # 成功切回返回True, 是当前任务的话返回True, 切回失败也返回False
        if not cls.is_task_page_open(wk):
            wk.record("任务面板未打开")
            return True  # 这种情况下也算是无干扰吧, 可能是被对话给挡了
        if cls.is_cur_task_desc(wk):
            return True
        wk.record("被其它任务干扰了")
        cls.close_other_talk(wk)
        cls.click_refresh_task_pic(wk)  # 刷新就能自动回到上次切的任务, 但有时上次也受影响
        msleep(1000)
        if cls.is_cur_task_desc(wk):
            return False
        wk.record("正在尝试切回原任务...")
        x, y = wk.get_pic_pos(*RECT_LEFT, "任务列表滑块.bmp")
        if x < 0:
            return False
        for i in range(16):
            if cls.change_to_origin_task(wk):
                return True
            if i == 11:
                return False
            # 从上往下找
            if i == 0:
                wk.move_drag_to(x, y, x, 0)
            else:
                x, y = wk.get_pic_pos(
                    *RECT_LEFT, "任务下.bmp|任务下2.bmp", order=ORDER_LRDU, timeout=400)
                if x < 0:
                    break
                wk.move_click(x + 5, y + 5, click_count=18,
                              is_sleep=False, is_delay=False)
            msleep(100)
            wk.re_move()
        return False

    @classmethod
    def is_cur_task_desc(cls, wk: Worker, timeout=0):
        npc_name = cls.region_task_status_get_npc_name(wk, timeout=timeout)
        # print(f'npc_name1:{npc_name}')
        if npc_name.startswith(("吉禾", "鲁工匠", "欧冶子", "冯知府", "震关西", "开封随从教员", "马伯乐", "路人甲", "白活老人")):
            return False
        return True

    @classmethod
    def change_to_origin_task(cls, wk: Worker):
        return True

    @classmethod
    def stop_auto_find_way(cls, wk: Worker, force=False):
        if not force and wk.is_stuck:
            return
        if force:
            cls.close_big_map(wk)
        wk.move_click(*POS_SMALL_MAP_CENTER, is_sleep=False, re_move=False)
        wk.is_stuck = True

    @classmethod
    def get_shou_huo_worker(cls, wk: Worker, category: str):
        for sh_wk in settings.worker_list:
            if sh_wk is None:
                continue
            if sh_wk.player_name == wk.cfg_plan_task[category]:
                return sh_wk
        return None
    
    @classmethod
    def go_to_trade_place(cls, wk: Worker):
        pass

    @classmethod
    def trade_with_player(cls, wk: Worker, player_name: str, pic_or_amount: str, RECT=RECT_FIND, category='', start_ts=0, filter_fn=None):
        if start_ts == 0:
            start_ts = settings.cur_time_stamp
        if settings.cur_time_stamp - start_ts > 60*60*2:
            # 避免一直递归把堆栈弄爆了
            return
        x, y = cls.get_people_name_x_y(wk, player_name, RECT)
        if category:  # 按类别交货时, 看一下接货人是否处于接货状态
            # 找一下shou_huo_name 对应的wk
            sh_wk: Worker = cls.get_shou_huo_worker(wk, category)
            if sh_wk and sh_wk.is_run:
                fail_count = 0
                for i in range(10000):
                    if i != 0:
                        msleep(5000, 10000)
                    if sh_wk.cur_task == "接货":  # 这时再找下名字
                        x, y = cls.get_people_name_x_y(wk, player_name, RECT)
                        if x < 0 and fail_count < 6:
                            fail_count += 1
                            cls.go_to_trade_place(wk) # 复位一下
                            continue
                        break  # 如果失败次数过多或者找到了
                    wk.record(f"收货人{player_name} 未在接货, 稍后再试...")

        if x < 0:
            return -1
        wk.key_press_combo(VK_ALT, VK_X)
        msleep(500)
        wk.move_relative_click(x+cls.name_calc_offset_x(player_name), y+rnd(
            70, 100), re_move=False, limit_border=True, limit_y=Y_LIMIT_CLICK)
        if not cls.wait_against_agree(wk):
            msleep(2000, 10000)
            cls.trade_with_player(
                wk, player_name, pic_or_amount, start_ts=start_ts, filter_fn=filter_fn)
            return
        wk.record("对方已同意交易, 正在交易中...")
        msleep(400)
        cls.close_other_talk(wk)
        if cls.ocr_trade_player_name(wk, timeout=600) != player_name:
            wk.record(f"交易对象不是 {player_name}, 自动取消")
            cls.close_pages(wk)
            return
        if pic_or_amount.isdigit():  # 找人要东西
            cls.trade_with_amount(wk, pic_or_amount)
        else:  # 给别人东西
            if cls.trade_with_pic(wk, pic_or_amount, filter_fn=filter_fn):  # 一次只能给8个，要给多次
                cls.trade_with_player(
                    wk, player_name, pic_or_amount, filter_fn=filter_fn)

    @classmethod
    def get_people_name_x_y(cls, wk: Worker, player_name: str, RECT=RECT_FIND):
        for i in range(2):
            x, y = wk.get_str_pos(*RECT, player_name, COLOR_WHITE+"|"+COLOR_NAME_VIP,
                                  zk=ZK_NAME_11, timeout=400)
            if x > 0:
                return x, y
            cls.close_other_talk(wk)
            wk.record(f"没找到 {player_name}, 按下F12重试...")
            wk.key_press(VK_F12)
            msleep(600)
        x, y = wk.get_str_pos(*RECT, player_name, COLOR_WHITE+"|"+COLOR_NAME_VIP,
                              zk=ZK_NAME_11, timeout=400)
        if x > 0:
            return x, y
        wk.record(f"还是没找到 {player_name}, 放弃交易...")
        return -1, -1

    @classmethod
    def name_calc_offset_x(cls, name: str):
        res = 0
        for ch in name:
            if ch in "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789":
                res += 3
            else:
                res += 6.5
        return int(res)

    @classmethod
    def wait_against_agree(cls, wk: Worker):
        wk.record("等待对方同意交易...")
        for i in range(10):
            cls.close_other_talk(wk)
            msleep(600)
            if cls.is_trade_page_open(wk):
                return True
        return False

    @classmethod
    def trade_with_amount(cls, wk: Worker, amount: str):
        wk.record(f"正在输入金额: {amount}")
        wk.move_click(345, 516, re_move=False)
        wk.move_click(364, 529, re_move=False)
        wk.send_string(amount)
        msleep(100)
        cls.click_confirm(wk)
        msleep(100)
        # 自己做完操作了, 点击锁定
        cls.lock_comfirm(wk)

    @classmethod
    def trade_with_pic(cls, wk: Worker, pic: str, filter_fn=None):
        # 返回 False代表物品全部交易完了不用继续了, True代表要继续交易
        if cls.trade_cur_page(wk, pic, filter_fn=filter_fn):
            return True
        pos_list = cls.get_bag_page_pos_list(wk, RECT_RIGHT, timeout=600)
        for pos in pos_list:  # 换页
            _, x, y = pos
            wk.move_click(x, y)
            msleep(400)
            if cls.trade_cur_page(wk, pic, filter_fn=filter_fn):
                return True
            # 如果返回False，说明交易栏没有满，继续下一页
        cls.lock_comfirm(wk)
        return False

    @classmethod
    def trade_cur_page(cls, wk: Worker, pic: str, filter_fn=None):
        # 交易当前页物品，如果返回True，说明交易栏满了，先成交，然后继续交易
        if cls.right_click_cur_page_things(wk, pic, MAX_COUNT=8, filter_fn=filter_fn):
            # 如果返回True，说明交易栏满了, 要继续交易
            cls.lock_comfirm(wk)
            msleep(600)
            if not cls.is_system_broad_show(wk, "东西太多了无法交易"):
                return True
        return False

    @classmethod
    def lock_comfirm(cls, wk: Worker):
        cls.trade_lock(wk)
        # 等待对方锁定
        cls.wait_against_lock(wk)
        cls.click_confirm(wk)

    @classmethod
    def trade_lock(cls, wk: Worker):
        for i in range(3):
            if not wk.find_pic_click(*RECT_TRADE_LOCK_SELF, "交易未锁定.bmp", timeout=400):
                break

    @classmethod
    def ocr_trade_player_name(cls, wk: Worker, timeout=0):
        return wk.ocr(*RECT_TRADE_NAME, COLOR_TRADE_NAME, zk=ZK_NAME_11, timeout=timeout)

    @classmethod
    def wait_against_lock(cls, wk: Worker, timeout=10000):
        sleep_gap = 400
        loop_count = int(settings.exec_rate * timeout//sleep_gap)
        wk.record("等待对方锁定...")
        for _ in range(loop_count):
            cls.close_other_talk(wk)
            if cls.is_popup_show_info(wk, "双方都锁定以后", timeout=0):
                cls.click_confirm(wk, RECT=RECT_POPUP)
            if cls.is_popup_show_info(wk, "邀请你和他交易", timeout=0):
                cls.click_cancel(wk)
            if wk.find_pic(*RECT_TRADE_LOCK_AGAINST, "交易锁定.bmp"):
                wk.find_pic_click(*RECT_TRADE_LOCK_SELF, "交易未锁定.bmp")
                if cls.click_confirm(wk, RECT=RECT_TRADE_CONFIRM):  # 自己确认
                    wk.record("对方已锁定, 己方已确认...")
                wk.record("等待对方确认...")
            if not cls.is_trade_page_open(wk):  # 说明对方取消或者确认
                wk.record("交易已完成")
                return True
            msleep(sleep_gap)
        wk.record("交易超时,自动关闭")
        cls.close_pages(wk)
        return False

    @classmethod
    def is_trade_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "交易未锁定.bmp|交易锁定.bmp", timeout=timeout)

    @classmethod
    def terse_bb(cls, wk: Worker):
        wk.record("正在精炼BB...")
        if not cls.open_terse_page(wk):
            cls.close_pages(wk)
            return
        msleep(500)
        x, y = wk.get_pic_pos(*RECT_TERSE_BB, "下.bmp", timeout=200)
        fail_count = 0
        for _ in range(100):
            if fail_count >= 2:
                break
            if not wk.find_str_click(*RECT_TERSE_BB, "麒麟", COLOR_WHITE, timeout=400):
                fail_count += 1
                if x > 0:
                    wk.move_click(x + 2, y + 2, click_count=3)  # 往下拉一点再识别
                    continue
            fail_count = 0
            msleep(400)
            if not wk.find_pic(*RECT_TERSE_BB, "白麒麟选中.bmp", timeout=200):
                wk.record("精炼时选中白色麒麟BB失败")
                break
            if not wk.find_pic_click(*RECT_FULL, "炼化随从.bmp"):
                break
            msleep(600)
            if cls.click_confirm(wk, RECT=RECT_POPUP):
                msleep(400)
                if cls.is_popup_show_info(wk, "包裹已满不能进行炼化"):
                    wk.record("包裹已满不能进行炼化")
                    wk.need_clear_bag = True
                    break
        wk.record(f"精炼BB完成")
        msleep(600)
        cls.click_confirm(wk)
        cls.close_pages(wk)

    @classmethod
    def is_quick_func_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_LEFT, "快捷随从.bmp", timeout=timeout)

    @classmethod
    def open_quick_func_page(cls, wk: Worker):
        for i in range(5):
            timeout = 0 if i == 0 else 600
            if cls.is_quick_func_page_open(wk, timeout):
                break
            else:
                wk.move_click(*POS_QUICK_FUNC)
            if cls.click_cancel(wk, RECT=RECT_FULL):
                wk.record("打开快捷面板失败, 已点击取消")
                return

    @classmethod
    def open_terse_page(cls, wk: Worker):
        cls.open_quick_func_page(wk)
        if not wk.find_pic_click(*RECT_LEFT, "快捷随从.bmp", timeout=200):
            wk.record("快捷页面打开失败")
            return False
        msleep(600)
        if not wk.find_str_click(*RECT_LEFT, "精炼", COLOR_BLACK, timeout=400):
            wk.record("点击 精炼 失败")
            return False
        msleep(600)
        return True

    @classmethod
    def open_shoushi_page(cls, wk: Worker):
        cls.open_quick_func_page(wk)
        cls.close_other_talk(wk)
        if not wk.find_str_click(*RECT_LEFT, "装备", COLOR_BLACK, zk=ZK_BOLD_11, timeout=400):
            wk.record("快捷页面打开失败")
            return False
        msleep(600)
        cls.close_other_talk(wk)
        if not wk.find_str_click(*RECT_LEFT, "打造", COLOR_BLACK, zk=ZK_BOLD_11, timeout=400):
            wk.record("点击 打造 失败")
            return False
        msleep(600)
        cls.close_other_talk(wk)
        if not wk.find_str_click(*RECT_LEFT, "首饰", COLOR_BLACK, zk=ZK_BOLD_11, timeout=400):
            wk.record("点击 首饰 失败")
            return False
        msleep(600)
        return True

    @classmethod
    def open_xun_ma_page(cls, wk: Worker):
        cls.open_quick_func_page(wk)
        if not wk.find_pic_click(*RECT_LEFT, "快捷坐骑.bmp", timeout=200):
            wk.record("快捷页面打开失败")
            return False
        msleep(600)
        if not wk.find_pic_click(*RECT_LEFT, "坐骑驯化.bmp"):
            return False
        msleep(600)
        if not wk.find_str_click(*RECT_LEFT, "编制驯马绳", COLOR_BLACK, timeout=400):
            wk.record("点击 编制驯马绳 失败")
            return False
        msleep(600)
        return True

    @classmethod
    def bian_xun_ma(cls, wk: Worker):
        wk.record("正在编制驯马绳...")
        if not cls.open_xun_ma_page(wk):
            cls.close_pages(wk)
            return
        msleep(500)
        for _ in range(100):
            wk.move_click(*POS_XUNMA_NUM)
            msleep(300)
            wk.send_string("99")
            wk.move_click(*POS_XUNMA_CONFIRM)
            msleep(400)
            if cls.is_popup_show_info(wk, "线不足"):
                wk.record("材料不足")
                break
            if cls.is_popup_show_info(wk, "金钱不足"):
                wk.record("金钱不足")
                break
            if cls.is_popup_show_info(wk, "背包空间不足"):
                wk.record("包裹已满")
                wk.need_clear_bag = True
                break
        wk.record("编制驯马绳完成")
        msleep(600)
        cls.click_confirm(wk)
        cls.close_pages(wk)

    @classmethod
    def is_team_platform_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "界_组队平台.bmp", timeout=timeout)

    @classmethod
    def open_team_platform_page(cls, wk: Worker):
        for i in range(2):
            timeout = 0 if i == 0 else 600
            if cls.is_team_platform_page_open(wk, timeout=timeout):
                break
            else:
                wk.move_click(*POS_TEAM_PLATFORM)

    @classmethod
    def switch_to_taohuadao(cls, wk: Worker):
        cls.open_team_platform_page(wk)
        if cls.click_confirm(wk, RECT=RECT_POPUP):
            return False
        if cls.is_team_platform_page_open(wk):
            wk.move_click(305, 131, is_sleep=False, re_move=False)
            wk.move_click(244, 197)
        return True

    @classmethod
    def open_clan_page(cls, wk: Worker):
        wk.move_click(*POS_CLAN)
        msleep(600)
        if cls.click_confirm(wk):
            return False
        return True

    @classmethod
    def is_bb_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "界_bb.bmp", timeout=timeout)

    @classmethod
    def open_bb_page(cls, wk: Worker):
        for i in range(3):
            timeout = 0 if i == 0 else 600
            if cls.is_bb_page_open(wk, timeout=timeout):
                break
            else:
                wk.move_click(*POS_BB_AVATAR)

    @classmethod
    def close_bb_page(cls, wk: Worker):
        for i in range(3):
            timeout = 0 if i == 0 else 600
            if not cls.is_bb_page_open(wk, timeout=timeout):
                break
            else:
                wk.key_press_combo(VK_ALT, VK_P)

    @classmethod
    def is_bag_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "界_背包.bmp", timeout=timeout)

    @classmethod
    def open_bag_page(cls, wk: Worker):
        cls.restore_default_cursor(wk)
        for i in range(3):
            timeout = 0 if i == 0 else 600
            if cls.is_bag_page_open(wk, timeout=timeout):
                break
            else:
                wk.key_press_combo(VK_ALT, VK_E)
        wk.find_pic_click(*RECT_FULL, "背包全.bmp")

    @classmethod
    def is_world_map_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "桃花岛.bmp|恶人谷.bmp", timeout=timeout)

    @classmethod
    def open_world_map_page(cls, wk: Worker):
        cls.handle_pass_map(wk)
        cls.close_other_talk(wk)
        cls.click_close_pic(wk, order=rnd(0, 3))
        for i in range(3):
            timeout = 0 if i == 0 else 600
            if cls.is_world_map_open(wk, timeout=timeout):
                break
            else:
                wk.key_press_combo(VK_ALT, VK_M)

    @classmethod
    def handle_pass_map(cls, wk: Worker):
        if not cls.is_popup_confirm_show(wk):
            return
        if cls.is_popup_show_info(wk, "级才能到|等级低于"):
            wk.record("等级不够, 无法进入")
            wk.re_move()
            msleep(500)
            if cls.cur_map(wk) == "金风山道":
                cls.cross_map(wk, "玄霜浅滩")
            else:
                cls.cross_map(wk, "青云幽谷")
        cls.click_confirm(wk)
        
    @classmethod
    def get_double(cls, wk: Worker, double_time: str):
        cls.back_to_kai_feng(wk)
        cls.talk_with_cur_map_npc(wk, "宋双双", ["领取双倍", double_time])

    @classmethod
    def is_team_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "离开队伍.bmp|离开队伍2.bmp", timeout=timeout)

    @classmethod
    def open_team_page(cls, wk: Worker):
        for i in range(3):
            timeout = 0 if i == 0 else 600
            if cls.is_team_open(wk, timeout=timeout):
                return True
            else:
                wk.key_press_combo(VK_ALT, VK_T)
        wk.move_r_click(CX, CY)
        cls.click_close_pic(wk, order=rnd(0, 3), timeout=400)

    @classmethod
    def is_activity_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "界_活动.bmp", timeout=timeout)

    @classmethod
    def open_activity_page(cls, wk: Worker):
        for i in range(4):
            timeout = 0 if i == 0 else 600
            if cls.is_activity_page_open(wk, timeout=timeout):
                return True
            wk.key_press_combo(VK_ALT, VK_G)

    @classmethod
    def is_fight_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "内功.bmp", timeout=timeout)

    @classmethod
    def open_fight_page(cls, wk: Worker):
        for i in range(4):
            timeout = 0 if i == 0 else 600
            if cls.is_fight_page_open(wk, timeout=timeout):
                return True
            wk.key_press_combo(VK_ALT, VK_Z)

    @classmethod
    def is_big_map_open(cls, wk: Worker, timeout=0):
        return not wk.find_str(*RECT_MAP_HOUR, "时", COLOR_WHITE, timeout=timeout)

    @classmethod
    def open_big_map(cls, wk: Worker):
        if wk.is_fight:
            return
        cls.click_close_pic(wk)
        for i in range(3):
            timeout = 0 if i == 0 else 600
            if cls.is_talk_open(wk) or cls.is_big_map_open(wk, timeout=timeout):
                break
            else:
                wk.key_press(VK_TAB)

    @classmethod
    def close_big_map(cls, wk: Worker):
        if wk.is_fight:
            return
        cls.click_close_pic(wk)
        for i in range(3):
            timeout = 0 if i == 0 else 600
            if cls.is_talk_open(wk) or not cls.is_big_map_open(wk, timeout=timeout):
                break
            else:
                wk.key_press(VK_TAB)

    @classmethod
    def open_mate_page(cls, wk: Worker):
        for _ in range(5):
            if cls.is_mate_page_open(wk):
                return True
            if wk.find_pic_click(*RECT_FULL, "好友.bmp"):
                msleep(600)
            if not wk.find_pic_click(*RECT_FULL, "队友页未选中.bmp"):
                wk.key_press_combo(VK_ALT, VK_F)
            msleep(600)
            wk.find_pic_click(*RECT_FULL, "友.bmp")
        return False

    @classmethod
    def is_mate_page_open(cls, wk: Worker):
        return wk.find_pic(*RECT_FULL, "队友页.bmp")

    @classmethod
    def open_small_account_page(cls, wk: Worker):
        for _ in range(5):
            if cls.is_small_account_page_open(wk):
                return True
            if wk.find_pic_click(*RECT_FULL, "好友.bmp"):
                msleep(600)
            if not wk.find_pic_click(*RECT_FULL, "小号页未选中.bmp"):
                wk.key_press_combo(VK_ALT, VK_F)
            msleep(600)
            wk.find_pic_click(*RECT_FULL, "友.bmp")
        return False

    @classmethod
    def is_small_account_page_open(cls, wk: Worker):
        return wk.find_pic(*RECT_FULL, "小号页.bmp")

    @classmethod
    def is_find_way_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "搜索.bmp|搜索2.bmp", timeout=timeout)

    @classmethod
    def open_find_way_page(cls, wk: Worker):
        for i in range(5):
            timeout = 0 if i == 0 else 600
            if cls.is_find_way_page_open(wk, timeout=timeout):
                break
            elif i == 0:
                wk.key_press_combo(VK_ALT, VK_N)
            else:
                wk.find_pic_click(*RECT_RIGHT_TOP, "寻路.bmp|寻路2.bmp")
                msleep(600)

    @classmethod
    def is_people_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "名号.bmp", timeout=timeout)

    @classmethod
    def ocr_player_name(cls, wk: Worker):
        wk.record("正在识别玩家名字...")
        cls.close_other_talk(wk)
        cls.click_close_pic(wk)
        wk.key_press_combo(VK_ALT, VK_C)  # 先关掉复位到人物基本tab
        for i in range(3):
            msleep(200)
            x, y = wk.get_pic_pos(*RECT_FULL, "名号.bmp", timeout=600)
            if x > 0:
                player_name = wk.ocr(
                    x + 20, y - 8, x+136, y+20, COLOR_WHITE+"|"+COLOR_NAME_VIP, zk=ZK_NAME_11, timeout=300)
                if player_name == "江湖小侠":  # 这个是识别错了
                    msleep(400)
                    continue
                if i < 2 and ("_" in player_name or "一" in player_name):
                    continue
                if len(player_name) < 2:
                    continue
                wk.record(f"玩家名字是:{player_name}")
                return player_name
            else:
                wk.key_press_combo(VK_ALT, VK_C)
        wk.record(f"玩家名字识别失败")
        return ""

    @classmethod
    def find_way_npc(
            cls, wk: Worker, npc_name: str, talk_items: list, until_func=None, reply_first=False,
            input=True, close_talk=True, handle_fight=True, close_find_way=True, fight_run_away=False,
            click_doing=False
    ):
        # 打开寻路页搜索NPC, 并对话
        if until_func and until_func(wk):
            return True
        color = COLOR_GREEN + "|" + COLOR_CYAN
        dx = cls.name_calc_offset_x(npc_name)
        for i in range(rate(1200)):
            cls.click_cancel(wk, timeout=0)  # 避免其它人点交易
            cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)  # 避免对方的物品有所变换
            if handle_fight and wk.is_fight:
                if fight_run_away:
                    wk.should_run_away = True
                cls.fight_operation(wk)
                if fight_run_away:
                    wk.should_run_away = False
            if cls.is_talk_open(wk):
                if reply_first:
                    color = COLOR_TALK_ITEM_TASK
                    if click_doing:
                        color += "|" + COLOR_TALK_ITEM_TASK_DOING
                    if wk.find_str_click(*RECT_TALK, "任务", color, timeout=100):
                        cls.close_other_talk(wk)
                talk_name = cls.get_talk_name(wk)
                # 如果对话的人是对的, 没找到对应的选项也要跳出
                # npc_name可能是"山崖|悬崖"
                case1 = (npc_name == "闭关" and talk_name in [
                    "执事使", "当值沙弥", "当值弟子", "执灯道士", "掌灯使"])
                case2 = (talk_name in npc_name) if "|" in npc_name else (
                    npc_name in talk_name)
                if case1 or case2:
                    wk.record(f"正在和 {talk_name} 对话...")
                    if talk_items:
                        cls.talk_click_items(
                            wk, talk_items, close_talk=close_talk)
                    break
            if i == 0:
                cls.click_close_pic(wk, RECT=RECT_RIGHT)
            if i == 0 or wk.is_stuck:
                if not cls.search_click_npc(wk, npc_name, input=input):
                    return False
                # 再触发点击名字下面的NPC, 以防寻路不出对话
                x, y = wk.get_str_pos(*RECT_FIND, npc_name, color)
                if 0 < x < 810:  # x靠右怕点到祥瑞
                    wk.move_relative_click(
                        x + dx + rnd(-2, 2), y + rnd(90, 110), limit_border=True, limit_y=Y_LIMIT_CLICK)
            msleep(400)
        if close_find_way:
            cls.close_find_way_page(wk)
        if until_func:
            return until_func(wk)
        return True

    @classmethod
    def talk_with_cur_map_npc(cls, wk: Worker, npc_name: str, talk_items: list, until_func=None, fight_run_away=False, close_talk=True, use_fast=True, reply_first=False, click_doing=False, only_reply=False, map_name="开封"):
        if until_func and until_func(wk):
            return True
        if wk.is_fight:
            return True
        npc_pos = NAME_POS_DICT.get(npc_name)
        if not npc_pos:
            wk.record(f"未找到NPC:{npc_name} 的坐标")
            return False

        dx = cls.name_calc_offset_x(npc_name)
        for i in range(rate(600)):
            cls.click_cancel(wk, timeout=0)  # 避免其它人点交易
            cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)  # 避免对方的物品有所变换
            if wk.is_fight:
                if fight_run_away:
                    wk.should_run_away = True
                cls.fight_operation(wk)
                if fight_run_away:
                    wk.should_run_away = False
            if cls.is_talk_open(wk):
                talk_name = cls.get_talk_name(wk)
                wk.record(f"正在和 {talk_name} 对话...")
                if reply_first:
                    color = COLOR_TALK_ITEM_TASK
                    if click_doing:
                        color += "|" + COLOR_TALK_ITEM_TASK_DOING
                    if wk.find_str_click(*RECT_TALK, "任务", color, timeout=100):
                        if close_talk:
                            cls.close_other_talk(wk)
                        if only_reply:
                            return None
                # 如果对话的人是对的, 没找到对应的选项也要跳出
                if talk_name and (npc_name in talk_name or talk_name in npc_name):
                    if talk_items:
                        cls.talk_click_items(
                            wk, talk_items, close_talk=close_talk, use_fast=use_fast, check_twice=True)
                    break
                cls.close_other_talk(wk)
            if rnd(0, 5) == 1:
                wk.key_press(VK_F12)
            # 这里找名字 容易找到上面的公告上的名字
            x, y = wk.get_str_pos(*RECT_FIND_SMALL, npc_name,
                                  COLOR_GREEN + "|" + COLOR_CYAN)
            if 0 < x < 810:  # x靠右怕点到祥瑞
                wk.move_relative_click(
                    x + dx + rnd(-2, 2), y + rnd(70, 90), limit_border=True, limit_y=Y_LIMIT_CLICK)
                msleep(600)
                continue
            if i == 0 or wk.is_stuck:
                cls.big_map_click(wk, *npc_pos)
            msleep(400)
            if cls.cur_map(wk) != map_name:  # 有时点了没过图, 过了几秒又过了的情况
                return True
        if until_func:
            for i in range(5):
                msleep(300)
                if until_func(wk):
                    return True
            return False
        return True

    @classmethod
    def close_find_way_page(cls, wk: Worker):
        x, y = wk.get_pic_pos(*RECT_LEFT, "搜索.bmp|搜索2.bmp")
        if x > 0:
            wk.move_click(x + 87, y - 426)
            msleep(500)

    @classmethod
    def is_bag_full(cls, wk: Worker):
        return wk.find_pic(*RECT_SKILL, "包满.bmp")

    @classmethod
    def click_map_find_map_npc(cls, wk: Worker, npc_name: str, x: int, y: int, talk_items: list):
        dx = 16 + 11 * (len(npc_name) - 2)
        for i in range(300):
            wk.find_str_offset_click(*RECT_FIND, npc_name, COLOR_GREEN, dx=dx,
                                     dy=rnd(60, 80), limit_border=True, limit_y=Y_LIMIT_CLICK)
            msleep(600)
            if cls.is_talk_open(wk):
                if cls.talk_click_items(wk, talk_items):
                    break
            if i == 0 or wk.is_stuck:
                cls.big_map_click(wk, x, y)

    @classmethod
    def task_npc_find_way(cls, wk: Worker, specify_item="", color=COLOR_TALK_ITEM, npc_name="", npc_map=""):
        # 打开任务页点击黄字NPC
        click_count = 0
        npc_name_clicked = False
        for i in range(rate(2500)):
            if wk.is_fight:
                cls.fight_operation(wk)
                click_count = 0
            cls.handle_pass_map(wk)
            if cls.is_talk_open(wk):
                if specify_item and cls.talk_click_specify_item(wk, specify_item, color=color):
                    cls.talk_click_first_item(wk)
                    return True
                cls.close_other_talk(wk)
            if i % 40 == 0 or wk.is_stuck:
                if click_count > 30:  # 点击次数达到后, 点下远处地板
                    cls.handle_find_way_often(wk)
                    click_count -= 5
                click_count += 1
                if not npc_name_clicked:  # 还没点击npc, 就优先点击npc名字
                    if npc_name and not cls.is_task_page_open(wk) and cls.talk_npc_in_place(wk, npc_name, timeout=200):
                        npc_name_clicked = True
                        if cls.is_talk_open(wk, timeout=200):
                            continue
                    cls.open_task_npc_click(wk)  # 这个会关对话的
                else:
                    cls.open_task_npc_click(wk)  # 这个会关对话的
                    if npc_name and not cls.is_task_page_open(wk) and cls.talk_npc_in_place(wk, npc_name):
                        npc_name_clicked = True
                        msleep(600)
                        continue
            msleep(400)
        wk.record("寻路超时, 自动结束")
        return False

    @classmethod
    def task_npc_find_way_run_away(cls, wk: Worker, specify_item="", color=COLOR_TALK_ITEM, npc_name="", npc_map=""):
        wk.should_run_away = True
        ret = cls.task_npc_find_way(wk, specify_item, color, npc_name, npc_map)
        wk.should_run_away = False
        return ret

    @classmethod
    def fast_fly_kaifeng(cls, wk: Worker, npc_name: str):
        if npc_name:
            wk.key_press_combo(VK_ALT, VK_WAVE)
            wk.record(f"使用超级遁甲 到:{npc_name}")
            msleep(200)
            if wk.find_str_db_click(
                    *RECT_FULL,
                    npc_name,
                    COLOR_DUN_JIA_NPC,
                    zk=ZK_ALL_9,
                    timeout=800,
            ):
                wk.record(f"成功点击超遁NPC:{npc_name}")
                msleep(800)
                return True
            else:
                wk.record(f"双击超遁NPC失败:{npc_name}")
        return False

    @classmethod
    def fast_fly(cls, wk: Worker):
        wk.key_press_combo(VK_ALT, VK_WAVE)
        x, y = wk.get_str_pos(
            *RECT_FULL, "任务", COLOR_TASK_NAME, timeout=800)
        if x > 0:
            npc_name = wk.ocr(x + 50, y-5, x+140, y+25,
                              COLOR_GOLD).rstrip('一_')
            wk.record(f"使用超级遁甲天书飞到{npc_name}附近")
            wk.move_click(x + 65, y + 8)
            msleep(500)
            return npc_name
        else:
            wk.record("使用超级遁甲天书失败")
            cls.close_pages(wk)
            return ""

    @classmethod
    def handle_find_way_often(cls, wk: Worker):
        wk.record("寻路点击次数过多, 尝试解除卡点...")
        if rnd(0, 2):
            wk.move_click(657, 88)
        else:
            wk.move_click(243, 278)

    @classmethod
    def ask_jhwst(cls, wk, talk_item: str):
        return cls.talk_with_cur_map_npc(wk, "江湖万事通", ["打听任务消息", talk_item, "确定"])

    @classmethod
    def trade_thing_specify_amount(cls, wk: Worker, pic_name: str, amount: int):
        def use_thing_amount():
            if wk.find_pic_r_click(*RECT_RIGHT, pic_name, timeout=300, re_move=False):
                if not cls.input_thing_number(wk, amount):
                    wk.record('输入数量失败')
                    return False
                cls.click_confirm(wk, RECT=RECT_RIGHT)
                msleep(400)
                return True
            return False
        if use_thing_amount():
            return True
        pos_list = cls.get_bag_page_pos_list(wk, RECT_RIGHT, timeout=300)
        for pos in pos_list:
            _, x, y = pos
            wk.move_click(x, y)
            msleep(400)
            if use_thing_amount():
                return True
        return False

    @classmethod
    def input_thing_number(cls, wk: Worker, num: int, RECT=RECT_RIGHT, again=True):
        time.sleep(0.2)
        if not wk.find_pic_click(*RECT, "选数量.bmp", timeout=600, re_move=False):
            wk.record("数量不足")
            return False
        time.sleep(0.2)
        wk.key_press(VK_BACK, 3, delay=20)
        time.sleep(0.2)
        wk.send_string(str(num))
        time.sleep(0.2)
        # 检查下数量是否跟设定的一样
        x, y = wk.get_pic_pos(*RECT, "选数量.bmp")
        if x > 0:
            count = wk.ocr(x-80, y-10, x, y+30, COLOR_WHITE, zk=ZK_DIGIT_11)
            if count <= str(num):  # 不然的话二组物品最多交一组
                return True
            wk.record(f"数量设定为{num}, 实际输入为{count}, 正在重新输入...")
            if again:
                return cls.input_thing_number(wk, num, RECT, again=False)
        return False

    @classmethod
    def right_click_thing_check_talk_info(cls, wk: Worker):
        # 通过检查返回True, 失败抛异常
        return True

    @classmethod
    def right_click_do_something(cls, wk: Worker, RECT=RECT_SELL_THING):
        cls.click_confirm(wk, RECT=RECT, timeout=0)

    @classmethod
    def right_click_cur_page_things(cls, wk: Worker, pic: str, RECT=RECT_SELL_THING, MAX_COUNT=None, filter_fn=None):
        # 没找到没交 或者 交了没交满, 返回False, 交满了返回True
        if filter_fn:
            return filter_fn(wk, pic, RECT, MAX_COUNT, click=True) > MAX_COUNT
        before_x, before_y = -1, -1
        for i in range(COUNT_ONE_BAG_THING):
            if cls.is_talk_open(wk):
                cls.right_click_thing_check_talk_info(wk)
                cls.close_other_talk(wk)  # 防止奇遇
            x, y = wk.get_pic_pos(*RECT, pic)
            if x == -1:
                return False
            if (x, y) == (before_x, before_y) or (MAX_COUNT and i == MAX_COUNT):
                # 卖出栏已满放不进去 或者说已经达到数量了，所以坐标没变
                return True
            before_x, before_y = x, y
            wk.move_r_click(x + rnd(2, 4), y + rnd(2, 4))
            time.sleep(0.2)
            if cls.right_click_do_something(wk, RECT=RECT):
                # 主要给上架用, 这样就知道有没有上架成功
                return True
            time.sleep(0.2)
        return False

    @classmethod
    def right_click_cur_page_things_left_one(cls, wk: Worker, pic: str, RECT=RECT_SELL_THING, MAX_COUNT=8, filter_fn=None):
        # 没找到没交 或者 交了没交满, 返回False, 交满了返回True
        wk.record(f"卡格子, 每组物品都保留一个")
        pos_list = wk.find_pic_ex(*RECT, pic, timeout=200)
        deliver_count = 0  # 已交的组数
        for _, x, y in pos_list:
            if cls.is_talk_open(wk):
                cls.right_click_thing_check_talk_info(wk)
                cls.close_other_talk(wk)  # 防止奇遇
            # 先识别下物品的数量
            num = wk.ocr(x, y, x+48, y+48, COLOR_WHITE, zk=ZK_DIGIT_9)
            if not num:
                continue
            wk.move_r_click(x + rnd(2, 4), y + rnd(2, 4), re_move=False)
            msleep(200)
            wk.find_pic_click(*RECT, "数量减.bmp", timeout=200)
            cls.click_confirm(wk, RECT=RECT)
            msleep(200)
            deliver_count += 1
            if deliver_count >= MAX_COUNT:
                return True
        return False

    @classmethod
    def wait_for_fight(cls, wk: Worker):
        for _ in range(10):
            if wk.is_fight:
                return True
            msleep(300)
        return False

    @classmethod
    def is_meet_call_bb_condition(cls, wk: Worker):
        if wk.is_called_bb:  # 本次战斗已经唤出过BB了, 就直接返回
            return False
        if not wk.cfg_plan["唤出BB"]:
            return False
        if not cls.is_people_action(wk):
            return False
        bb_name = wk.cfg_plan["唤出BB名字"]
        setting_percent = wk.cfg_plan["唤出BB血内比例"]
        setting_round = wk.cfg_plan["唤出BB回合数"]
        if not bb_name:
            wk.record("未设置唤出BB名字!")
            return False
        if wk.cur_round > setting_round:
            wk.record(f"超过设定回合数{setting_round}, 唤出BB:{bb_name}")
            return True
        _, low_x, _, red_y, blue_y = FIGHT_PEOPLE_INFO[wk.player_idx]
        x = int((setting_percent / 100) * 37 + low_x)
        if wk.get_pos_color(low_x-1, red_y) != "a4a4b0":  # 说明 角色的时装导致血条上升
            red_y -= 30
            blue_y -= 30
        if wk.get_pos_color(x, red_y) != COLOR_BLOOD or \
                wk.get_pos_color(x, blue_y) != COLOR_INTERNAL:
            wk.record(f"血内低于设定阈值{setting_percent}, 唤出BB:{bb_name}")
            return True
        return False

    @classmethod
    def walk_to_run(cls, wk: Worker):
        if wk.find_pic(*RECT_WALK, "走路.bmp"):
            wk.key_press_combo(VK_ALT, VK_R)
            msleep(600)
            wk.record("切换为跑步")

    @classmethod
    def run_left_right_click_floor(cls, wk: Worker):
        for i in range(100):
            # 随机选择左右点击顺序，避免固定模式导致偏移
            if random.choice([True, False]):
                # 随机：先右后左
                wk.move_click(CX + 400, CY, re_move=False, is_sleep=False)
                msleep(100)
                wk.move_click(CX - 400, CY, re_move=False, is_sleep=False)
                msleep(100)
            else:
                # 随机：先左后右
                wk.move_click(CX - 400, CY, re_move=False, is_sleep=False)
                msleep(100)
                wk.move_click(CX + 400, CY, re_move=False, is_sleep=False)
                msleep(100)
            if cls.is_talk_open(wk):
                cls.close_other_talk(wk)
            if wk.is_fight:
                break

    @classmethod
    def is_depot_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "包裹.bmp|包裹2.bmp", timeout=timeout)

    @classmethod
    def is_gift_enable(cls, wk: Worker):
        wk.move_to(*POS_GIFT)
        res = wk.find_str(*RECT_GIFT, "可领取", COLOR_GREEN, timeout=800)
        wk.re_move()
        return res

    @classmethod
    def is_fest_enable(cls, wk: Worker):
        return wk.find_pic(*RECT_FEST, "国庆奖励.bmp")

    @classmethod
    def get_gift(cls, wk: Worker):
        cls.stop_auto_find_way(wk, force=True)
        wk.move_click(*POS_GIFT)
        msleep(600)
        wk.record("领取祥瑞...")
        count = 0
        # 祥瑞
        x, y = wk.get_str_pos(*RECT_TODAY_TIANJIANG, "降祥", COLOR_WHITE)
        if x > 0 and wk.find_str(x-10, y+53, x+45, y+100, "可领取", COLOR_GREEN):
            count += 1
            wk.move_click(x+17, y+50)
            msleep(600)
            wk.find_pic_click(*RECT_FULL, "确定5.bmp", timeout=300)
            wk.find_pic_click(*RECT_FULL, "确定5.bmp")  # 确保点掉
            wk.record("已领取祥瑞")
        # 签到
        x, y = wk.get_str_pos(*RECT_TODAY_TIANJIANG, "日签", COLOR_WHITE)
        if x > 0 and wk.find_str(x-15, y+53, x+45, y+100, "可领取", COLOR_GREEN):
            count += 1
            wk.move_click(x+17, y+50)
            msleep(600)
            wk.move_click(*POS_QIANDAO)
            if cls.click_confirm(wk):
                wk.record("背包满了无法签到")
                if cls.TASK_NAME == "单人刷野" and wk.cfg_plan_task["清理背包"]:
                    wk.need_clear_bag = True
            cls.click_close_pic(wk)
            wk.record("已签到")
        # 领地
        x, y = wk.get_str_pos(*RECT_TODAY_TIANJIANG, "领地", COLOR_WHITE)
        if x > 0 and wk.find_str(x-7, y+53, x+45, y+100, "可领取", COLOR_GREEN):
            count += 1
            wk.move_click(x+17, y+50)
            msleep(600)
            for pos in [(254, 332), (433, 249), (621, 370)]:
                wk.move_click(*pos)
                wk.find_pic_click(*RECT_LING_DI2, "领地收获.bmp|领地培养.bmp")
                msleep(400)
                if cls.click_confirm(wk, RECT=RECT_POPUP):  # 背包满了
                    wk.record("领地收获报错背包必须保留两格")
                    if cls.TASK_NAME == "单人刷野" and wk.cfg_plan_task["清理背包"]:
                        wk.need_clear_bag = True
                wk.move_click(*POS_LINGDI_CLOSE)
            cls.click_close_pic(wk)
            wk.record("领地培养收获已完成")
        if count == 0:
            wk.record("祥瑞 没有可领取的奖励")
        cls.close_pages(wk)
        wk.is_stuck = True
        if cls.TASK_NAME == "单人刷野" and wk.need_clear_bag:
            cls.clear_bag_and_back_map(wk)

    @classmethod
    def get_fest(cls, wk: Worker):
        cls.stop_auto_find_way(wk, force=True)
        wk.move_click(*POS_FEST)
        msleep(600)
        wk.record("领取节日奖励...")
        cls.click_confirm(wk)

    @classmethod
    def clear_bag_and_back_map(cls, wk: Worker):
        pass

    @classmethod
    def go_to_wu_ling_meng(cls, wk: Worker):
        for i in range(3):
            wk.record("正在前往武林盟...")
            if cls.cur_map(wk) == "武林盟":
                wk.record("到达武林盟")
                return True
            cls.back_to_kai_feng(wk)
            cls.cross_map(wk, "武林盟")
        return cls.cur_map(wk) == "武林盟"

    @classmethod
    def open_kezhan_page(cls, wk: Worker):
        if cls.is_depot_open(wk):
            return True
        if cls.talk_with_cur_map_npc(wk, "银湘玉", ["查看包裹和仓库"]):
            if cls.is_depot_open(wk, timeout=400):
                return True
        if cls.click_cancel(wk, RECT=RECT_FULL, timeout=400):
            wk.record("需要输入二级密码, 已点击取消")
        return False

    @classmethod
    def open_school_depot_page(cls, wk: Worker):
        return cls.find_way_npc(wk, "闭关", ["包裹与仓库"], cls.is_depot_open)

    @classmethod
    def is_shop_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "单价.bmp", timeout=timeout)

    @classmethod
    def is_ji_shou_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "界_寄售搜索.bmp", timeout=timeout)

    @classmethod
    def open_shop_page(cls, wk: Worker):
        cls.back_to_kai_feng(wk)
        return cls.talk_with_cur_map_npc(wk, "胡大夫", ["买卖"], cls.is_shop_open)

    @classmethod
    def open_zahuo_page(cls, wk: Worker):
        cls.back_to_kai_feng(wk)
        return cls.talk_with_cur_map_npc(wk, "桑蝶儿", ["买卖"], cls.is_shop_open)

    @classmethod
    def open_wakuang_page(cls, wk: Worker):
        return cls.find_way_npc(wk, "金大坚", ["购买"], cls.is_shop_open)

    @classmethod
    def open_huang_ys_page(cls, wk: Worker):
        cls.run_to_map(wk, "桃花岛")
        return cls.find_way_npc(wk, "黄药师", ["买卖"], cls.is_shop_open)

    @classmethod
    def open_ji_shou_page(cls, wk: Worker):
        cls.back_to_kai_feng(wk)
        return cls.talk_with_cur_map_npc(wk, "开封商人", ["寄售物品"],
                                         until_func=cls.is_ji_shou_open)

    @classmethod
    def buy_thing(cls, wk: Worker, thing_name: str, thing_num: int):
        if thing_num <= 0:
            return
        if thing_name in ["熊猫香", "百草蜜酿", "玄冰针", "4000红药", "4000蓝药", "花露水"]:
            if cls.open_shop_page(wk) and cls.select_thing(wk, thing_name+".bmp"):
                cls.shop_input_thing_number_and_buy(wk, thing_num)
        elif thing_name in ["贺岁礼花", "狂乱散"]:
            if cls.open_zahuo_page(wk) and cls.select_thing(wk, thing_name+".bmp"):
                cls.shop_input_thing_number_and_buy(wk, thing_num)
        elif thing_name == "矿镐":
            if cls.open_wakuang_page(wk) and cls.select_thing(wk, thing_name+".bmp"):
                cls.shop_input_thing_number_and_buy(wk, thing_num)
        elif thing_name == "桃子":
            if cls.open_huang_ys_page(wk) and cls.select_thing(wk, thing_name+".bmp"):
                cls.shop_input_thing_number_and_buy(wk, thing_num)

    @classmethod
    def select_thing(cls, wk: Worker, item_bmp: str):
        x, y = wk.get_pic_pos(*RECT_FULL, "单价.bmp", timeout=600)
        if x < 0:
            wk.record("商店打开失败，选择物品失败")
            return False
        for i in range(3):
            msleep(300)
            if wk.find_pic_click(x-154, y-225, x+129, y-11, item_bmp, timeout=300):
                wk.record("选择物品成功")
                return True
            wk.move_click(x+116, y-26, 10, re_move=False)  # 往下拉
        return False

    @classmethod
    def shop_input_thing_number_and_buy(cls, wk: Worker, num: int):
        x, y = wk.get_pic_pos(*RECT_FULL, "单价.bmp")
        if x < 0:
            wk.record("商店打开失败，输入物品数量失败")
            return False
        num_arr = cls.split_num_to_arr(num)
        for num in num_arr:
            cls.input_number(wk, num)
            # 点击购买和确定
            wk.move_click(x+23, y+121)
            msleep(300)
            cls.talk_click_specify_item(wk, "是的")
            msleep(600)
            if cls.is_talk_show_info(wk, "物品栏太满了"):
                wk.record("物品栏太满了")
                cls.close_pages(wk)
                wk.need_clear_bag = True
                return False
        cls.close_pages(wk)
        return True

    @classmethod
    def shop_buy(cls, wk: Worker):
        x, y = wk.get_pic_pos(*RECT_FULL, "单价.bmp")
        if x < 0:
            wk.record("商店打开失败")
            return False
        # 点击购买和确定
        wk.move_click(x+23, y+121)
        msleep(300)
        cls.talk_click_specify_item(wk, "是的")
        msleep(600)
        if cls.is_talk_show_info(wk, "物品栏太满了"):
            wk.record("物品栏太满了")
            cls.close_pages(wk)
            wk.need_clear_bag = True
            return False
        cls.close_pages(wk)
        return True

    @classmethod
    def split_num_to_arr(cls, num: int):
        # 把num分为数组，因为最大一次只能买99个
        arr = []
        while num > 0:
            if num >= 99:
                num -= 99
                arr.append(99)
            else:
                arr.append(num)
                num = 0
        return arr

    @classmethod
    def input_number(cls, wk: Worker, num: int):
        res = wk.find_pic_offset_click(
            *RECT_FULL, "数量减.bmp", dx=-20, timeout=300)
        if not res:
            wk.record("输入数量失败")
            return False
        wk.key_press(VK_RIGHT, 2)
        wk.key_press(VK_BACK, 2)  # 删除之前的数字
        msleep(300)
        wk.send_string(str(num))
        msleep(300)
        wk.record(f"输入数量 {num}")
        return True

    @classmethod
    def ji_shou_buy_thing(cls, wk: Worker, thing_name: str, thing_num: int):
        if not cls.open_ji_shou_page(wk):
            wk.record("寄售购买物品失败")
            return
        msleep(0, 2000)  # 让不同的号不要同一时间全抢一样物品
        wk.move_click(*POS_JI_SHOU_INPUT)
        wk.send_string(thing_name)
        msleep(400)
        if not wk.find_pic_click(*RECT_FULL, "界_寄售搜索.bmp", timeout=400):
            wk.record("寄售点击搜索失败")
            cls.close_pages(wk)
            return
        msleep(800)
        min_price, min_price_pos = cls.get_ji_shou_min_price(wk, thing_name)
        if min_price_pos == (-1, -1):
            wk.record("寄售页未找到 最便宜商品")
            return
        wk.record(f"寄售页找到最便宜商品 {thing_name} {min_price}")
        # 读取物价表, 看看价格是否超过了限价
        limit_price = cls.get_goods_price(wk, thing_name)
        if limit_price != 0 and min_price > limit_price:
            wk.record(
                f"寄售页最便宜商品 {thing_name} 价格{min_price} 超过了限价{limit_price}")
            cls.close_pages(wk)
            return
        min_price_x, min_price_y = min_price_pos
        print(f"min_price_x: {min_price_x}, min_price_y: {min_price_y}")
        wk.move_click(min_price_x+396, min_price_y+5)  # 点击购买
        msleep(600)
        if cls.is_popup_show_info(wk, "背包空格不足"):
            wk.record("背包空格不足")
            wk.need_clear_bag = True
            cls.close_pages(wk)
            return
        if not cls.input_number(wk, thing_num):
            cls.close_pages(wk)
            return
        if cls.click_confirm(wk, timeout=600):
            msleep(600)
            cls.click_confirm(wk)  # 购买结算确认
            msleep(600)
            cls.click_confirm(wk, timeout=600)  # 购买成功的确认
            wk.record(f"寄售购买 {thing_name} 成功, 单价:{min_price}, 数量: {thing_num}")
        else:
            wk.record(f"寄售购买 {thing_name} 失败, 没成功点击确认")
        cls.close_pages(wk)

    @classmethod
    def get_ji_shou_min_price(cls, wk: Worker, thing_name: str):
        NOT_FOUND_POS = (-1, -1)
        MAX_PRICE = 9999999999999
        min_price_pos = NOT_FOUND_POS
        min_price = MAX_PRICE
        name_color = "|".join([COLOR_WHITE, COLOR_QUALITY_BLUE,
                               COLOR_QUALITY_GREEN, COLOR_GOLD, COLOR_QUALITY_ORANGE])
        price_color = "|".join([COLOR_WHITE, COLOR_PRICE_GREEN])
        page_count = 3
        # 往下翻, 找出最低价商品的价格
        wk.record(f"正在寄售中向下翻页找最便宜商品...")
        if len(thing_name) > 6:
            thing_name = thing_name[:6]
        for i in range(page_count):
            pos_list = wk.find_str_ex(
                *RECT_JI_SHOU_NAME, thing_name, name_color, timeout=800)
            if i == 0 and not pos_list:
                wk.record(f"寄售中没有 {thing_name}")
                cls.close_pages(wk)
                return min_price, min_price_pos
            for _, x, y in pos_list:  # 逐个识别物品的价格
                price_str = wk.ocr(x+190, y-3, x+295, y+18,
                                   price_color, zk=ZK_DIGIT_11)
                price = int(price_str or MAX_PRICE)
                # print(f"price: {price}, min_price: {min_price}, min_price_pos: {min_price_pos}")
                if price < min_price:
                    min_price = price
            wk.record(f"当前找到最便宜的 {thing_name} 价格是 {min_price}")
            if len(pos_list) < 8:  # 说明没有下一页了
                wk.record("已经到最后一页了")
                break
            wk.move_click(*POS_JI_SHOU_NEXT)
            msleep(600)
        # 往上翻, 找到那个最低价
        wk.record(f"正在寄售中向上翻页点击最便宜商品...")
        for i in range(page_count+1):
            pos_list = wk.find_str_ex(
                *RECT_JI_SHOU_NAME, thing_name, name_color, timeout=800)
            for _, x, y in pos_list:  # 逐个识别物品的价格
                price_str = wk.ocr(x+190, y-3, x+295, y+18,
                                   price_color, zk=ZK_DIGIT_11)
                price = int(price_str or MAX_PRICE)
                # print(f"price: {price}, min_price: {min_price}, min_price_pos: {min_price_pos}")
                if price <= min_price:
                    min_price = price
                    min_price_pos = (x, y)
                    wk.record(f"找到最便宜的 {thing_name} 价格是 {min_price}, 搜索完成")
                    return min_price, min_price_pos
            wk.move_click(*POS_JI_SHOU_PREV)
            msleep(600)
        wk.record(f"寄售中回翻时没找到最便宜的 {thing_name} 价格是 {min_price}")
        return MAX_PRICE, NOT_FOUND_POS

    # -------------------------- 重叠验证 ------------------------------

    @classmethod
    def is_overlap_auth_appear(cls, wk: Worker):
        return wk.find_pic(*RECT_CHECK_AUTH, "未重叠.bmp|未重叠2.bmp")

    @classmethod
    @MutexLocker
    def pass_overlap_auth(cls, wk: Worker):
        """
        策略是：
        先找下看有没有多图，如果有，就先收集一下，然后提高相似度，降低偏色，再找，直到只能找到一个图
        """
        if not cls.is_overlap_auth_appear(wk):
            return
        wk.record("验证码出现")
        wk.mate_check_leave = True  # 每一次出验证要检查是否暂离
        cls.close_other_talk(wk)
        cls.click_close_pic(wk)
        if not cls.AUTH_PIC:
            cls.AUTH_PIC = wk.match_pic("验_*.bmp")
        delta_color = cls.ready_overlap_auth(wk)
        last_x, last_y = -1, -1
        exclude_pos = set()
        # 开始过验证
        for i in range(20):
            if cls.click_answer(wk, i):
                return
            wk.re_move()
            msleep(100)
            pic_pos_list = wk.find_pic_ex(
                *RECT_AUTH, cls.AUTH_PIC, delta_color=delta_color
            )
            if len(pic_pos_list) != 1:
                pic_list_length = len(pic_pos_list)
                delta_color = cls.adjust_color(
                    wk, pic_list_length, delta_color)
                continue
            x, y = wk.get_pic_pos(*RECT_AUTH, cls.AUTH_PIC,
                                  delta_color=delta_color)
            if x < 0:
                continue
            if (last_x, last_y) == (x, y) or (x, y) in exclude_pos:
                exclude_pos.add((x, y))
                continue
            wk.move_click(x + 30, y + 10, re_move=False, is_sleep=False)
            last_x, last_y = x, y

    @classmethod
    def collect_overlap_pic(cls, wk: Worker):
        wk.capture(*RECT_FULL, f"重叠验证_{settings.cur_time_stamp}_{wk.row}.bmp")
        wk.record("重叠验证已收集")

    @classmethod
    def ready_overlap_auth(cls, wk: Worker):
        delta_color = "202020"
        if cls.switch_to_taohuadao(wk):
            delta_color = "101010"
        return delta_color

    @classmethod
    def adjust_color(cls, wk: Worker, pic_list_length: int, delta_color: str):
        # 默认情况下不用调整偏色
        return delta_color

    @classmethod
    def click_answer(cls, wk: Worker, i: int):
        if cls.is_time_to_click_answer(wk, i):
            if not wk.find_pic_click(
                    *RECT_FULL,
                    "选择按钮.bmp|选择按钮2.bmp|选择按钮3.bmp",
                    re_move=False,
            ):
                return False
            wk.record("验证已点击")
        msleep(800)
        wk.re_move()
        if not wk.find_pic(*RECT_FULL, "未重叠.bmp|未重叠2.bmp"):
            if not cls.check_confirm_popup(wk):
                wk.record("验证未通过!")
                return False
            wk.record("验证已通过")
            msleep(200)
            cls.close_pages(wk)
            wk.is_stuck = True
            return True
        wk.record("验证未通过")
        return False

    @classmethod
    def is_time_to_click_answer(cls, wk: Worker, i: int):
        return True

    @classmethod
    def check_confirm_popup(cls, wk: Worker):
        return True

    @classmethod
    def minimize_talk_region(cls, wk: Worker, try_again=True):
        new_y = 572
        x, y = wk.get_pic_pos(*RECT_LEFT, "聊天框.bmp|聊天框2.bmp", timeout=200)
        if  y == new_y or y < 0:
            return
        wk.find_pic_drag_to(*RECT_LEFT, "聊天框.bmp|聊天框2.bmp", new_x=x, new_y=new_y)
        msleep(200)
        x, y = wk.get_pic_pos(*RECT_LEFT, "聊天框.bmp|聊天框2.bmp", timeout=200)
        if try_again:
            cls.minimize_talk_region(wk, try_again=False)

    @staticmethod
    def add_colors(color1, color2="010101"):
        int1 = int(color1, 16)
        int2 = int(color2, 16)
        result_int = int1 + int2
        result_color = format(result_int, "06x")
        return result_color

    @staticmethod
    def sub_colors(color1, color2="010101"):
        int1 = int(color1, 16)
        int2 = int(color2, 16)
        result_int = int1 - int2
        result_color = format(result_int, "06x")
        return result_color

    @classmethod
    @MutexLocker
    def input_account_password_login(cls, wk: Worker):
        wk.find_pic_click(*RECT_SEL_SERVER_POPUP, "登录确定.bmp|登录确定2.bmp")
        if not wk.find_pic(*RECT_CHECK_LOGIN, "登录页面.bmp"):
            return
        wk.mate_check_leave = True  # 每一次重新登录要检查是否暂离
        wk.record("正在登录...")
        wk.find_pic_click(*RECT_FULL, "登录弹窗关闭.bmp",
                          re_move=False, timeout=400)
        game_account = settings.wnd_main.tbe_console.item(
            wk.row, COL_ACCOUNT).text()
        if not game_account:
            wk.record("没有找到账号信息")
            return
        account = game_account
        password = settings.wnd_main.tbe_console.item(
            wk.row, COL_PASSWORD).text()
        if account:
            for i in range(6):
                if not wk.find_color(*RECT_ACCOUNT_INPUT, COLOR_ACCOUNT, timeout=400):
                    wk.record("账号栏清空完毕")
                    break
                if i == 0:
                    wk.record("正在清空账号栏...")
                wk.move_click(*POS_LOGIN_ACCOUNT, click_count=1,
                              re_move=False, is_sleep=False)
                msleep(100)
                wk.key_press(VK_BACK, 5, delay=10)
            wk.record("正在输入账号...")
            wk.move_click(*POS_LOGIN_ACCOUNT)  # 确保在账号栏
            wk.send_string(account)
            wk.record(f"账号输入完毕")
            msleep(500)
            wk.re_move()
            wk.left_click()
            msleep(500)
        if not wk.find_pic(*RECT_CHECK_LOGIN, "登录页面.bmp"):
            wk.key_press(VK_BACK, 20)
            return
        if password:
            for i in range(6):
                if not wk.find_color(*RECT_PASSWORD_INPUT, COLOR_ACCOUNT, timeout=400):
                    wk.record("密码栏清空完毕")
                    break
                if i == 0:
                    wk.record("正在清空密码栏...")
                wk.move_click(*POS_LOGIN_PASSWORD,
                              click_count=1, re_move=False)
                msleep(100)
                wk.key_press(VK_BACK, 5, delay=10)
            wk.record("正在输入密码...")
            wk.move_click(*POS_LOGIN_PASSWORD)
            wk.send_string(password)
            wk.record(f"密码输入完毕")
            msleep(500)
        if not wk.find_pic(*RECT_CHECK_LOGIN, "登录页面.bmp"):
            wk.key_press(VK_BACK, 20)
            return
        # 登录
        if wk.find_pic_click(*RECT_FULL, "进入.bmp|进入2.bmp"):
            wk.record("确定登录")
            msleep(1000)
        wk.key_press(VK_BACK, 20)
        msleep(500)
        for _ in range(40):
            msleep(400)
            if wk.find_multi_color(*RECT_IN_GAME, MCOLOR_IN_GAME):
                wk.record("登录成功!")
                msleep(600)
                return
            if wk.find_pic_click(
                    *RECT_FULL, "游戏.bmp", timeout=500, re_move=False
            ):  # # 对于合区后有两个角色的，选择默认角色进入游戏
                wk.record("发现全区角色, 选择默认角色")
                msleep(1500)
            cls.pick_server(wk)
            wk.record("正在登陆服务器...")
        wk.record("登录失败!")

    @classmethod
    def pick_server(cls, wk: Worker):
        # 选线路
        if not wk.find_pic(*RECT_CHECK_SEL_SERVER, "选线界面.bmp"):
            return
        server = settings.wnd_main.tbe_console.cellWidget(wk.row, COL_SERVER).currentText()
        if server == "默认":
            wk.record("选择默认线路")
            msleep(400)
            if wk.find_pic_click(*RECT_FULL, "登录确定.bmp|登录确定2.bmp", timeout=600, re_move=False):
                msleep(1000)
            wk.find_pic_click(*RECT_FULL, "登录确定.bmp|登录确定2.bmp")  # 再点一次确保点到
            return
        wk.record(f"正在选择服务器 {server}...")
        server = server.rstrip("线")
        color = "|".join([COLOR_SERVER_FREE, COLOR_SERVER_BUSY])
        for i in range(20):
            if wk.find_str_db_click(
                    *RECT_FULL, server, color, timeout=600
            ):
                break
            msleep(400)
            if i >= 5:  # 没找到的话尝试用偏移来点
                x, y = wk.get_pic_pos(*RECT_FULL, "登录确定.bmp|登录确定2.bmp")
                if x > 0:
                    cls.sel_server_by_offset(wk, server, x, y)
                else:
                    break

    @classmethod
    def sel_server_by_offset(cls, wk: Worker, server: str, x: int, y: int):
        if server.startswith("一"):
            wk.move_db_click(x+56, y-296)
        elif server.startswith("二"):
            wk.move_db_click(x+56, y-264)
        elif server.startswith("三"):
            wk.move_db_click(x+56, y-234)
        elif server.startswith("四"):
            wk.move_db_click(x+56, y-204)
        elif server.startswith("五"):
            wk.move_db_click(x+56, y-174)
        else:
            wk.move_db_click(x+56, y-144)
