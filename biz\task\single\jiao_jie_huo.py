from biz.task.__base import TaskBase
import settings
from biz.obj.worker import Worker
from biz.constants.constants import *
from utils.utils import msleep, rnd

POS_DICT = {
    "开封一": (401, 226),
    "开封二": (615, 239),
    "开封三": (595, 298),
    "开封四": (825, 415),
    "摆摊区": (524, 412),
    "震关西": (573, 348),
}

class TaskJiaoHuo(TaskBase):
    TASK_NAME = "交货"

    @classmethod
    def run(cls, wk: Worker):
        cls.go_to_trade_place(wk)
        if wk.cfg_plan_task["游戏币开关"]:
            wk.record("请注意:勾选了游戏币会导致其它物品交接货不执行")
            cls.jiao_money(wk)
        else:
            cls.jiao_huo_proc(wk)
            cls.jiao_huo_proc(wk)
            
    @classmethod
    def go_to_trade_place(cls, wk: Worker):
        deliver_name = wk.cfg_plan_task["交接位置"]
        if deliver_name == "原地":
            wk.record("交接位置：原地")
            cls.stop_auto_find_way(wk, force=True)
        elif deliver_name == "帮会领地":
            wk.record("交接位置：帮会领地")
            if not cls.is_in_bang_hui(wk):
                cls.go_to_bang_hui(wk)
        else:
            x, y = POS_DICT[deliver_name]
            wk.record(f"交接位置:{deliver_name}, 在 {x},{y} 处接货")
            cls.go_to_deliver_pos(wk, x, y)

    @classmethod
    def jiao_huo_proc(cls, wk: Worker):
        wk.record("正在清点需要交货的物品...")
        cls.click_confirm(wk)
        need_bang_hui, need_yao_cai, need_zhu_fu, need_za_huo, need_ji_neng, \
            need_ling_shi, need_fu_ben, need_cang_bang_tu, need_tong_bao_dai, \
            need_tao_zi, need_xun_ma, need_expansive, need_equip = cls.get_need_deliver_things(
                wk)
        wk.record(f"需要交货情况: 帮会物品{need_bang_hui}, 药材{need_yao_cai}, 主辅材{need_zhu_fu}, 杂货{need_za_huo}, 技能书{need_ji_neng}, 灵石{need_ling_shi}, 副本产出{need_fu_ben}, 藏宝图{need_cang_bang_tu}, 通宝袋{need_tong_bao_dai}, 桃子{need_tao_zi}, 驯马材料{need_xun_ma}, 贵重物品{need_expansive}, 回炉装备{need_equip}")
        cls.close_pages(wk)
        # 跑帮物品
        if need_bang_hui:
            pic_bang_hui = PIC_BANG_HUI
            cls.trade_jiao_huo(
                wk, pic_bang_hui, wk.cfg_plan_task["跑帮物品"], "跑帮物品")
        # 药材
        if need_yao_cai:
            pic_yao_cai = wk.match_pic("药材_*.bmp")
            cls.trade_jiao_huo(wk, pic_yao_cai, wk.cfg_plan_task["药材"], "药材")
        # 主辅材
        if need_zhu_fu:
            pic_zhu_fu = wk.match_pic("主材_*.bmp") + \
                "|" + wk.match_pic("辅材_*.bmp")
            cls.trade_jiao_huo(wk, pic_zhu_fu, wk.cfg_plan_task["主辅材"], "主辅材")
        # 杂货
        if need_za_huo:
            pic_za_huo = wk.match_pic(
                "杂货_*.bmp") + "|存_倚.bmp|存_天.bmp|存_剑.bmp|存_与.bmp|存_屠.bmp|存_龙.bmp|存_刀.bmp"
            cls.trade_jiao_huo(wk, pic_za_huo, wk.cfg_plan_task["杂货"], "杂货")
        # 技能书
        if need_ji_neng:
            pic_ji_neng = wk.match_pic("随从*书.bmp")
            cls.trade_jiao_huo(wk, pic_ji_neng, wk.cfg_plan_task["技能书"], "技能书")
        # 灵石
        if need_ling_shi:
            pic_ling_shi = wk.match_pic("*级*灵石.bmp")
            cls.trade_jiao_huo(wk, pic_ling_shi, wk.cfg_plan_task["灵石"], "灵石")
        # 副本产出
        if need_fu_ben:
            pic_fu_ben = wk.match_pic(
                "存_**套装碎片.bmp") + "|随从卡片.bmp|随从卡片2.bmp|随从卡片3.bmp"
            cls.trade_jiao_huo(
                wk, pic_fu_ben, wk.cfg_plan_task["副本产出"], "副本产出")
        # 藏宝图
        if need_cang_bang_tu:
            pic_cang_bao_tu = "九州重宝图.bmp"
            cls.trade_jiao_huo(wk, pic_cang_bao_tu,
                               wk.cfg_plan_task["藏宝图"], "藏宝图")
        # 通宝袋
        if need_tong_bao_dai:
            pic_tong_bao_dai = wk.match_pic("*通宝袋.bmp")
            cls.trade_jiao_huo(wk, pic_tong_bao_dai,
                               wk.cfg_plan_task["通宝袋"], "通宝袋")
        # 桃子
        if need_tao_zi:
            pic_tao_zi = "桃子.bmp"
            cls.trade_jiao_huo(wk, pic_tao_zi, wk.cfg_plan_task["桃子"], "桃子")
        # 驯马材料
        if need_xun_ma:
            pic_xun_ma = "亚麻线.bmp|捆绳线.bmp|存_驯马绳.bmp"
            cls.trade_jiao_huo(
                wk, pic_xun_ma, wk.cfg_plan_task["驯马材料"], "驯马材料")
        # 贵重物品
        if need_expansive:
            cls.trade_jiao_huo(wk, PIC_EXPANSIVE,
                               wk.cfg_plan_task["贵重物品"], "贵重物品")
        # 回炉装备
        if need_equip:
            cls.trade_jiao_huo(wk, PIC_JINTI + "|" + PIC_EQUIP,
                               wk.cfg_plan_task["回炉装备"], "回炉装备", filter_fn=filter_fn)
            
        wk.record("交易完成")

    @classmethod
    def jiao_money(cls, wk: Worker):
        cur_amount_str = cls.get_money_amount(wk)
        MIN_MONEY = 1000000
        if not cur_amount_str or int(cur_amount_str) <= MIN_MONEY:
            wk.record(f"游戏币数量:{cur_amount_str} <= 100W, 跳过")
            return
        trade_amount = int(cur_amount_str) - MIN_MONEY
        amount_str = f"{trade_amount}"
        money_player_name = wk.cfg_plan_task["游戏币"]
        if not money_player_name:
            wk.record("游戏币收货人未设置, 跳过")
            return
        cls.trade_with_player(wk, money_player_name, amount_str)


    @classmethod
    def get_money_amount(cls, wk: Worker):
        cls.open_bag_page(wk)
        wk.find_pic_drag_to(*RECT_FULL, "界_背包.bmp", 404, 547)
        msleep(200)
        color_money = "|".join(
            [COLOR_WHITE, COLOR_PRICE_GREEN, COLOR_GOLD, COLOR_HUNDRED, COLOR_THOUSAND])
        amount_str = wk.ocr(457, 192, 535, 229, color_money, zk=ZK_DIGIT_11, timeout=400)
        wk.record(f"游戏币数量: {amount_str}")
        cls.close_pages(wk)
        return amount_str

    @classmethod
    def go_to_deliver_pos(cls, wk: Worker, x: int, y: int):
        cls.back_to_kai_feng(wk)
        wk.record(f"正在前往交接地点...")
        if not cls.big_map_click(wk, x, y):
            wk.record("寻路失败, 重试中...")
            msleep(600)
            cls.big_map_click(wk, x, y)
        for i in range(100):
            msleep(800)
            if wk.is_stuck:
                if cls.is_talk_open(wk):
                    cls.close_other_talk(wk)
                    cls.big_map_click(wk, x, y)
                    wk.is_stuck = False
                else:
                    wk.record("到达交接地点")
                    break

    @classmethod
    def trade_jiao_huo(cls, wk: Worker, pic: str, shou_huo_name: str, thing_type: str, filter_fn=None):
        if not shou_huo_name:
            return
        if shou_huo_name == wk.player_name:
            wk.record("收货人就是自己 跳过")
            return
        wk.record(f"正在交易{thing_type} 给 {shou_huo_name}...")
        cls.trade_with_player(wk, shou_huo_name, pic, category=thing_type, filter_fn=filter_fn)

    @classmethod
    def get_need_deliver_things(cls, wk: Worker):
        need_bang_hui = False
        need_yao_cai = False
        need_zhu_fu = False
        need_za_huo = False
        need_ji_neng = False
        need_ling_shi = False
        need_fu_ben = False
        need_cang_bang_tu = False
        need_tong_bao_dai = False
        need_tao_zi = False
        need_xun_ma = False
        need_expansive = False
        need_equip = False
        cls.open_bag_page(wk)
        x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
        if x > 0:
            begin_x, begin_y = x + 18, y - 300
            RECT_BAG = (begin_x, begin_y, begin_x + 260, begin_y + 300)
            need_bang_hui, need_yao_cai, need_zhu_fu, need_za_huo, need_ji_neng, \
                need_ling_shi, need_fu_ben, need_cang_bang_tu, need_tong_bao_dai, \
                need_tao_zi, need_xun_ma, need_expansive, need_equip = \
                cls.cur_page_find_pic_is_need_deliver(wk, RECT_BAG,
                                                      need_bang_hui, need_yao_cai, need_zhu_fu,
                                                      need_za_huo, need_ji_neng, need_ling_shi,
                                                      need_fu_ben, need_cang_bang_tu,
                                                      need_tong_bao_dai, need_tao_zi, need_xun_ma, need_expansive, need_equip)

            pos_list = cls.get_bag_page_pos_list(wk, RECT_BAG)
            for pos in pos_list:  # 遍历背包
                _, x, y = pos
                wk.move_click(x, y)
                msleep(600)
                need_bang_hui, need_yao_cai, need_zhu_fu, need_za_huo, need_ji_neng, \
                    need_ling_shi, need_fu_ben, need_cang_bang_tu, need_tong_bao_dai, \
                    need_tao_zi, need_xun_ma, need_expansive, need_equip = \
                    cls.cur_page_find_pic_is_need_deliver(wk, RECT_BAG,
                                                          need_bang_hui, need_yao_cai, need_zhu_fu,
                                                          need_za_huo, need_ji_neng, need_ling_shi,
                                                          need_fu_ben, need_cang_bang_tu,
                                                          need_tong_bao_dai, need_tao_zi, need_xun_ma, need_expansive, need_equip)
        return need_bang_hui, need_yao_cai, need_zhu_fu, need_za_huo, need_ji_neng, need_ling_shi, need_fu_ben, need_cang_bang_tu, need_tong_bao_dai, need_tao_zi, need_xun_ma, need_expansive, need_equip

    @classmethod
    def cur_page_find_pic_is_need_deliver(cls, wk: Worker, RECT_BAG: tuple, need_bang_hui, need_yao_cai, need_zhu_fu, need_za_huo, need_ji_neng, need_ling_shi, need_fu_ben, need_cang_bang_tu, need_tong_bao_dai, need_tao_zi, need_xun_ma, need_expansive, need_equip):
        if wk.cfg_plan_task["跑帮物品开关"] and not need_bang_hui:
            pic_bang_hui = PIC_BANG_HUI
            need_bang_hui = cls.check_cur_page_have_thing(
                wk, pic_bang_hui, RECT_BAG)
        if wk.cfg_plan_task["药材开关"] and not need_yao_cai:
            pic_yao_cai = wk.match_pic("药材_*.bmp")
            need_yao_cai = cls.check_cur_page_have_thing(
                wk, pic_yao_cai, RECT_BAG)
        if wk.cfg_plan_task["主辅材开关"] and not need_zhu_fu:
            pic_zhu_fu = wk.match_pic("主材_*.bmp") + \
                "|" + wk.match_pic("辅材_*.bmp")
            need_zhu_fu = cls.check_cur_page_have_thing(
                wk, pic_zhu_fu, RECT_BAG)
        if wk.cfg_plan_task["杂货开关"] and not need_za_huo:
            pic_za_huo = wk.match_pic(
                "杂货_*.bmp") + "|存_倚.bmp|存_天.bmp|存_剑.bmp|存_与.bmp|存_屠.bmp|存_龙.bmp|存_刀.bmp"
            need_za_huo = cls.check_cur_page_have_thing(
                wk, pic_za_huo, RECT_BAG)
        if wk.cfg_plan_task["技能书开关"] and not need_ji_neng:
            pic_ji_neng = wk.match_pic("随从*书.bmp")
            need_ji_neng = cls.check_cur_page_have_thing(
                wk, pic_ji_neng, RECT_BAG)
        if wk.cfg_plan_task["灵石开关"] and not need_ling_shi:
            pic_ling_shi = wk.match_pic("*级*灵石.bmp")
            need_ling_shi = cls.check_cur_page_have_thing(
                wk, pic_ling_shi, RECT_BAG)
        if wk.cfg_plan_task["副本产出开关"] and not need_fu_ben:
            pic_fu_ben = wk.match_pic(
                "存_**套装碎片.bmp") + "|随从卡片.bmp|随从卡片2.bmp|随从卡片3.bmp"
            need_fu_ben = cls.check_cur_page_have_thing(
                wk, pic_fu_ben, RECT_BAG)
        if wk.cfg_plan_task["藏宝图开关"] and not need_cang_bang_tu:
            pic_cang_bao_tu = "九州重宝图.bmp"
            if wk.find_pic(*RECT_BAG, pic_cang_bao_tu):
                wk.record("发现藏宝图")
                need_cang_bang_tu = True
        if wk.cfg_plan_task["通宝袋开关"] and not need_tong_bao_dai:
            pic_tong_bao_dai = wk.match_pic("*通宝袋.bmp")
            if wk.find_pic(*RECT_BAG, pic_tong_bao_dai):
                need_tong_bao_dai = True
        if wk.cfg_plan_task["桃子开关"] and not need_tao_zi:
            pic_tao_zi = "桃子.bmp"
            need_tao_zi = cls.check_cur_page_have_thing(
                wk, pic_tao_zi, RECT_BAG)
        if wk.cfg_plan_task["驯马材料开关"] and not need_xun_ma:
            pic_xun_ma = "亚麻线.bmp|捆绳线.bmp|存_驯马绳.bmp"
            need_xun_ma = cls.check_cur_page_have_thing(
                wk, pic_xun_ma, RECT_BAG)
        if wk.cfg_plan_task["贵重物品开关"] and not need_expansive:
            need_expansive = cls.check_cur_page_have_thing(
                wk, PIC_EXPANSIVE, RECT_BAG)
        if wk.cfg_plan_task["回炉装备开关"] and not need_equip:
            need_equip = cls.check_cur_page_have_thing(
                wk, PIC_JINTI + "|" + PIC_EQUIP, RECT_BAG, filter_fn=filter_fn)
        return need_bang_hui, need_yao_cai, need_zhu_fu, need_za_huo, need_ji_neng, need_ling_shi, need_fu_ben, need_cang_bang_tu, need_tong_bao_dai, need_tao_zi, need_xun_ma, need_expansive, need_equip

    @classmethod
    def right_click_cur_page_things(cls, wk: Worker, pic: str, RECT=RECT_SELL_THING, MAX_COUNT=None, filter_fn=None):
        # 没找到没交 或者 交了没交满, 返回False, 交满了返回True
        if not wk.cfg_plan_task["卡格子交货"]:  # 不卡格子的话直接全交
            return super().right_click_cur_page_things(wk, pic, MAX_COUNT=MAX_COUNT, filter_fn=filter_fn)
        return super().right_click_cur_page_things_left_one(wk, pic, MAX_COUNT=MAX_COUNT)

    @classmethod
    def check_cur_page_have_thing(cls, wk: Worker, pic: str, RECT_BAG: tuple, filter_fn=None, timeout=0):
        if filter_fn:
            return filter_fn(wk, pic, RECT_BAG, click=False)
        if wk.cfg_plan_task["卡格子交货"]:
            arr = wk.find_pic_ex(*RECT_BAG, pic, timeout=timeout)
            for _, x, y in arr:
                count = int(wk.ocr(x, y, x+45, y+45,
                            COLOR_WHITE, zk=ZK_DIGIT_9) or 1)
                if count > 1:
                    return True
        else:
            if wk.find_pic(*RECT_BAG, pic, timeout=timeout):
                return True
        return False

    @classmethod
    def get_default_biz_config(cls):
        return {
            "交接位置": "原地",
            "跑帮物品": "",
            "药材": "",
            "主辅材": "",
            "杂货": "",
            "技能书": "",
            "灵石": "",
            "副本产出": "",
            "藏宝图": "",
            "通宝袋": "",
            "桃子": "",
            "驯马材料": "",
            "贵重物品": "",
            "回炉装备": "",
            "游戏币": "",
            "跑帮物品开关": False,
            "药材开关": False,
            "主辅材开关": False,
            "杂货开关": False,
            "技能书开关": False,
            "灵石开关": False,
            "副本产出开关": False,
            "藏宝图开关": False,
            "通宝袋开关": False,
            "桃子开关": False,
            "驯马材料开关": False,
            "贵重物品开关": False,
            "回炉装备开关": False,
            "游戏币开关": False,
            "卡格子交货": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_deliver_pos.setCurrentText(cls.CONFIG["交接位置"])
        settings.wnd_main.edt_deliver_banghui.setText(cls.CONFIG["跑帮物品"])
        settings.wnd_main.edt_deliver_yao_cai.setText(str(cls.CONFIG["药材"]))
        settings.wnd_main.edt_deliver_zhu_fu_cai.setText(cls.CONFIG["主辅材"])
        settings.wnd_main.edt_deliver_za_huo.setText(cls.CONFIG["杂货"])
        settings.wnd_main.edt_deliver_bb_skill_book.setText(
            str(cls.CONFIG["技能书"]))
        settings.wnd_main.edt_deliver_ling_shi.setText(cls.CONFIG["灵石"])
        settings.wnd_main.edt_deliver_cangbaotu.setText(cls.CONFIG["藏宝图"])
        settings.wnd_main.edt_deliver_tong_bao_dai.setText(cls.CONFIG["通宝袋"])
        settings.wnd_main.edt_deliver_peach.setText(cls.CONFIG["桃子"])
        settings.wnd_main.edt_deliver_xun_ma.setText(cls.CONFIG["驯马材料"])
        settings.wnd_main.edt_deliver_fu_ben.setText(cls.CONFIG["副本产出"])
        settings.wnd_main.edt_deliver_expansive.setText(cls.CONFIG["贵重物品"])
        settings.wnd_main.edt_deliver_equip.setText(cls.CONFIG["回炉装备"])
        settings.wnd_main.edt_deliver_yxb.setText(cls.CONFIG["游戏币"])

        settings.wnd_main.chk_deliver_banghui.setChecked(cls.CONFIG["跑帮物品开关"])
        settings.wnd_main.chk_deliver_yao_cai.setChecked(cls.CONFIG["药材开关"])
        settings.wnd_main.chk_deliver_zhu_fu_cai.setChecked(
            cls.CONFIG["主辅材开关"])
        settings.wnd_main.chk_deliver_za_huo.setChecked(cls.CONFIG["杂货开关"])
        settings.wnd_main.chk_deliver_bb_skill_book.setChecked(
            cls.CONFIG["技能书开关"])
        settings.wnd_main.chk_deliver_ling_shi.setChecked(cls.CONFIG["灵石开关"])
        settings.wnd_main.chk_deliver_fu_ben.setChecked(cls.CONFIG["副本产出开关"])
        settings.wnd_main.chk_deliver_cangbaotu.setChecked(cls.CONFIG["藏宝图开关"])
        settings.wnd_main.chk_deliver_tong_bao_dai.setChecked(
            cls.CONFIG["通宝袋开关"])
        settings.wnd_main.chk_deliver_peach.setChecked(cls.CONFIG["桃子开关"])
        settings.wnd_main.chk_deliver_xun_ma.setChecked(cls.CONFIG["驯马材料开关"])
        settings.wnd_main.chk_deliver_expansive.setChecked(
            cls.CONFIG["贵重物品开关"])
        settings.wnd_main.chk_deliver_equip.setChecked(
            cls.CONFIG["回炉装备开关"])
        settings.wnd_main.chk_deliver_yxb.setChecked(
            cls.CONFIG["游戏币开关"])
        settings.wnd_main.chk_deliver_jiao_left_one.setChecked(
            cls.CONFIG["卡格子交货"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["交接位置"] = settings.wnd_main.cmb_deliver_pos.currentText()
        cls.CONFIG["跑帮物品"] = settings.wnd_main.edt_deliver_banghui.text()
        cls.CONFIG["药材"] = settings.wnd_main.edt_deliver_yao_cai.text()
        cls.CONFIG["主辅材"] = settings.wnd_main.edt_deliver_zhu_fu_cai.text()
        cls.CONFIG["杂货"] = settings.wnd_main.edt_deliver_za_huo.text()
        cls.CONFIG["技能书"] = settings.wnd_main.edt_deliver_bb_skill_book.text()
        cls.CONFIG["灵石"] = settings.wnd_main.edt_deliver_ling_shi.text()
        cls.CONFIG["副本产出"] = settings.wnd_main.edt_deliver_fu_ben.text()
        cls.CONFIG["藏宝图"] = settings.wnd_main.edt_deliver_cangbaotu.text()
        cls.CONFIG["通宝袋"] = settings.wnd_main.edt_deliver_tong_bao_dai.text()
        cls.CONFIG["桃子"] = settings.wnd_main.edt_deliver_peach.text()
        cls.CONFIG["驯马材料"] = settings.wnd_main.edt_deliver_xun_ma.text()
        cls.CONFIG["贵重物品"] = settings.wnd_main.edt_deliver_expansive.text()
        cls.CONFIG["回炉装备"] = settings.wnd_main.edt_deliver_equip.text()
        cls.CONFIG["游戏币"] = settings.wnd_main.edt_deliver_yxb.text()

        cls.CONFIG["跑帮物品开关"] = settings.wnd_main.chk_deliver_banghui.isChecked()
        cls.CONFIG["药材开关"] = settings.wnd_main.chk_deliver_yao_cai.isChecked()
        cls.CONFIG["主辅材开关"] = settings.wnd_main.chk_deliver_zhu_fu_cai.isChecked()
        cls.CONFIG["杂货开关"] = settings.wnd_main.chk_deliver_za_huo.isChecked()
        cls.CONFIG["技能书开关"] = settings.wnd_main.chk_deliver_bb_skill_book.isChecked()
        cls.CONFIG["灵石开关"] = settings.wnd_main.chk_deliver_ling_shi.isChecked()
        cls.CONFIG["副本产出开关"] = settings.wnd_main.chk_deliver_fu_ben.isChecked()
        cls.CONFIG["藏宝图开关"] = settings.wnd_main.chk_deliver_cangbaotu.isChecked()
        cls.CONFIG["通宝袋开关"] = settings.wnd_main.chk_deliver_tong_bao_dai.isChecked()
        cls.CONFIG["桃子开关"] = settings.wnd_main.chk_deliver_peach.isChecked()
        cls.CONFIG["驯马材料开关"] = settings.wnd_main.chk_deliver_xun_ma.isChecked()
        cls.CONFIG["贵重物品开关"] = settings.wnd_main.chk_deliver_expansive.isChecked()
        cls.CONFIG["回炉装备开关"] = settings.wnd_main.chk_deliver_equip.isChecked()
        cls.CONFIG["游戏币开关"] = settings.wnd_main.chk_deliver_yxb.isChecked()
        cls.CONFIG["卡格子交货"] = settings.wnd_main.chk_deliver_jiao_left_one.isChecked()
        super().cfg_save(plan_name)


class TaskJieHuo(TaskBase):
    TASK_NAME = "接货"

    @classmethod
    def run(cls, wk: Worker):
        deliver_name = wk.cfg_plan_task["交接位置"]
        if deliver_name == "原地":
            wk.record("接货位置：原地")
        elif deliver_name == "帮会领地":
            wk.record("接货位置：帮会领地")
            if not cls.is_in_bang_hui(wk):
                cls.go_to_bang_hui(wk)
        else:
            x, y = POS_DICT[deliver_name]
            x = x + rnd(2, 8) if rnd(0, 1) else x - rnd(2, 8)
            y = y + rnd(2, 6)
            wk.record(f"在 {x},{y} 处接货, 正在跑向交接地点...")
            cls.go_to_deliver_pos(wk, x, y)
        for i in range(10000000):
            if wk.is_fight:
                wk.should_run_away = True
                cls.fight_operation(wk)
                wk.should_run_away = False
            if i % 10 == 0:
                wk.record("交易准备就绪...")
            if cls.is_popup_confirm_show(wk):
                cls.click_confirm(wk, RECT=RECT_POPUP)
            msleep(400)
            if cls.is_trade_page_open(wk, timeout=400):
                cls.trade_shou_huo(wk)
            else:
                cls.close_pages(wk)

            if cls.is_system_broad_show(wk, "东西太多了无法交易"):
                wk.record("东西太多了无法交易...")
                if wk.cfg_plan_task["接货包满飞回门派"]:
                    wk.key_press(VK_F8)  # 直接飞门派
                break

    @classmethod
    def trade_shou_huo(cls, wk: Worker):
        wk.record("---- 开始交易 ----")
        player_name = ""
        for _ in range(3):
            msleep(600)
            player_name = cls.ocr_trade_player_name(wk)
            if player_name:
                break
            cls.click_cancel(wk)  # 如果这里进了别人的邀请
            cls.close_system_page(wk)  # 关闭系统页面
        if not player_name:
            wk.record("---- 交易失败 ----")
            cls.close_pages(wk)
            return
        wk.record(f"正在与 {player_name} 交易...")

        cls.trade_lock(wk)  # 直接先锁定
        if not cls.wait_against_lock(wk, timeout=60000):
            wk.record(f"等待对方锁定超时, 对方名称: {player_name}")
            cls.close_pages(wk)
            return
        cls.close_other_talk(wk)
        cls.trade_lock(wk)  # 防止之前没锁定
        cls.click_confirm(wk, RECT=RECT_TRADE_CONFIRM)
        wk.record("---- 交易成功 ----")

    @classmethod
    def go_to_deliver_pos(cls, wk: Worker, x: int, y: int):
        cls.back_to_kai_feng(wk)
        wk.record(f"正在前往交接地点...")
        if not cls.big_map_click(wk, x, y):
            wk.record("寻路失败, 重试中...")
            msleep(600)
            cls.big_map_click(wk, x, y)
        for i in range(100):
            msleep(800)
            if wk.is_stuck:
                if cls.is_talk_open(wk):
                    cls.close_other_talk(wk)
                    cls.big_map_click(wk, x, y)
                    wk.is_stuck = False
                else:
                    wk.record("到达交接地点")
                    break

    @classmethod
    def get_default_biz_config(cls):
        return {
            "接货包满飞回门派": True,
            "交接位置": "原地",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_deliver_jie_full_back.setChecked(
            cls.CONFIG["接货包满飞回门派"])
        settings.wnd_main.cmb_deliver_pos.setCurrentText(cls.CONFIG["交接位置"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["接货包满飞回门派"] = settings.wnd_main.chk_deliver_jie_full_back.isChecked()
        cls.CONFIG["交接位置"] = settings.wnd_main.cmb_deliver_pos.currentText()
        super().cfg_save(plan_name)


def filter_fn(wk: Worker, pic: str, RECT: tuple, MAX_COUNT=8, click=False) -> int:
    # 返回找到的数量
    idx_x_y_list = wk.find_pic_ex(*RECT, pic)
    found_count = 0
    for _, zb_x, zb_y in idx_x_y_list:
        if click == False and wk.find_pic(*RECT, PIC_JINTI):
            return 1
        if click == True and wk.find_pic_r_click(*RECT, PIC_JINTI):
            found_count+=1
            continue
        if found_count > MAX_COUNT:
            break
        wk.move_to(zb_x, zb_y)
        msleep(100)
        wk.move_relative(4, 4)
        msleep(200)
        x, y = wk.get_str_pos(*RECT_FULL, "察看", COLOR_CHAKAN, timeout=800)
        msleep(200)
        if x and wk.find_color(x-25, y+30, x, y+40, COLOR_WHITE):
            wk.record(f"发现白色装备, 跳过")
            continue
        # 识别装备等级
        level = 0
        x2, y2 = wk.get_str_pos(
            x-90, y+75, x+100, y+136, "等级", COLOR_WHITE, zk=ZK_ALL_9, timeout=400)
        if x2:
            level_str = wk.ocr(x2 + 5, y2 - 3, x2 + 45,
                                y2 + 20, COLOR_WHITE, zk=ZK_DIGIT_9)
            if level_str:
                level = int(level_str)
        # 看装备类型: 武器衣服<80, 头盔鞋子腰带<90, 跳过
        equip_type = wk.ocr(x-13, y+37, x+44, y+65,
                            COLOR_OBJECT_DESC, zk=ZK_ALL_9)
        if equip_type and level:
            wk.record(f"装备类型:{equip_type}, 等级:{level}")
            if equip_type not in ["武器", "铠甲"] or (equip_type == "武器" and level < 80) or (equip_type == "铠甲" and level < 90):
                wk.record(f'装备类型:{equip_type}, 等级:{level}, 不符合回炉标准, 跳过')
                continue
            wk.record(f"装备类型:{equip_type}, 等级:{level} 符合条件")
            if click:
                wk.move_r_click(zb_x, zb_y, re_move=False)
            found_count += 1
    return found_count
