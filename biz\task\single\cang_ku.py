from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker
import settings


class TaskCangKu(TaskBase):
	TASK_NAME = "仓库取物"
	IS_FREE = True
	SHUXING_LIST = ["jin", "mu", "shui", "huo", "tu"]
	SHUXING_ZH_LIST = ["金", "木", "水", "火", "土"]
	JIBIE_ZH_LIST = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]

	@classmethod
	def run(cls, wk: Worker):
		cls.back_to_kai_feng(wk)
		if not cls.open_kezhan_page(wk):
			return
		# 优先取单个的物品
		if wk.cfg_plan_task["会神凝神香"]:
			fetch_pic = "存_高级会神香.bmp|存_会神香.bmp"
			if cls.depot_fetch_things(wk, fetch_pic, fetch_num="1"):
				wk.record("取会神香成功")
			else:
				wk.record("取会神香失败")
			fetch_pic = "存_高级凝神香.bmp|存_凝神香.bmp"
			if cls.depot_fetch_things(wk, fetch_pic, fetch_num="1"):
				wk.record("取凝神香成功")
			else:
				wk.record("取凝神香失败")
		# 再取批量的物品
		fetch_pic = "熊猫香.bmp"
		if (
			wk.cfg_plan_task["灵石"]["取出"]
			and wk.cfg_plan_task["灵石"]["级别"] != [False] * 10
			and wk.cfg_plan_task["灵石"]["属性"] != [False] * 5
		):
			selected_levels = [
				cls.JIBIE_ZH_LIST[i]
				for i, v in enumerate(wk.cfg_plan_task["灵石"]["级别"])
				if v
			]
			selected_shuxing = [
				cls.SHUXING_ZH_LIST[i]
				for i, v in enumerate(wk.cfg_plan_task["灵石"]["属性"])
				if v
			]
			for level in selected_levels:
				for shuxing in selected_shuxing:
					fetch_pic += f"|{level}级{shuxing}灵石.bmp"
		if wk.cfg_plan_task["百里香"]:
			fetch_pic += "|百里香.bmp"
		if wk.cfg_plan_task["通宝袋"]:
			fetch_pic += "|" + wk.match_pic("*通宝袋.bmp")
		if wk.cfg_plan_task["晶体石"]:
			fetch_pic += "|" + wk.match_pic("晶体石_*.bmp")
		if wk.cfg_plan_task["杂货"]:
			fetch_pic += "|" + wk.match_pic("杂货_*.bmp")
		if wk.cfg_plan_task["药材"]:
			fetch_pic += "|" + wk.match_pic("药材_*.bmp")
		if wk.cfg_plan_task["主辅材"]:
			fetch_pic += "|" + \
				wk.match_pic("主材_*.bmp") + "|" + wk.match_pic("辅材_*.bmp")
		if wk.cfg_plan_task["技能书"]:
			fetch_pic += "|" + wk.match_pic("随从*书.bmp")
		if wk.cfg_plan_task["驯马材料"]:
			fetch_pic += "|存_驯马绳.bmp|亚麻线.bmp|捆绳线.bmp"
		if wk.cfg_plan_task["副本产出"]:
			tzsp = wk.match_pic("*套装碎片.bmp")
			fetch_pic += "|" + tzsp + "|随从卡片.bmp|随从卡片2.bmp|随从卡片3.bmp"
		if wk.cfg_plan_task["跑帮物品"]:
			fetch_pic += "|" + PIC_BANG_HUI
		if wk.cfg_plan_task["堂主挑战令"]:
			fetch_pic += "|" + "存_堂主挑战令.bmp"
		if wk.cfg_plan_task["藏宝图"]:
			fetch_pic += "|九州重宝图.bmp|存_超级藏宝图.bmp"
		if wk.cfg_plan_task["桃子"]:
			fetch_pic += "|桃子.bmp"
		if wk.cfg_plan_task["元灵"]:
			fetch_pic += "|低级元灵.bmp|存_高级元灵.bmp"
		if wk.cfg_plan_task["小雇佣令"]:
			fetch_pic += "|存_小雇佣令.bmp|存_雇佣令.bmp"
		if wk.cfg_plan_task["字"]:
			fetch_pic += "|存_倚.bmp|存_天.bmp|存_剑.bmp|存_与.bmp|存_屠.bmp|存_龙.bmp|存_刀.bmp"
		if wk.cfg_plan_task["贵重物品"]:
			fetch_pic += "|" + PIC_EXPANSIVE
		if wk.cfg_plan_task["万溶锡"]:
			fetch_pic += "|存_万溶锡.bmp"
		cls.depot_fetch_things(wk, fetch_pic)

	@classmethod
	def depot_fetch_things(cls, wk: Worker, fetch_pic: str, fetch_num="0"):
		# fetch_num为"0"代表全取
		try:
			res = cls.fetch_cur_page_things(wk, fetch_pic, fetch_num)
			if not (fetch_num != "0" and res):
				for pos in POS_DEPOT_LIST:
					wk.move_click(*pos)  # 换页
					msleep(500)
					res = cls.fetch_cur_page_things(wk, fetch_pic, fetch_num)
					if fetch_num != "0" and res:
						break
			cls.click_confirm(wk)
			msleep(400)
			return True
		except:
			wk.record("仓库取物失败, 背包已满")
			return False

	@classmethod
	def fetch_cur_page_things(cls, wk: Worker, fetch_pic: str, fetch_num="0"):
		for _ in range(COUNT_ONE_DEPOT_THING):
			cls.close_other_talk(wk)
			if not wk.find_pic_r_click(*RECT_DEPOT, fetch_pic, timeout=200):
				return False
			if cls.is_popup_show_info(wk, "物品箱已经不能容纳", timeout=200):
				wk.record("背包已经满了, 仓库取物失败")
				raise Exception("背包已经满了, 仓库取物失败")
			if fetch_num not in ["", "0", "1"]:
				msleep(300)
				cls.input_thing_number(
					wk, int(fetch_num), RECT=RECT_FETCH_THING_CONFIRM)
			if cls.click_confirm(wk, timeout=300, RECT=RECT_FETCH_THING_CONFIRM):
				msleep(400)
			if fetch_num != "0":  # 指定数量的话说明已经成功了
				break
			msleep(200)
		return True

	@classmethod
	def get_default_biz_config(cls):
		return {
			"灵石": {
				"取出": False,
				"级别": [True] * 10,
				"属性": [True] * 5,
			},
			"会神凝神香": True,
			"百里香": False,
			"通宝袋": False,
			"晶体石": False,
			"杂货": False,
			"药材": False,
			"主辅材": False,
			"技能书": False,
			"驯马材料": False,
			"副本产出": False,
			"跑帮物品": False,
			"堂主挑战令": False,
			"藏宝图": False,
			"桃子": False,
			"元灵": False,
			"小雇佣令": False,
			"字": False,
			"贵重物品": False,
			"万溶锡": False,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.groupBox_fetch_lingshi.setChecked(
			cls.CONFIG["灵石"]["取出"])
		for i in range(1, 11):
			chk = getattr(settings.wnd_main, f"chk_fetch_lingshi{i}")
			chk.setChecked(cls.CONFIG["灵石"]["级别"][i - 1])
		for s in cls.SHUXING_LIST:
			chk = getattr(settings.wnd_main, f"chk_fetch_lingshi_{s}")
			chk.setChecked(cls.CONFIG["灵石"]["属性"][cls.SHUXING_LIST.index(s)])
		settings.wnd_main.chk_fetch_hnx.setChecked(cls.CONFIG["会神凝神香"])
		settings.wnd_main.chk_fetch_bai_li_xiang.setChecked(cls.CONFIG["百里香"])
		settings.wnd_main.chk_fetch_tong_bao_dai.setChecked(cls.CONFIG["通宝袋"])
		settings.wnd_main.chk_fetch_jts.setChecked(cls.CONFIG["晶体石"])
		settings.wnd_main.chk_fetch_yao_cai.setChecked(cls.CONFIG["药材"])
		settings.wnd_main.chk_fetch_za_huo.setChecked(cls.CONFIG["杂货"])
		settings.wnd_main.chk_fetch_zhu_fu_cai.setChecked(cls.CONFIG["主辅材"])
		settings.wnd_main.chk_fetch_skill_book.setChecked(cls.CONFIG["技能书"])
		settings.wnd_main.chk_fetch_xun_ma.setChecked(cls.CONFIG["驯马材料"])
		settings.wnd_main.chk_fetch_fu_ben.setChecked(cls.CONFIG["副本产出"])
		settings.wnd_main.chk_fetch_bang_hui.setChecked(cls.CONFIG["跑帮物品"])
		settings.wnd_main.chk_fetch_tang_zhu.setChecked(cls.CONFIG["堂主挑战令"])
		settings.wnd_main.chk_fetch_cangbaotu.setChecked(cls.CONFIG["藏宝图"])
		settings.wnd_main.chk_fetch_peach.setChecked(cls.CONFIG["桃子"])
		settings.wnd_main.chk_fetch_yuan_ling.setChecked(cls.CONFIG["元灵"])
		settings.wnd_main.chk_fetch_gu_yong_ling.setChecked(cls.CONFIG["小雇佣令"])
		settings.wnd_main.chk_fetch_zi.setChecked(cls.CONFIG["字"])
		settings.wnd_main.chk_fetch_expansive.setChecked(cls.CONFIG["贵重物品"])
		settings.wnd_main.chk_fetch_wrx.setChecked(cls.CONFIG["万溶锡"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["灵石"][
			"取出"
		] = settings.wnd_main.groupBox_fetch_lingshi.isChecked()
		cls.CONFIG["灵石"]["级别"] = [
			getattr(settings.wnd_main, f"chk_fetch_lingshi{i}").isChecked()
			for i in range(1, 11)
		]
		cls.CONFIG["灵石"]["属性"] = [
			getattr(settings.wnd_main, f"chk_fetch_lingshi_{s}").isChecked()
			for s in cls.SHUXING_LIST
		]
		cls.CONFIG["会神凝神香"] = settings.wnd_main.chk_fetch_hnx.isChecked()
		cls.CONFIG["百里香"] = settings.wnd_main.chk_fetch_bai_li_xiang.isChecked()
		cls.CONFIG["通宝袋"] = settings.wnd_main.chk_fetch_tong_bao_dai.isChecked()
		cls.CONFIG["晶体石"] = settings.wnd_main.chk_fetch_jts.isChecked()
		cls.CONFIG["药材"] = settings.wnd_main.chk_fetch_yao_cai.isChecked()
		cls.CONFIG["杂货"] = settings.wnd_main.chk_fetch_za_huo.isChecked()
		cls.CONFIG["主辅材"] = settings.wnd_main.chk_fetch_zhu_fu_cai.isChecked()
		cls.CONFIG["技能书"] = settings.wnd_main.chk_fetch_skill_book.isChecked()
		cls.CONFIG["驯马材料"] = settings.wnd_main.chk_fetch_xun_ma.isChecked()
		cls.CONFIG["副本产出"] = settings.wnd_main.chk_fetch_fu_ben.isChecked()
		cls.CONFIG["跑帮物品"] = settings.wnd_main.chk_fetch_bang_hui.isChecked()
		cls.CONFIG["堂主挑战令"] = settings.wnd_main.chk_fetch_tang_zhu.isChecked()
		cls.CONFIG["藏宝图"] = settings.wnd_main.chk_fetch_cangbaotu.isChecked()
		cls.CONFIG["桃子"] = settings.wnd_main.chk_fetch_peach.isChecked()
		cls.CONFIG["元灵"] = settings.wnd_main.chk_fetch_yuan_ling.isChecked()
		cls.CONFIG["小雇佣令"] = settings.wnd_main.chk_fetch_gu_yong_ling.isChecked()
		cls.CONFIG["字"] = settings.wnd_main.chk_fetch_zi.isChecked()
		cls.CONFIG["贵重物品"] = settings.wnd_main.chk_fetch_expansive.isChecked()
		cls.CONFIG["万溶锡"] = settings.wnd_main.chk_fetch_wrx.isChecked()
		super().cfg_save(plan_name)
