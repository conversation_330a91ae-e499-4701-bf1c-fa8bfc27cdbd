from biz.exception import TaskFinalStatusException
from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskJrwXlDg(TaskBase):
    NEED_AVOID_FIGHT = True
    IS_DIFFICULT_TASK = False
    IS_TASK_FIX_EQUIP_BB_ENABLE = True
    IS_TEAM_TASK = True
    IS_ACTIVITY = False  # 是否为活动

    @classmethod
    def run(cls, wk: Worker):
        if not cls.check_the_week(wk):
            return
        wk.setting_count = cls.get_task_setting_count(wk)
        cls.on_ride(wk)
        while cls.end_condition(wk):
            if cls.is_reach_time(wk):
                wk.record("时间到, 自动结束")
                break
            try:  # 如果任务完成达到上限，抛出异常
                cls.receive_task(wk)
                cls.find_way_fight(wk)
            except TaskFinalStatusException as e:
                if cls.handle_final_exception(wk) == "break":
                    break
            except Exception as e:
                if cls.handle_other_exception(wk) == "break":
                    break

    @classmethod
    def handle_final_exception(cls, wk: Worker):
        return "break"

    @classmethod
    def handle_other_exception(cls, wk: Worker):
        return "break"

    @classmethod
    def is_reach_time(cls, wk: Worker):
        return False

    @classmethod
    def get_task_name(cls) -> str:
        raise NotImplementedError

    @classmethod
    def get_task_publisher_name(cls) -> str:
        raise NotImplementedError

    @classmethod
    def end_condition(cls, wk: Worker) -> bool:
        return wk.done_count < wk.setting_count

    @classmethod
    def get_task_setting_count(cls, wk: Worker) -> int:
        raise NotImplementedError

    @classmethod
    def find_way_fight(cls, wk: Worker):
        npc_name, npc_map = "", ""
        if cls.is_fast_mode_go_out(wk):  # 出城超级遁甲
            npc_name = cls.fast_fly(wk)
        if not npc_name:
            cls.open_task_page_quick(wk)
            npc_name = cls.region_task_desc_ocr(wk, COLOR_GOLD, timeout=600)
            npc_map = cls.region_task_desc_ocr(wk, COLOR_RED, timeout=600)
        if not cls.check_target_npc_name(wk, npc_name):  # 如果目标npc不是当前任务的
            if not cls.avoid_other_task_disturb(wk):  # 且没有正确切回的话
                raise Exception("任务切回失败")
        wk.record(f"寻路到任务目标:{npc_map} {npc_name} ...")
        ret = cls.task_npc_find_way(
            wk, cls.get_enemy_talk_item(), npc_name=npc_name, npc_map=npc_map)
        if not ret:
            return
        cls.wait_for_fight(wk)
        if wk.is_fight:
            cls.fight_operation(wk)
            wk.done_count += 1
            wk.record(f"== 已完成:{wk.done_count}/{wk.setting_count} ==")

    @classmethod
    def check_target_npc_name(cls, wk: Worker, npc_name: str):
        return True

    @classmethod
    def is_cur_task_desc(cls, wk: Worker):
        if cls.region_task_desc_ocr(wk) == '':
            wk.record("任务描述被遮挡")
            return True
        return cls.region_task_desc_find_str(wk, "要多加小心", timeout=200)

    @classmethod
    def change_to_origin_task(cls, wk: Worker):
        if cls.region_task_list_find_str_click(wk, "缉拿"):
            wk.record("已切回 原任务")
            msleep(400)
            return True
        return False

    @classmethod
    def get_enemy_talk_item(cls):
        return "抓的就是你"
