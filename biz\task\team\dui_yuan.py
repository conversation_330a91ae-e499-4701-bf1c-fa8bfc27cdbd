from biz.task.__base import TaskBase
from biz.constants.constants import *
from biz.task.team.fu_ben import TaskFuBen, TaskFuBenBB, TaskFuBenPeoPle
from biz.task.team.ming_bu import TaskBoss
from biz.task.team.ming_guai import TaskWu<PERSON>ingYiShou
from biz.task.single.qing_li import TaskQingLi
from biz.task.other import TaskLeaveTeam, TaskSwitchLine, TaskOffRide, TaskOnRide
from utils import *
from biz.obj.worker import Worker


class TaskDuiYuan(TaskBase):
    TASK_NAME = "队员挂机"
    IS_TEAM_TASK = True

    @classmethod
    def run(cls, wk: Worker):
        if cls.get_teammate_count(wk) == 1:
            wk.record("只有自己一个人在队伍中, 自动离队")
            cls.leave_team(wk)
        while True:
            if wk.is_fight:
                cls.fight_operation(wk)
            cls.back_to_team(wk)
            cls.talk_proc(wk)
            cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)
            cls.answer_question(wk)
            try:
                cls.team_action(wk)
            except Exception as e:
                wk.record("队员接收了队长的退队信号,自动离队")
                break
            msleep(800)
        
    @classmethod
    def is_difficult_task(cls, wk: Worker):
        if not wk.team.is_difficult_task:
            return False
        if wk.team.task_name in [TaskFuBenPeoPle.TASK_NAME, TaskFuBenBB.TASK_NAME]:
            return TaskFuBen.is_difficult_task(wk)
        return True
    
    @classmethod
    def after_fight_switch_primary_bb(cls, wk: Worker):
        if not wk.cfg_plan["人物切回首发BB"]:
            return
        if wk.cfg_plan["人宠使用技能"] == "强制使用":
            cls.switch_primary_bb(wk)
            return
        if not cls.is_difficult_task(wk) and not wk.team.is_switch_primary_bb_enable:
            return
        cls.switch_primary_bb(wk)
    
    @classmethod
    def fight_do_something(cls, wk: Worker):
        cls.fight_check_pf(wk)
        if not wk.team.IS_TASK_FIX_EQUIP_BB_ENABLE:
            return
        if not wk.cfg_plan_task["通知队长修理忠诚"]:
            return
        if wk.team.need_fix_equip_bb:
            return
        if rnd(1, 5) != 5:  # 不用太频繁
            return
        if cls.is_system_show_fix_equib_bb(wk):
            wk.team.need_fix_equip_bb = True
            wk.record("需要修理忠诚, 已通知队长")
        if wk.find_pic_click(*RECT_FIGHT_DIE, "死亡回桃花岛.bmp"):
            wk.record("队员死亡回桃花岛")
            wk.mate_check_leave = True
            wk.is_die = True
    
    @classmethod
    def answer_question(cls, wk: Worker):
        if not wk.team.need_answer_question:
            return
        if wk.find_pic(*RECT_FULL, "答题.bmp"):
            wk.record("答题中...")
            wk.find_pic_click(*RECT_FULL, "答题选项.bmp", order=rnd(0,3))
            msleep(500)
            wk.move_click(*POS_CONFIRM)

    @classmethod
    def people_fight_action(cls, wk: Worker):
        if wk.team.is_yi_shou_wu_xing:
            wk.record("全队在打五行异兽中...")
            return TaskWuXingYiShou.people_fight_action(wk)
        if wk.team.fight_random and cls.is_people_action(wk):
            if wk.team.true_enemy_pos != (-1, -1):
                wk.record("攻击 真身")
                wk.move_relative_click(*wk.team.true_enemy_pos)
                return
            idx = rnd(5, 9)
            wk.record(f"人物随机攻击:{idx}")
            x, y = POS_ENENMY_LIST[idx]
            wk.move_relative_click(x, y - rnd(50, 60))
            return
        return super().people_fight_action(wk)
    
    @classmethod
    def bb_fight_action(cls, wk: Worker):
        if wk.team.fight_random and cls.is_bb_action(wk):
            if wk.team.true_enemy_pos != (-1, -1):
                wk.record("攻击 真身")
                wk.move_relative_click(*wk.team.true_enemy_pos)
                return
            idx = rnd(5, 9)
            wk.record(f"BB随机攻击:{idx}")
            x, y = POS_ENENMY_LIST[idx]
            wk.move_relative_click(x, y - rnd(50, 60))
            return
        return super().bb_fight_action(wk)

    @classmethod
    def ready_overlap_auth(cls, wk: Worker):
        if wk.team.need_auth_colors:
            return TaskFuBen.ready_overlap_auth(wk)
        return super().ready_overlap_auth(wk)
        
    
    @classmethod
    def adjust_color(cls, wk: Worker, pic_list_length: int, delta_color: str):
        if wk.team.need_auth_colors:
            return TaskFuBen.adjust_color(wk, pic_list_length, delta_color)
        return super().adjust_color(wk, pic_list_length, delta_color)
    
    @classmethod
    def call_bb(cls, wk: Worker):  # 唤出BB
        if wk.team.is_call_bb_enable or wk.cfg_plan["人宠使用技能"] == "强制使用":
            TaskBoss.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
            res = TaskBoss.call_bb(wk)
            cls.set_task_config(wk)  # 还原
            return res
        return super().call_bb(wk)
    
    @classmethod
    def recognize_enemy(cls, wk: Worker):
        if wk.team.is_super_chanllenge:
            return TaskBoss.recognize_enemy(wk)
        return super().recognize_enemy(wk)
    
    @classmethod
    def people_run_away(cls, wk: Worker):
        if not wk.cfg_plan_task["遇怪逃"]:
            return
        monster_name = wk.cfg_plan_task["怪名"]
        if not monster_name:
            wk.record("未设置怪名, 遇怪逃跑功能失效")
            return
        if wk.find_str(*RECT_FIGHT_ENEMY, monster_name, COLOR_GOLD):
            wk.record(f"遇怪逃跑:{monster_name}")
            cls.click_run_away(wk)
    
    @classmethod
    def after_fight_rest_call_bb(cls, wk: Worker):
        if not wk.is_called_bb:  # 没唤出BB就别休息bb
            return
        if wk.team.is_call_bb_enable or wk.cfg_plan["人宠使用技能"] == "强制使用":
            if not wk.cfg_plan["四大名捕"]["剩怪唤出BB"]:
                return
            cls.do_rest_bb(wk)
    
    @classmethod
    def talk_proc(cls, wk: Worker):
        if not cls.is_talk_open(wk):
            return
        # 自动修理 
        if wk.find_str_click(*RECT_TALK, "修理全部", COLOR_TALK_ITEM) or wk.find_str_click(*RECT_TALK, "修理|确定", COLOR_TALK_ITEM):
            wk.record("自动修理装备")
            msleep(400)
            if wk.find_str_click(
                *RECT_TALK, "修理全部|确定", COLOR_TALK_ITEM, timeout=600
            ):
                msleep(400)
                wk.find_str_click(
                    *RECT_TALK, "修理全部|确定", COLOR_TALK_ITEM, timeout=600
                )
            cls.close_other_talk(wk)
            return
        # 自动补忠诚
        if wk.teach_first and wk.find_str_click(
            *RECT_TALK, "教化全部随从|教化随从|好好的给我教化", COLOR_TALK_ITEM
        ):
            wk.record("自动教化BB")
            msleep(400)
            wk.find_str_click(
                *RECT_TALK, "教化全部随从|好好的给我教化", COLOR_TALK_ITEM, timeout=600
            )
            msleep(400)
            wk.find_str_click(
                *RECT_TALK, "教化全部随从|好好的给我教化", COLOR_TALK_ITEM, timeout=600
            )
            cls.close_other_talk(wk)
            wk.teach_first = False
            return
        # 自动治疗
        if wk.find_str_click(*RECT_TALK, "治疗全部随从|治疗随从|治疗我的", COLOR_TALK_ITEM):
            wk.record("自动治疗BB")
            msleep(400)
            wk.find_str_click(
                *RECT_TALK, "治疗全部随从|治疗我的", COLOR_TALK_ITEM, timeout=600
            )
            msleep(400)
            wk.find_str_click(
                *RECT_TALK, "治疗全部随从|治疗我的", COLOR_TALK_ITEM, timeout=600
            )
            cls.close_other_talk(wk)
            wk.teach_first = True
            return
        # 休息恢复血内
        if wk.find_str_click(*RECT_TALK, "休息恢复血内", COLOR_TALK_ITEM):
            wk.record("休息恢复血内")
            msleep(600)
            wk.find_str_click(*RECT_TALK, "休息", COLOR_TALK_ITEM)
            cls.close_other_talk(wk)
            return
        # 领取帮会双倍
        if wk.find_str_click(*RECT_TALK, "领取帮会双倍", COLOR_TALK_ITEM):
            wk.record("领取帮会双倍")
            cls.close_other_talk(wk)
            return
        # 领取宋双双的双倍
        talk_name = cls.get_talk_name(wk)
        if talk_name == "宋双双":
            if wk.cfg_plan_task["跟随队长领双"]:
                if wk.find_str_click(*RECT_TALK, "领取双倍", COLOR_TALK_ITEM):
                    wk.record("点击 领取双倍")
                    msleep(400)
                double_time = wk.cfg_plan_task["领双时间"]
                if wk.cfg_plan["人宠使用技能"] == "自动判断":
                    if wk.team.task_name == "带队打牛":
                        double_time = wk.cfg_plan["领双时间带队打牛"]
                        if not wk.cfg_plan["领双带队打牛"]:
                            wk.record("方案配置中未勾选打牛领双, 不领双")
                            wk.find_str_click(*RECT_TALK, "离开", COLOR_TALK_ITEM)
                            return
                    elif wk.team.task_name == "围捕大盗":
                        double_time = wk.cfg_plan["领双时间围捕大盗"]
                        if not wk.cfg_plan["领双围捕大盗"]:
                            wk.record("方案配置中未勾选围捕大盗, 不领双")
                            wk.find_str_click(
                                *RECT_TALK, "离开", COLOR_TALK_ITEM)
                            return
                    elif wk.team.task_name == "边关清剿":
                        double_time = wk.cfg_plan["领双时间边关清剿"]
                        if not wk.cfg_plan["领双边关清剿"]:
                            wk.record("方案配置中未勾选边关清剿, 不领双")
                            wk.find_str_click(
                                *RECT_TALK, "离开", COLOR_TALK_ITEM)
                            return
                    elif wk.team.task_name == "百战百胜":
                        double_time = wk.cfg_plan["领双时间百战百胜"]
                        if not wk.cfg_plan["领双百战百胜"]:
                            wk.record("方案配置中未勾选百战百胜, 不领双")
                            wk.find_str_click(
                                *RECT_TALK, "离开", COLOR_TALK_ITEM)
                            return
                if wk.find_str_click(*RECT_TALK, double_time, COLOR_TALK_ITEM):
                    wk.record(f"跟随队长领双 {double_time}")
        if wk.find_str_click(*RECT_TALK, "送福利得来", COLOR_TALK_ITEM):
            wk.record("点击BOSS对话进入战斗")
        # 再发现其它的对话框就直接关掉
        cls.close_other_talk(wk)

    @classmethod
    def team_action(cls, wk: Worker):
        if wk.cfg_plan_task["跟随队长离队"] and wk.team.signal_leave_team.mate_query(wk.row):
            wk.team.signal_leave_team.mate_resp(wk.row)
            if wk.team.signal_leave_team.last_set_ts - settings.cur_time_stamp > 2:
                wk.record("离队信号过期, 只响应不操作")
                
                return
            wk.record("跟随队长离队...")
            TaskLeaveTeam.leave_team(wk)
            raise Exception("离开队伍成功，队员挂机完成")
        
        if wk.cfg_plan_task["跟随队长换线"]:
            line = wk.team.signal_switch_line.mate_query(wk.row)
            wk.team.signal_switch_line.mate_resp(wk.row)
            if line:
                wk.record("跟随队长换线...")
                TaskSwitchLine.switch_line(wk, line)
                
                
        if wk.cfg_plan_task["跟随队长装卸坐骑"]:
            if wk.team.signal_off_ride.mate_query(wk.row):
                wk.record("跟随队长卸下坐骑...")
                wk.team.signal_off_ride.mate_resp(wk.row)
                TaskOffRide.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
                TaskOffRide.run(wk)
                cls.set_task_config(wk)  # 还原
                
            if wk.team.signal_on_ride.mate_query(wk.row):
                wk.record("跟随队长装备坐骑...")
                wk.team.signal_on_ride.mate_resp(wk.row)
                TaskOnRide.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
                TaskOnRide.run(wk)
                cls.set_task_config(wk)  # 还原
                
        if wk.team.signal_drop_garbage.mate_query(wk.row):
            wk.team.signal_drop_garbage.mate_resp(wk.row)
            wk.record("跟随队长清理垃圾...")
            TaskQingLi.set_task_config(wk, cls.TASK_NAME)
            TaskQingLi.throw_things(wk)
            cls.set_task_config(wk)  # 还原
            
        if wk.cfg_plan_task["跟随队长签到祥瑞"]:   
            if wk.team.signal_get_gift.mate_query(wk.row):
                wk.team.signal_get_gift.mate_resp(wk.row)
                wk.record("跟随队长签到祥瑞...")
                if cls.is_gift_enable(wk):
                    cls.get_gift(wk)
                if cls.is_fest_enable(wk):
                    cls.get_fest(wk)
                wk.record("签到祥瑞完成")
                
        if wk.team.signal_rest_bb.mate_query(wk.row):
            wk.team.signal_rest_bb.mate_resp(wk.row)
            wk.record("收到队长发来的休息BB信号")
            TaskBoss.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
            if wk.cfg_plan_task["启动时休息当前BB"]:
                wk.record("配置了超级挑战启动时休息当前BB")
                TaskBoss.do_rest_bb(wk)
            else:
                wk.record("未配置超级挑战启动时休息当前BB")
            cls.set_task_config(wk)  # 还原

    @classmethod
    def get_default_biz_config(cls):
        return {
            "领双时间": "2小时",
            "跟随队长领双": True,
            "跟随队长离队": True,
            "跟随队长换线": True,
            "跟随队长装卸坐骑": True,
            "跟随队长签到祥瑞": True,
            "通知队长修理忠诚": False,
            "遇怪逃": False,
            "怪名": "踏云虎|跟班|手下|小弟",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_double_time.setCurrentText(cls.CONFIG["领双时间"])
        settings.wnd_main.chk_duiyuan_ls.setChecked(cls.CONFIG["跟随队长领双"])
        settings.wnd_main.chk_duiyuan_ld.setChecked(cls.CONFIG["跟随队长离队"])
        settings.wnd_main.chk_duiyuan_hx.setChecked(cls.CONFIG["跟随队长换线"])
        settings.wnd_main.chk_duiyuan_off_ride.setChecked(cls.CONFIG["跟随队长装卸坐骑"])
        settings.wnd_main.chk_duiyuan_get_gift.setChecked(cls.CONFIG["跟随队长签到祥瑞"])
        settings.wnd_main.chk_duiyuan_notify_fix_bb.setChecked(cls.CONFIG["通知队长修理忠诚"])
        settings.wnd_main.chk_duiyuan_run.setChecked(cls.CONFIG["遇怪逃"])
        settings.wnd_main.edt_duiyuan_run.setText(cls.CONFIG["怪名"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["领双时间"] = settings.wnd_main.cmb_double_time.currentText()
        cls.CONFIG["跟随队长领双"] = settings.wnd_main.chk_duiyuan_ls.isChecked()
        cls.CONFIG["跟随队长离队"] = settings.wnd_main.chk_duiyuan_ld.isChecked()
        cls.CONFIG["跟随队长换线"] = settings.wnd_main.chk_duiyuan_hx.isChecked()
        cls.CONFIG["跟随队长装卸坐骑"] = settings.wnd_main.chk_duiyuan_off_ride.isChecked()
        cls.CONFIG["跟随队长签到祥瑞"] = settings.wnd_main.chk_duiyuan_get_gift.isChecked()
        cls.CONFIG["通知队长修理忠诚"] = settings.wnd_main.chk_duiyuan_notify_fix_bb.isChecked()
        cls.CONFIG["遇怪逃"] = settings.wnd_main.chk_duiyuan_run.isChecked()
        cls.CONFIG["怪名"] = settings.wnd_main.edt_duiyuan_run.text()
        super().cfg_save(plan_name)