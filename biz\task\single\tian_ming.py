from biz.constants.constants import *
from biz.task.__base import TaskBase
from utils import *
from biz.obj.worker import Worker


class TaskTianMing(TaskBase):
    TASK_NAME = "天命占天"

    @classmethod
    def run(cls, wk: Worker):
        if wk.cfg_plan_task["金票占天"]:
            wk.record("占天方式: 金票占天")
        else:
            wk.record("占天方式: 通宝占天")
        cls.open_tian_ming_page(wk)
        if wk.find_pic_click(*RECT_FULL, "前往占天.bmp", timeout=300):
            msleep(500)
        cls.use_rabish_tian_ming(wk)
        wk.find_pic_click(*RECT_FULL, "天命拾取.bmp")
        for _ in range(1000):
            msleep(300)
            if wk.find_str(*RECT_POPUP, "占天空间不足", COLOR_BLACK):
                wk.record("占天空间不足...")
                cls.click_confirm(wk)
                cls.use_rabish_tian_ming(wk)
                wk.done_count -= 1
            if cls.is_popup_show_info(wk, "无法一键占天"):
                wk.record("金票不足, 无法一键占天")
                cls.click_confirm(wk)
                break
            if wk.find_str(*RECT_POPUP, "次数已使用完毕|通宝不足", COLOR_BLACK):
                wk.record("占天次数或通宝不足，自动退出")
                cls.click_confirm(wk)
                if cls.use_rabish_tian_ming(wk):
                    cls.click_confirm(wk)
                    msleep(200)
                if wk.find_pic_click(*RECT_FULL, "天命拾取.bmp", re_move=False):
                    wk.record("拾取高级天命")
                    cls.click_confirm(wk)
                    msleep(200)
                break
            setting_count = wk.cfg_plan_task["占天次数"]
            if wk.cfg_plan_task["金票占天"]:
                wk.move_click(*POS_TIANMING_YI_JIAN)
                msleep(400)
                if cls.click_confirm(wk):
                    wk.done_count += 1
                    wk.record(f"已完成 {wk.done_count}/{setting_count}")
                    time.sleep(1)
                    if cls.is_popup_confirm_show(wk):
                        continue
                    time.sleep(4)
                    cls.use_rabish_tian_ming(wk)
                    if wk.find_pic_click(*RECT_FULL, "天命拾取.bmp"):
                        wk.record("正在拾取天命...")
            else:
                if not wk.find_pic_offset_click(
                    *RECT_FULL,
                    "占天对话.bmp",
                    delta_color="151515",
                    timeout=400,
                    dx=5,
                    dy=60,
                    is_sleep=False,
                    re_move=False
                ):
                    wk.record("未找到 占天对话, 自动退出")
                    break
                wk.done_count += 1
                wk.record(f"已完成 {wk.done_count}/{setting_count}")
                time.sleep(0.6)
            if wk.done_count >= setting_count:
                break
        wk.key_press(VK_ESC)
        msleep(600)
        wk.key_press(VK_ESC)

    @classmethod
    def is_tian_ming_page_open(cls, wk: Worker, timeout=0):
        return wk.find_pic(*RECT_FULL, "前往占天.bmp", timeout=timeout)

    @classmethod
    def open_tian_ming_page(cls, wk: Worker):
        for i in range(3):
            if cls.is_tian_ming_page_open(wk, timeout=200):
                return
            wk.move_click(*POS_TIANMING)
            msleep(400)

    @classmethod
    def use_rabish_tian_ming(cls, wk: Worker):
        if wk.cfg_plan_task["处理方式"] == "分解" and wk.find_pic_click(*RECT_FULL, "天命分解.bmp"):
            wk.record("分解垃圾天命...")
            return True
        if wk.cfg_plan_task["处理方式"] == "炼化" and wk.find_pic_click(*RECT_FULL, "天命炼化.bmp"):
            wk.record("炼化垃圾天命...")
            return True
        return False

    @classmethod
    def get_default_biz_config(cls):
        return {
            "处理方式": "分解",
            "占天次数": 24,
            "金票占天": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_tian_ming, cls.CONFIG["处理方式"])
        settings.wnd_main.spin_count_zhan_tian.setValue(cls.CONFIG["占天次数"])
        settings.wnd_main.chk_zhan_tian_jin_piao.setChecked(cls.CONFIG["金票占天"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["处理方式"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_tian_ming)
        cls.CONFIG["占天次数"] = settings.wnd_main.spin_count_zhan_tian.value()
        cls.CONFIG["金票占天"] = settings.wnd_main.chk_zhan_tian_jin_piao.isChecked()
        super().cfg_save(plan_name)
