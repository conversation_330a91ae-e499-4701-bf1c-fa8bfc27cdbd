.PHONY: api build upload update run venv

api:
	python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. ./api/netauth/v1/enums.proto ./api/netauth/v1/card.proto

build:
	rmdir /s /q dist 2>nul || echo "dist directory not found"
	python ./binstaller.py build

upload:
	python ./binstaller.py upload

update:
	python ./binstaller.py update

run:
	sudo python ./qiling.py

venv:
	source .venv/Scripts/activate

