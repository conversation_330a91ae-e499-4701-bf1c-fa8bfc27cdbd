# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'wnd_plan_confirm.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *


class Ui_WndPlanConfirm(object):
    def setupUi(self, WndPlanConfirm):
        if not WndPlanConfirm.objectName():
            WndPlanConfirm.setObjectName(u"WndPlanConfirm")
        WndPlanConfirm.resize(201, 102)
        self.verticalLayout = QVBoxLayout(WndPlanConfirm)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.label = QLabel(WndPlanConfirm)
        self.label.setObjectName(u"label")
        self.label.setMargin(2)

        self.verticalLayout.addWidget(self.label)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_4)

        self.cmb_plan_list = QComboBox(WndPlanConfirm)
        self.cmb_plan_list.setObjectName(u"cmb_plan_list")
        self.cmb_plan_list.setMinimumSize(QSize(80, 0))
        self.cmb_plan_list.setStyleSheet(u"/* \u4e0b\u62c9\u5217\u8868\u7684\u5bb9\u5668\u6837\u5f0f */\n"
"/* \u4e0b\u62c9\u5217\u8868\u7684\u5bb9\u5668\u6837\u5f0f */\n"
"            QComboBox QAbstractItemView {\n"
"                font-size: 10px;           /* \u5b57\u4f53\u5927\u5c0f */\n"
"                show-decoration-selected: 1; /* \u9009\u4e2d\u9879\u9ad8\u4eae */\n"
"            }\n"
"\n"
"            /* \u4e0b\u62c9\u5217\u8868\u7684\u6bcf\u4e00\u9879 */\n"
"            QComboBox QAbstractItemView::item {\n"
"                height: 11px;              /* \u6bcf\u9879\u9ad8\u5ea6 */\n"
"                margin: 0px;               /* \u79fb\u9664\u9ed8\u8ba4\u5916\u8fb9\u8ddd */\n"
"                padding: 0px;              /* \u9879\u5185\u8fb9\u8ddd */\n"
"                border: none;\n"
"            }")

        self.horizontalLayout_2.addWidget(self.cmb_plan_list)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_5)


        self.verticalLayout.addLayout(self.horizontalLayout_2)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)

        self.btn_confirm = QPushButton(WndPlanConfirm)
        self.btn_confirm.setObjectName(u"btn_confirm")
        self.btn_confirm.setMinimumSize(QSize(60, 22))

        self.horizontalLayout.addWidget(self.btn_confirm)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_3)

        self.btn_cancel = QPushButton(WndPlanConfirm)
        self.btn_cancel.setObjectName(u"btn_cancel")
        self.btn_cancel.setMinimumSize(QSize(60, 22))

        self.horizontalLayout.addWidget(self.btn_cancel)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_2)


        self.verticalLayout.addLayout(self.horizontalLayout)


        self.retranslateUi(WndPlanConfirm)

        self.btn_confirm.setDefault(True)


        QMetaObject.connectSlotsByName(WndPlanConfirm)
    # setupUi

    def retranslateUi(self, WndPlanConfirm):
        WndPlanConfirm.setWindowTitle(QCoreApplication.translate("WndPlanConfirm", u"\u63d0\u793a", None))
        self.label.setText(QCoreApplication.translate("WndPlanConfirm", u"\u5c06\u5f53\u524d\u914d\u7f6e\u4fdd\u5b58\u5230\u65b9\u6848", None))
        self.btn_confirm.setText(QCoreApplication.translate("WndPlanConfirm", u"\u786e\u5b9a", None))
        self.btn_cancel.setText(QCoreApplication.translate("WndPlanConfirm", u"\u53d6\u6d88", None))
    # retranslateUi

