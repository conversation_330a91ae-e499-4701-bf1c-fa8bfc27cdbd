from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskShiYong(TaskBase):
    TASK_NAME = "使用物品"
    IS_FREE = True

    @classmethod
    def run(cls, wk: Worker):
        cls.stop_auto_find_way(wk)
        if wk.cfg_plan_task["百里香"]:
            wk.record("使用百里香...")
            cls.bag_use_item(wk, "百里香.bmp")
        if wk.cfg_plan_task["会神凝神香"]:
            wk.record("使用会神香和凝神香...")
            cls.bag_use_item(wk, "存_高级会神香.bmp|存_会神香.bmp")
            cls.bag_use_item(wk, "存_高级凝神香.bmp|存_凝神香.bmp")
        if wk.cfg_plan_task["修炼卷"]:
            wk.record("使用修炼卷...")
            cls.bag_use_item(wk, "用_修炼卷.bmp")
        if wk.cfg_plan_task["通宝袋"]:
            wk.record("使用通宝袋...")
            pic_names = wk.match_pic("*通宝*.bmp")
            cls.bag_use_item_all(wk, pic_names)
        if wk.cfg_plan_task["天命碎片袋"]:
            wk.record("使用天命碎片袋...")
            cls.bag_use_item_all(wk, "用_天命碎片袋.bmp")
        if wk.cfg_plan_task["行囊月卡"]:
            wk.record("使用行囊月卡...")
            cls.bag_use_item_all(wk, "用_行囊月卡.bmp|用_高级行囊月卡.bmp")
        if wk.cfg_plan_task["四象洗练丹"]:
            wk.record("使用四象洗练丹...")
            pic_names = wk.match_pic("*四象洗练丹.bmp")
            cls.bag_use_item_all(wk, pic_names)
        if wk.cfg_plan_task["名望宝盒"]:
            wk.record("使用名望宝盒...")
            cls.bag_use_item_all(wk, "名望宝盒.bmp")

    @classmethod
    def get_default_biz_config(cls):
        return {
            "百里香": False,
            "会神凝神香": True,
            "修炼卷": True,
            "通宝袋": True,
            "天命碎片袋": True,
            "名望宝盒": True,
            "行囊月卡": True,
            "四象洗练丹": True,
        }
    
    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_shiyong_bai_li_xiang.setChecked(cls.CONFIG["百里香"])
        settings.wnd_main.chk_shiyong_hnx.setChecked(cls.CONFIG["会神凝神香"])
        settings.wnd_main.chk_shiyong_xiulianjuan.setChecked(cls.CONFIG["修炼卷"])
        settings.wnd_main.chk_shiyong_tongbaodai.setChecked(cls.CONFIG["通宝袋"])
        settings.wnd_main.chk_shiyong_tianmingsuipian.setChecked(cls.CONFIG["天命碎片袋"])
        settings.wnd_main.chk_shiyong_ming_wang.setChecked(cls.CONFIG["名望宝盒"])
        settings.wnd_main.chk_shiyong_xinglangyueka.setChecked(cls.CONFIG["行囊月卡"])
        settings.wnd_main.chk_shiyong_sixiang.setChecked(cls.CONFIG["四象洗练丹"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["百里香"] = settings.wnd_main.chk_shiyong_bai_li_xiang.isChecked()
        cls.CONFIG["会神凝神香"] = settings.wnd_main.chk_shiyong_hnx.isChecked()
        cls.CONFIG["修炼卷"] = settings.wnd_main.chk_shiyong_xiulianjuan.isChecked()
        cls.CONFIG["通宝袋"] = settings.wnd_main.chk_shiyong_tongbaodai.isChecked()
        cls.CONFIG["天命碎片袋"] = settings.wnd_main.chk_shiyong_tianmingsuipian.isChecked()
        cls.CONFIG["名望宝盒"] = settings.wnd_main.chk_shiyong_ming_wang.isChecked()
        cls.CONFIG["行囊月卡"] = settings.wnd_main.chk_shiyong_xinglangyueka.isChecked()
        cls.CONFIG["四象洗练丹"] = settings.wnd_main.chk_shiyong_sixiang.isChecked()
        super().cfg_save(plan_name)

    
