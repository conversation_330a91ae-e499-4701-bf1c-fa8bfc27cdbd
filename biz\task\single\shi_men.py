from biz.task.__base import TaskBase
from biz.task.single.cang_ku import TaskCangKu
from biz.task.single.qing_li import TaskQingLi
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskShiMen(TaskBase):
    TASK_NAME = "师门任务"
    NEED_AVOID_FIGHT = False
    IS_TASK_FIX_EQUIP_BB_ENABLE = True

    @classmethod
    def get_enemy_color_list(cls, wk: Worker):
        return [COLOR_GREEN, COLOR_GOLD, COLOR_RED]

    @classmethod
    def run(cls, wk: Worker):
        cls.refresh_task_list(wk)
        while wk.done_count < cls.get_task_setting_count(wk):
            run_away = cls.get_teammate_count(wk) <= 1
            try:
                wk.should_run_away = run_away
                cls.receive_task(wk)
                wk.should_run_away = False
            except Exception as e:
                wk.record("任务完成达到上限")
                break
            try:
                cls.do_task(wk, run_away=run_away)
            except:  # 任务终态
                cls.refresh_task_list(wk)
            msleep(600)
        if not wk.cfg_plan_task["无限做"]:  # 最后一环交任务
            cls.reply_task(wk)

    @classmethod
    def reply_task(cls, wk: Worker):
        wk.record("最后一环交任务中...")
        cls.goto_recv_task_place(wk)
        for i in range(200):
            if cls.is_talk_open(wk) and cls.get_talk_name(wk) in "玄金真人|孔辰|执善堂主事|藏经阁主事|唐大":
                if cls.talk_click_specify_item(wk, "任务", COLOR_TALK_ITEM_TASK, close_talk=False):
                    msleep(500)
                    cls.close_other_talk(wk)
                    wk.record("交任务完成")
                else:
                    wk.record("不需要交任务")
                break
            if i == 0 or wk.is_stuck:
                cls.click_system_task_name(wk, enable_white=True)
            msleep(600)

    @classmethod
    def receive_task(cls, wk: Worker):
        if wk.need_clear_bag:
            wk.record('接任务前需要先清理背包...')
            TaskQingLi.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
            TaskQingLi.throw_things(wk)  # 丢东西
            TaskQingLi.use_things(wk)  # 用东西
            cls.set_task_config(wk)  # 还原
            wk.need_clear_bag = False
        super().receive_task(wk)

    @classmethod
    def click_task_name(
        cls, wk: Worker, task_name: str, is_activity=False, enable_white=False, timeout=400
    ):
        if wk.cfg_plan_task["无限做"]:
            return super().click_task_name(wk, task_name, is_activity, True, timeout)
        else:
            return super().click_task_name(wk, task_name, is_activity, False, timeout)

    @classmethod
    def throw_things(cls, wk: Worker, additional_pics=""):
        additional_pics = "金针.bmp|金创药.bmp|玲珑生肌膏.bmp|" + wk.match_pic("药材_*.bmp") + "|" + wk.match_pic("装备_*.bmp")
        super().throw_things(wk, additional_pics)

    @classmethod
    def big_map_click(cls, wk: Worker, x: int, y: int):
        if cls.in_abnormal_fight(wk):
            wk.record("战斗发生异常")
            if cls.get_teammate_count(wk) <= 1:
                wk.record("直接逃跑")
                cls.click_run_away(wk)
            else:
                wk.record("有队伍, 不逃跑")
                wk.key_press_combo(VK_ALT, VK_A)
                wk.key_press_combo(VK_ALT, VK_A)
            msleep(600)
            return False
        return super().big_map_click(wk, x, y)

    @classmethod
    def get_task_setting_count(cls, wk: Worker):
        return 20

    @classmethod
    def get_task_name(cls) -> str:
        return "师门任务"

    @classmethod
    def get_task_publisher_name(cls) -> str:
        return "师门环任务"

    @classmethod
    def goto_recv_task_place(cls, wk: Worker):
        cls.back_to_school(wk)

    @classmethod
    def talk_recv_task(cls, wk: Worker) -> bool:
        if cls.talk_click_specify_item(wk, "任务", COLOR_TALK_ITEM_TASK, close_talk=False):
            msleep(500)
            cls.close_other_talk(wk)
            return False
        return cls.talk_click_specify_item(wk, "巾帼英雄|降妖除魔|听不听故事|长老有事|求您赶紧说")

    @classmethod
    def click_system_task_name(cls, wk: Worker, enable_white=False):
        res = super().click_system_task_name(wk, enable_white)
        # 有时不会触发对话
        wk.find_str_offset_click(*RECT_FIND, "任务发布", COLOR_GREEN+"|"+COLOR_CYAN,
                                 dx=30, dy=90, limit_border=True, limit_y=Y_LIMIT_CLICK)
        return res

    @classmethod
    def is_cur_task_desc(cls, wk: Worker):
        if not super().is_cur_task_desc(wk):
            return False
        if cls.region_task_status_get_status(wk) in ["成功", "失败", "过期"]:
            wk.record("发现任务处于终态, 刷新一下")
            cls.click_refresh_task_pic(wk)
            msleep(1000)
        if cls.region_task_desc_ocr(wk) == "":  # 找寻任务没有描述
            return True
        if cls.region_task_status_find_str(wk, "截止", COLOR_TASK_NAME):
            return True
        if not cls.region_task_desc_find_str(wk, "要多加小心|当前应该在|到杀个|那购买|寻找个|送到的"):
            return False
        if cls.region_task_status_find_str(wk, "林冲", COLOR_GOLD):
            return False
        return True

    @classmethod
    def change_to_origin_task(cls, wk: Worker):
        if cls.region_task_list_find_pic_offset_click(wk, "门派跑环任务.bmp"):
            wk.record("已切回 原任务")
            msleep(400)
            return True
        return False

    @classmethod
    def do_task(cls, wk: Worker, run_away=True):
        if wk.cfg_plan_task["熊猫香避怪"]:
            cls.use_xmx(wk)
        cls.open_task_page(wk)
        if not cls.is_cur_task_desc(wk) and not cls.avoid_other_task_disturb(wk):
            return
        if cls.region_task_status_get_status(wk) != "进行中":
            cls.close_pages(wk)
            return
        
        # 根据不同的任务来做操作
        if cls.region_task_status_find_str(wk, "江湖万事通", COLOR_GOLD):
            cls.do_task_query_jhwst(wk)  # 缉拿 和 找寻 和 买药
        if cls.region_task_desc_find_str(wk, "当前应该在"):
            return cls.do_task_find_people(wk, run_away=run_away)
        if cls.region_task_desc_find_str(wk, "要多加小心"):
            return cls.do_task_fight_people(wk, run_away=run_away)
        if cls.region_task_desc_find_str(wk, "到杀个"):  # 消灭XX
            return cls.do_task_kill_monster(wk, run_away=run_away)
        if cls.region_task_desc_find_str(wk, "信函", COLOR_GREEN):
            return cls.do_task_send_mail(wk, run_away=run_away)
        if cls.region_task_desc_find_str(wk, "寻找"):  # 收集物品
            return cls.do_task_find_thing(wk)
        if cls.region_task_desc_find_str(wk, "那购买"):  # 购买 跌打白药 罗汉明子膏 玉垒浮云膏
            return cls.do_task_shop_buy_thing(wk, run_away=run_away)
        wk.record("识别子任务失败...")
        msleep(1000)

    @classmethod
    def do_task_find_people(cls, wk: Worker, run_away=True):
        npc_name = cls.region_task_desc_ocr(wk, COLOR_GOLD)
        wk.record(f"找寻 {npc_name}...")
        cls.close_pages(wk)
        if run_away:
            cls.task_npc_find_way_run_away(
                wk, "找寻", color=COLOR_TALK_ITEM_TASK, npc_name=npc_name)
        else:
            cls.task_npc_find_way(
                wk, "找寻", color=COLOR_TALK_ITEM_TASK, npc_name=npc_name)
        if wk.is_fight:
            cls.fight_operation(wk)
            cls.do_fix_bb(wk)

    @classmethod
    def do_task_fight_people(cls, wk: Worker, run_away=True):
        npc_name = cls.region_task_desc_ocr(wk, COLOR_GOLD)
        wk.record(f"缉拿 {npc_name}...")
        if run_away:
            cls.task_npc_find_way_run_away(
                wk, "看招", color=COLOR_TALK_ITEM, npc_name=npc_name)
        else:
            cls.task_npc_find_way(
                wk, "看招", color=COLOR_TALK_ITEM, npc_name=npc_name)
        msleep(800)
        if wk.is_fight:
            cls.fight_operation(wk)
            cls.do_fix_bb(wk)
        if cls.is_popup_show_info(wk, "物品栏已满"):
            wk.record("物品栏已满")
            cls.close_pages(wk)
            wk.need_clear_bag = True
            return

    @classmethod
    def do_task_send_mail(cls, wk: Worker, run_away=True):
        msleep(2000)
        npc_name = cls.region_task_status_get_npc_name(wk)
        wk.record(f"送信函给 {npc_name}")
        msleep(5000)
        cls.close_pages(wk)
        if run_away:
            cls.task_npc_find_way_run_away(
                wk, "送信函", npc_name=npc_name, color=COLOR_TALK_ITEM_TASK)
        else:
            cls.task_npc_find_way(
                wk, "送信函", npc_name=npc_name, color=COLOR_TALK_ITEM_TASK)

    @classmethod
    def do_task_find_thing(cls, wk: Worker):
        thing_name = cls.region_task_desc_ocr(wk, COLOR_GREEN)
        thing_num = cls.region_task_desc_ocr_one_line(
            wk, COLOR_GREEN, zk=ZK_DIGIT_11)
        wk.record(f"寻找物品:{thing_name} 数量:{thing_num}")
        cls.close_pages(wk)
        if wk.cfg_plan_task["任务物品"] == "背包自备":
            wk.record("收集杂货, 背包自备...")
            return
        if wk.cfg_plan_task["任务物品"] == "门派仓库":
            wk.record("收集杂货, 门派仓库取货...")
            cls.back_to_school(wk)
            if not cls.find_way_npc(wk, "闭关", ["包裹与仓库"]):
                wk.record("打开门派仓库失败")
                return
            if TaskCangKu.depot_fetch_things(wk, "杂货_"+thing_name+".bmp", thing_num) is False:
                cls.close_pages(wk)
                wk.need_clear_bag = True
            return
        # 寄售购买
        wk.record("收集杂货, 寄售购买...")
        cls.ji_shou_buy_thing(wk, thing_name, thing_num)

    @classmethod
    def do_task_shop_buy_thing(cls, wk: Worker, run_away=True):
        # 识别 商店物品名 物品数量
        thing_name = cls.region_task_desc_ocr(wk, COLOR_GREEN)
        thing_num = cls.region_task_desc_ocr(wk, COLOR_GREEN, zk=ZK_DIGIT_11)
        wk.record(f"商店购买物品:{thing_name} 数量:{thing_num}")
        if run_away:
            cls.task_npc_find_way_run_away(wk, "买卖")  # 是哪个NPC已经问过江湖万事通了
        else:
            cls.task_npc_find_way(wk, "买卖")
        if cls.is_shop_open(wk, timeout=600):
            if thing_name == "奔雷":  # 有几个图片一样的, 只能点特定位置
                wk.record("选择 奔雷")
                wk.move_click(430, 339, click_count=4)
                wk.move_click(296, 315)  # 选中
            else:
                cls.select_thing(wk, thing_name+".bmp")

            if thing_num in ["", "1"]:
                cls.shop_buy(wk)
            else:
                cls.shop_input_thing_number_and_buy(wk, int(thing_num))
        wk.record("商店购买物品完成")

    @classmethod
    def do_task_kill_monster(cls, wk: Worker, run_away=True):
        target_map_name = cls.region_task_desc_ocr_one_line(wk, COLOR_RED)
        wk.record(f"到{target_map_name}消灭怪物...")
        cls.close_pages(wk)
        if run_away:
            cls.run_to_map_run_away(wk, target_map_name)
        else:
            cls.run_to_map(wk, target_map_name)
        cls.use_hls(wk)
        for _ in range(500):
            if wk.is_fight:
                cls.fight_operation(wk, check_xmx=False)
                if cls.do_fix_bb(wk):  # 如果修理忠诚 要再回来原地图
                    if run_away:
                        cls.run_to_map_run_away(wk, target_map_name)
                    else:
                        cls.run_to_map(wk, target_map_name)
                cls.stop_auto_find_way(wk, force=True)
                if cls.is_task_status_turn_reply(wk):
                    if not run_away:
                        cls.use_xmx(wk)
                    break
                wk.is_stuck = True
            if wk.is_stuck:
                cls.auto_meet_enemy_by_shortcut(wk)
            msleep(800)

    @classmethod
    def is_task_status_turn_reply(cls, wk: Worker) -> bool:
        cls.open_task_page(wk)
        cls.avoid_other_task_disturb(wk)
        res = cls.region_task_status_get_status(wk) == "回复"
        cls.close_pages(wk)
        return res

    @classmethod
    def get_default_biz_config(cls):
        return {
            "无限做": False,
            "熊猫香避怪": False,
            "任务物品": "背包自备",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_shi_men_forever.setChecked(cls.CONFIG["无限做"])
        settings.wnd_main.chk_shi_men_xmx.setChecked(cls.CONFIG["熊猫香避怪"])
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_shi_men_task_thing, cls.CONFIG["任务物品"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["无限做"] = settings.wnd_main.chk_shi_men_forever.isChecked()
        cls.CONFIG["熊猫香避怪"] = settings.wnd_main.chk_shi_men_xmx.isChecked()
        cls.CONFIG["任务物品"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_shi_men_task_thing)
        super().cfg_save(plan_name)
