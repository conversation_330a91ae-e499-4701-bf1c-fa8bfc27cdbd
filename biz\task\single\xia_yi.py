from biz.constants.constants import *
from biz.task.__base import TaskBase
from biz.obj.worker import Worker
from utils.utils import msleep


@classmethod
class TaskXiaYi(TaskBase):
    TASK_NAME = "侠义礼券"
    IS_FREE = True

    @classmethod
    def run(cls, wk: Worker):
        cls.back_to_kai_feng(wk)
        if cls.cur_map(wk) != "开封":
            wk.record("回城失败, 无法完成清理任务")
            return
        if not cls.talk_with_cur_map_npc(wk, "侠义盟使者", ["兑换礼券"]):
            wk.record("打开礼券兑换页失败")
            return
        for i in range(100):
            wk.move_click(316, 370)  # 1000
            wk.move_click(304, 406)  # 确认
            if cls.is_popup_show_info(wk, "侠义值不足", timeout=200):
                wk.record("侠义值不足")
                msleep(400)
                wk.move_click(426, 343)  # 确定
                break
            if cls.is_talk_show_info(wk, "侠义值不够"):
                wk.record("侠义值不够")
                break
            cls.close_other_talk(wk)
            wk.move_click(396, 344)  # 确定兑换
            wk.record(f"已兑换{i+1}次")
            msleep(400)
        msleep(600)
        cls.click_cancel(wk, RECT=RECT_FULL)
        cls.close_pages(wk)
