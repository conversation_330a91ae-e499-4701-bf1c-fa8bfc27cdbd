import ctypes
import ctypes.wintypes
import re
import sys
import time
import functools
import random
import psutil
import win32process
import traceback
from datetime import datetime, timezone, timedelta

from PySide2.QtCore import QThread
import settings


# 随机数
def rnd(min: int, max: int):
    return random.randint(min, max)


# 随机返回序列中的一个元素
def rnd_choice(seq: list):
    return random.choice(seq)


# 间隔多少秒数
def delta_sec(start_sec: int, end_sec: int):
    gap_sec = end_sec - start_sec
    return gap_sec


# 间隔多少分钟
def delta_minute(start_sec: int, end_sec: int):
    gap_sec = end_sec - start_sec
    gap_min = gap_sec // 60
    return gap_min


def rate(count):
    return int(count * settings.exec_rate)


# 线程阻塞毫秒
def msleep(min_ms: int, max_ms=None):
    if max_ms is None:
        t_ms = min_ms
    else:
        t_ms = rnd(min_ms, max_ms)
    t_ms = t_ms // (settings.exec_rate-0.1) or 1
    QThread.msleep(t_ms)


# 时间串 转 时间戳, "2020-12-26 00:59:30" -> 1608915570
def time_str_to_time_stamp(time_str: str):
    format_time = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    time_stamp = int(time.mktime(format_time))
    return time_stamp

# 时间戳 转 时间串, 1608915570 -> "2020-12-26 00:59:30"
def time_stamp_to_time_str(time_stamp: int):
    format_time = time.localtime(time_stamp)
    time_str = time.strftime("%Y-%m-%d %H:%M:%S", format_time)
    return time_str


# 自动锁定解锁
def MutexLocker(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        wk = args[1]
        result = None
        wk.mutex.lock()
        try:
            result = func(*args, **kwargs)
        except Exception as e:
            print(f"异常:{e}")
            traceback.print_exc()
        wk.mutex.unlock()
        return result
    return wrapper


# 轮转列表
def rotate_list(lst: list, n: int):
    n = n % len(lst)
    return lst[n:] + lst[:n]


def shuffle_list(original_list: list):
    shuffled_list = original_list[:]  # 创建副本
    random.shuffle(shuffled_list)
    return shuffled_list

# 计算全屏中心点的坐标
def get_centered_region(screen_width, screen_height, target_width, target_height):
    screen_center_x = screen_width // 2
    screen_center_y = screen_height // 2
    # 计算左上角和右下角的坐标
    region_top_left_x = max(0, screen_center_x - (target_width // 2))
    region_top_left_y = max(0, screen_center_y - (target_height // 2))
    region_bottom_right_x = min(screen_width, screen_center_x + (target_width // 2))
    region_bottom_right_y = min(screen_height, screen_center_y + (target_height // 2))

    wnd_title_height = 12

    return (
        region_top_left_x,
        region_top_left_y + wnd_title_height,
        region_bottom_right_x,
        region_bottom_right_y + wnd_title_height,
    )


def color_in_range(color1: str, color2: str, threshold: int):
    if not color1 or not color2:
        return False
    # color1类型为str, 例如"ffffff"， color2类型为str, 例如"ffffff"， threshold类型为int, 例如10
    color1 = int(color1, 16)
    color2 = int(color2, 16)
    red1 = color1 >> 16
    green1 = (color1 >> 8) & 0xFF
    blue1 = color1 & 0xFF
    red2 = color2 >> 16
    green2 = (color2 >> 8) & 0xFF
    blue2 = color2 & 0xFF
    return (
        abs(red1 - red2) <= threshold
        and abs(green1 - green2) <= threshold
        and abs(blue1 - blue2) <= threshold
    )


def starts_with_digit(s):
    pattern = r"^\d"
    return re.match(pattern, s) is not None


def get_start_digit(s):
    pattern = r"^\d+"
    match = re.match(pattern, s)
    if match:
        match_group = match.group()
        return int(match_group)
    return -1

def find_windows_by_class(wnd_class):
    # 定义回调函数来处理窗口句柄
    def enum_windows_callback(hwnd, lParam):
        class_name = ctypes.create_unicode_buffer(256)
        ctypes.windll.user32.GetClassNameW(hwnd, class_name, ctypes.sizeof(class_name))
        if class_name.value == wnd_class:
            hwnd_list.append(hwnd)
        return True
    hwnd_list = []
    # 调用EnumWindows函数枚举窗口
    ctypes.windll.user32.EnumWindows(
        ctypes.WINFUNCTYPE(ctypes.wintypes.BOOL, ctypes.wintypes.HWND, ctypes.wintypes.LPARAM)(enum_windows_callback),
        0
    )
    # 对窗口句柄列表按照创建时间顺序排序
    hwnd_list.sort(key=lambda hwnd: get_window_create_time(hwnd))
    return hwnd_list

def get_latest_window_by_class(wnd_class):
    hwnd_list = find_windows_by_class(wnd_class)
    if hwnd_list == []:
        return None
    return hwnd_list[-1]

def find_windows_by_name(window_name):
    def enum_windows_callback(hwnd, lParam):
        name = ctypes.create_unicode_buffer(256)
        ctypes.windll.user32.GetWindowTextW(hwnd, name, ctypes.sizeof(name))
        if isinstance(name.value, str) and name.value.startswith(window_name):
            hwnd_list.append(hwnd)
        return True

    hwnd_list = []
    ctypes.windll.user32.EnumWindows(
        ctypes.WINFUNCTYPE(ctypes.wintypes.BOOL, ctypes.wintypes.HWND, ctypes.wintypes.LPARAM)(enum_windows_callback),
        0
    )
    hwnd_list.sort(key=lambda hwnd: get_window_create_time(hwnd))
    return hwnd_list

def get_latest_window_by_name(window_name):
    hwnd_list = find_windows_by_name(window_name)
    if hwnd_list == []:
        return None
    return hwnd_list[-1]

def get_window_create_time(hwnd):
    try:
        process_id = win32process.GetWindowThreadProcessId(hwnd)[1]
        process = psutil.Process(process_id)
        create_time = process.create_time()
        return create_time
    except Exception as e:
        print(f"获取创建时间失败：{e}")
        return 0
    

def get_resp_json(resp):
    try:
        resp_json = resp.json()
    except:
        resp_json = {}
    return resp_json


def loop_until(func, timeout_ms=20000):
    sleep_gap = 300
    loop_count = int(timeout_ms//sleep_gap)
    for i in range(loop_count):
        if func():
            return True
        time.sleep(sleep_gap/1000)
    return False

def loop_until_res(func, timeout_ms=20000):
    sleep_gap = 300
    loop_count = int(timeout_ms//sleep_gap)
    for i in range(loop_count):
        res = func()
        if res:
            return res
        time.sleep(sleep_gap / 1000)
    return None

def contains_chinese(s):
    return bool(re.search('[\u4e00-\u9fff]', s))

def all_chinese(s):
    return all(re.search('[\u4e00-\u9fff]', c) for c in s)

def isadmin():
    return ctypes.windll.shell32.IsUserAnAdmin()

def get_screen_scaling_factor():
    # Get the handle of the primary monitor
    hdc = ctypes.windll.user32.GetDC(0)
    # Get the screen DPI
    dpi = ctypes.windll.gdi32.GetDeviceCaps(hdc, 88)  # 88 is the index for LOGPIXELSX
    # Release the handle
    ctypes.windll.user32.ReleaseDC(0, hdc)
    # Calculate the scaling factor
    scaling_factor = dpi / 96  # 96 DPI is the standard scaling factor (100%)
    return int(scaling_factor * 100)

def validate_card_number(card_number):
    if not card_number:
        return False
    pattern = r'^[A-Z0-9]{32}$'
    if re.match(pattern, card_number):
        return True
    else:
        return False
    
def is_win11():
    windows_version = sys.getwindowsversion()
    build = windows_version.build
    if build >= 22000:
        return True
    else:
        return False
    

def get_week_day():
    # 设置UTC+8时区
    utc_plus_8 = timezone(timedelta(hours=8))
    # 获取当前UTC+8时间
    current_time = datetime.now(utc_plus_8)
    # 获取星期几（0-6，0表示周一，6表示周日）
    weekday_num = current_time.weekday()
    return weekday_num + 1
