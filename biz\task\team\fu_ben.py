from biz.task.__base import TaskBase
from biz.task.single.qing_li import TaskQingLi
from biz.constants.constants import *
from utils import *
import settings
from biz.obj.worker import Worker


class TaskFuBen(TaskBase):
	TASK_NAME = "副本类"
	IS_TEAM_TASK = True
	IS_DIFFICULT_TASK = True
	IS_TASK_FIX_EQUIP_BB_ENABLE = True
	FU_BEN_NAME_LIST = []
	MAP_NPC = {}
	RECT_FUBEN_FIND = (3, 72, 826, 537)
	SEAL_MAIN_FIRST = False  # 随机封

	@classmethod
	def before_run(cls, wk: Worker):
		super().before_run(wk)
		wk.team.need_auth_colors = True
		
	@classmethod
	def after_run(cls, wk):
		cls.do_fix_bb(wk, force=True)
		super().after_run(wk)

	@classmethod
	def fuben_proc(cls, wk: Worker, fuben_name: str):
		if not cls.is_in_fu_ben(wk):
			wk.record(f"未能成功进入副本, 再次尝试进入副本{fuben_name}...")
			cls.enter_fuben(wk, fuben_name)
			if not cls.is_in_fu_ben(wk):
				wk.record(f"仍未能成功进入副本, 跳过副本{fuben_name}")
				return
		while True:
			if cls.cur_map(wk, sleep=False) == "开封":
				wk.record("可能是掉线出副本了, 本副本自动结束")
				break
			cls.talk_with_fuben_npc(wk)
			msleep(700)
			if wk.is_fight:
				cls.fight_operation(wk)
			if cls.click_map_recv_task(wk):
				msleep(800, 1400)
				cls.recv_new_task_in_place(wk)
				continue
			if wk.is_fight:
				cls.fight_operation(wk)
				continue
			# 打开任务界面，判断任务状态
			cls.open_task_page_quick(wk)
			# 判断是否到了出副本的那步, 能出直接出
			if cls.go_out_fu_ben(wk, timeout=600):
				cls.close_pages(wk)
				break
			# 防干扰
			if cls.switch_to_fuben_task(wk):
				wk.record("被其它任务干扰了，已自动切回")
				continue
			# 需要过图则过图, 否则点击npc
			if cls.handle_pass_map_in_fb(wk, timeout=600):
				break
			# 点击当前任务npc寻路
			try:
				npc_name = cls.get_target_npc_name(wk)
				# print(f'npc_name2:{npc_name}')
				if cls.click_task_npc(wk, npc_name):  # 这里有异常捕获
					wk.fail_count = 0
			except:  # 说明找到任务成功了，这时要接新任务
				if cls.recv_new_task_in_place(wk):
					wk.fail_count = 0

	@classmethod
	def handle_pass_map_in_fb(cls, wk: Worker, timeout=0):
		# 返回True代表从副本出去了, 否则只是普通的过图
		cur_map_name = cls.cur_map(wk, sleep=False)
		if cur_map_name == "":
			return
		if cls.is_cur_task_desc(wk, timeout=timeout):
			target_map = cls.get_target_map(wk) 
			if target_map != "" and target_map != cur_map_name and all_chinese(target_map):
				if not cls.cross_map(wk, target_map):  # 这种情况一般是！和？被人挡了
					if cur_map_name in ["万安寺大厅", "九里桥"]:  # 有时做完就清空任务了
						wk.record("副本完成, 正在返回开封...")
						cls.cross_map(wk, "开封")
						return True  
				else:
					wk.record(f"过图成功:{cur_map_name} -> {target_map}")
					wk.fail_count = 0
					# 如果是过图成功的情况, 且目标地图是副本外, 说明副本完成了
					if not cls.recv_new_task_in_place(wk) and target_map in OUTSIDE_MAP_LIST:
						if cls.cur_map(wk) in FU_BEN_MAP_LIST:  # 有风险
							return cls.go_out_fu_ben(wk, force_out=True)
			return
		wk.fail_count += 1
		wk.record(f"可能要卡点了, 失败次数:{wk.fail_count}")
		if wk.fail_count > 2:
			wk.fail_count = 0
			wk.record("失败次数过多, 直接出副本!")
			return cls.go_out_fu_ben(wk, force_out=True)
		if wk.fail_count > 1:
			cls.recv_new_task_in_place(wk)
		cls.refresh_task_list(wk)
		cls.close_pages(wk)

	@classmethod
	def is_big_map_open(cls, wk: Worker, timeout=0):
		if wk.sub_task == "万安寺":
			_, y = wk.get_pic_pos(*RECT_FULL, "寻路.bmp|寻路2.bmp")
			return y < 50
		return super().is_big_map_open(wk, timeout)

	@classmethod
	def is_difficult_task(cls, wk: Worker):
		SAFE_FUBEN = ["", "九里桥", "万安寺", "70", "80", "90", "活死人墓"]
		if wk.cur_task == '队员挂机':
			if wk.team.sub_task in SAFE_FUBEN:
				return False
		else:
			if wk.sub_task in SAFE_FUBEN:
				return False
		return cls.IS_DIFFICULT_TASK

	@classmethod
	def is_in_fu_ben(cls, wk: Worker):
		return cls.cur_map(wk) in cls.MAP_NPC.keys()

	@classmethod
	def fight_do_something(cls, wk: Worker):
		cls.fight_check_pf(wk)
		wk.find_pic_click(*RECT_SKIP_FIGHT, "跳过战斗.bmp")

	@classmethod
	def enter_fuben(cls, wk: Worker, fuben_name: str):
		if cls.is_in_fu_ben(wk):
			return
		wk.record("正在进入副本...")
		cls.fu_ben_dan_shua_invite(wk)
		if fuben_name not in ["70", "80", "90", "万安寺"]:
			cls.use_xmx(wk)
		cls.wait_mate_return_team(wk)
		cls.go_to_fu_ben_npc_enter(wk, fuben_name)
		msleep(1200)
		if cls.is_talk_show_info(wk, "还是明天再来吧|等级不足", COLOR_BLACK):
			wk.record("进入副本失败，等级不足或者次数已用完")
			cls.close_other_talk(wk)
			return
		if cls.click_confirm(wk, timeout=1000):
			wk.record("即将进入副本...")
			for i in range(10):
				wk.re_move()
				msleep(1000)
				if cls.is_in_fu_ben(wk):
					wk.record("进入副本成功")
					return
		wk.record("进入副本失败")

	@classmethod
	def go_to_fu_ben_npc_enter(cls, wk: Worker, fuben_name: str):
		raise NotImplementedError

	@classmethod
	def go_out_fu_ben(cls, wk: Worker, force_out=False, timeout=0):
		cur_desc = cls.is_cur_task_desc(wk, timeout=timeout)
		if not force_out:
			if not cur_desc:
				return False
			if cls.region_task_status_get_status(wk) != "成功":
				return False
		# 出地图
		out_map = ""
		from_map = cls.cur_map(wk)
		if from_map == "":
			return False
		if from_map in OUTSIDE_MAP_LIST:
			return True
		if from_map in ["仙人谷", "隐士洞"]:
			if cls.region_task_desc_find_str(wk, "带着神功秘籍去见白眉") or force_out:
				out_map = "开封"
		elif from_map in ["华山山脚", "华山山间", "华山山顶"]:
			if cls.region_task_desc_find_str(wk, "平息武林危机") or force_out:
				out_map = "开封"
		elif from_map == "木行阵":
			if cls.region_task_desc_find_str(wk, "杀死木行守阵人后") or force_out:
				out_map = "碧峰峡"
		elif from_map in ["万安寺二层", "万安寺大厅", "万安寺八层"]:
			if cls.region_task_desc_find_str(wk, "消灭掉王保保后") or force_out:
				out_map = "开封"
				if from_map == "万安寺八层":
					cls.cross_map(wk, "万安寺大厅")
		elif from_map == "九里桥":
			if cls.region_task_desc_find_str(wk, "刑捕头此案的前因后果") or force_out:
				out_map = "开封"

		if out_map:
			cls.fu_ben_dan_shua_leave(wk)
			cls.cross_map(wk, out_map)
			return cls.cur_map(wk) in OUTSIDE_MAP_LIST
		return False

	@classmethod
	def fu_ben_dan_shua_invite(cls, wk: Worker):
		for i in range(60):
			cur_team_count = cls.get_teammate_count(wk, count_pause_leave=False)
			if cur_team_count >= 3:
				break
			if i % 5 == 0:
				wk.record(f"队伍人数{cur_team_count}<3人, 自动进入凑数单刷模式, 正在邀请凑数小号...")
			if cur_team_count == 0:
				cls.create_team(wk)
			cls.invite_small_account(wk)
			msleep(1000)

	@classmethod
	def invite_small_account(cls, wk: Worker):
		if not cls.open_small_account_page(wk):
			return
		idx_x_y_list = wk.find_pic_ex(
			*RECT_FULL, "好友男.bmp|好友女.bmp|好友男2.bmp|好友女2.bmp")
		for _, x, y in idx_x_y_list:
			wk.move_r_click(x+60, y, re_move=False)
			msleep(300)
			if not wk.find_str_click(*RECT_FULL, "邀请加队", COLOR_PLATFORM_MAP, timeout=300):
				break
			msleep(300)
		wk.record("邀请队友完毕")
		cls.close_pages(wk)

	@classmethod
	def fu_ben_dan_shua_leave(cls, wk: Worker):
		# 对于队员凑数 队长单刷 检查是否其它人都暂离了
		total_count = cls.get_teammate_count(wk)
		leave_count = cls.get_pause_leave_teammate_count(wk)
		if total_count > 1 and total_count == leave_count + 1:
			wk.record(
				f"副本凑数单刷模式, 队伍总人数: {total_count}, 暂离人数: {leave_count}, 队长退队")
			cls.leave_team(wk)

	@classmethod
	def switch_to_fuben_task(cls, wk: Worker):
		# 切换成功返回True, 不需要切换或切换失败返回False
		if not cls.is_cur_task_desc(wk):
			cls.refresh_task_list(wk)
			if cls.is_cur_task_desc(wk):
				return True
		return False

	@classmethod
	def is_cur_task_desc(cls, wk: Worker, timeout=0):
		target_map = cls.get_target_map(wk, timeout=timeout)
		if target_map == "":
			return True
		if not super().is_cur_task_desc(wk, timeout=timeout):
			return False
		return target_map in FU_BEN_MAP_LIST

	@classmethod
	def recv_new_task_in_place(cls, wk: Worker):
		wk.record("原地找NPC接任务")
		cls.close_pages(wk)
		cur_map_name = cls.cur_map(wk)
		npc_name_list = cls.MAP_NPC.get(cur_map_name, [])
		if not npc_name_list:
			wk.record(f"当前地图:{cur_map_name}没有副本npc")
			cls.close_other_talk(wk)
			return False
		color = "|".join([COLOR_CYAN, COLOR_GREEN, COLOR_GOLD])
		for npc_name in npc_name_list:
			x, y = wk.get_str_pos(
				*cls.RECT_FUBEN_FIND, npc_name, color, sim=0.9
			)
			if x < 0:
				continue
			new_x, new_y = x + 7*len(npc_name) + rnd(-2, 2), y + rnd(90, 100)
			wk.move_relative_click(
				new_x, new_y, limit_border=True, limit_y=Y_LIMIT_CLICK)
			wk.record(f"点击了npc: {npc_name}, 坐标: {new_x}, {new_y}")
			msleep(600)
			if cls.is_talk_open(wk, timeout=200):
				return True
		# 一直到最后都没点到的话，挪一下位置
		if rnd(0, 10) == 1:
			wk.key_press(VK_F12)  # 屏蔽玩家
			wk.move_click(CX+rnd(-200, 200), CY+rnd(-20, 120), re_move=False)
			wk.record("挪动位置防卡点")
			wk.right_click()
			msleep(600)
			cls.close_pages(wk)
			if cur_map_name == "少室山":
				wk.record("再次调整位置")
				cls.big_map_click(wk, 750, 183)
		return False

	@classmethod
	def get_target_map(cls, wk: Worker, timeout=0):
		"""
		要识别出目标场景是哪里？
		如果状态 是 进行中，就找“正隐藏在” 或者  “拜访”后面的红色 或者 “送到”后面的红色
		如果状态 是 回复，就找“回复”后面的红色字体
		"""
		if not cls.is_task_page_open(wk, timeout=timeout):
			wk.record("未能成功打开任务面板,目标场景识别为空")
			return ""
		target_map = ""
		if cls.region_task_status_get_status(wk) == "进行中":
			x1, y1 = cls.region_task_desc_get_str_pos(wk, "正隐藏在")
			if x1 > 0:
				x2, _ = cls.region_task_desc_get_str_pos(wk, "此人")
				target_map = wk.ocr(x1 + 55, y1 - 2, x2,
									y1 + 14, COLOR_TASK_NAME)
			if not target_map:  # 万安寺的地图是红色
				x1, y1 = cls.region_task_desc_get_str_pos(wk, "到杀个|当前应该在|正隐藏在")
				if x1 > 0:
					target_map = wk.ocr(
						x1, y1 - 2, x1 + 150, y1 + 14, COLOR_RED)
		else:  # 回复
			x1, y1 = cls.region_task_desc_get_str_pos(wk, "回复")
			if x1 > 0:
				target_map = wk.ocr(x1, y1 - 2, x1 + 150, y1 + 14, COLOR_RED)
		target_map = target_map or cls.region_task_desc_ocr(wk, COLOR_RED)
		return target_map.strip()
	
	@classmethod
	def get_target_npc_name(cls, wk: Worker):
		return cls.region_task_status_get_npc_name_pro(wk)

	@classmethod
	def fuben_task_npc_click(cls, wk: Worker, condition_func_dict={}):
		# 打开任务面板，寻路任务NPC
		cls.close_pages(wk)
		cls.open_task_page(wk)
		if not cls.is_task_page_open(wk):
			return False
		for condition_str, func in condition_func_dict.items():
			if cls.region_task_list_find_str(wk, condition_str, func):
				return func(wk)
		if cls.region_task_status_click_npc(wk):
			cls.close_pages(wk)
			wk.is_stuck = False
			return True
		cls.close_pages(wk)
		return False

	@classmethod
	def click_map_recv_task(cls, wk: Worker):
		# 找到感叹号返回True, 啥也没找到或问号返回False(说明要打开任务列表来看了)
		if cls.big_map_find_pic_click(wk, "问号.bmp"):
			wk.record("点击地图NPC交任务...")
			cls.close_big_map(wk)
			return False
		if cls.big_map_find_pic_click(wk, "感叹号1.bmp|感叹号2.bmp|感叹号3.bmp|感叹号4.bmp", delta_color="000000"):
			wk.record("点击地图NPC接任务...")
			cls.close_big_map(wk)
			return True
		cls.close_big_map(wk)
		return False

	@classmethod
	def click_task_npc(cls, wk: Worker, npc_name:str):
		for i in range(3):
			msleep(500)
			if wk.is_fight:
				cls.fight_operation(wk)
			if cls.is_talk_open(wk):
				return True
			# 优先直接点击地面上的NPC
			if not cls.is_page_open(wk):
				if npc_name and wk.find_str_offset_click(*RECT_FIND, npc_name, COLOR_GOLD + "|" + COLOR_CYAN, dx=rnd(14, 35),
											dy=rnd(60, 80), limit_border=True, limit_y=Y_LIMIT_CLICK) and rnd(0, 1):
					wk.record(f"直接点击npc, 名字:{npc_name}")
					continue
			if i == 0 or wk.is_stuck:
				cls.open_task_npc_click(wk)  # 任务成功会抛异常被上层捕获
		return False

	@classmethod
	def open_task_npc_click(cls, wk: Worker, condition_func_dict={}):
		if wk.sub_task == "万安寺":  # 五个号同时单刷可能相互影响, 不能读team
			cls.open_task_page(wk)
			if cls.region_task_desc_find_str(wk, "到杀个|寻找个|六层的阿大|六层的阿二|七层寻找赵敏"):
				return cls.handle_wan_an_si_kill_monster(wk)
		res = super().open_task_npc_click(wk, condition_func_dict)
		if not res:
			wk.record("点任务NPC失败, 原地接任务")
			cls.recv_new_task_in_place(wk)
		return res


	@classmethod
	def handle_wan_an_si_kill_monster(cls, wk: Worker):
		x, y = cls.region_task_desc_get_str_pos(wk, "到杀个")
		if x > 0:
			need_kill = wk.ocr(x, y - 2, x + 150, y + 14,
							   COLOR_GREEN, zk=ZK_DIGIT_11) or "0"
			have_kill = wk.ocr(x + 30, y + 14, x + 150, y +
							   35, COLOR_GREEN, zk=ZK_DIGIT_11) or "0"
			wk.record(f"杀怪数量：{have_kill}/{need_kill}")
			if int(have_kill) < int(need_kill):
				cls.close_pages(wk)
				cls.run_left_right(wk)
				return

		x, y = cls.region_task_desc_get_str_pos(wk, "寻找个")
		if x > 0 and cls.region_task_status_get_status(wk) == "进行中":
			cls.run_left_right(wk)
			return

		x, y = cls.region_task_desc_get_str_pos(wk, "六层的阿大|六层的阿二")
		if x > 0 and cls.region_task_status_get_status(wk) == "进行中":
			cls.run_left_right(wk)
			return

		x, y = cls.region_task_desc_get_str_pos(wk, "七层寻找赵敏")
		if x > 0 and cls.region_task_status_get_status(wk) == "进行中":
			cls.run_left_right(wk)
			return

		x, y = cls.region_task_desc_get_str_pos(wk, "回复")
		if x < 0:
			return
		target_map = wk.ocr(x, y - 2, x + 150, y + 14, COLOR_RED)
		if cls.cur_map(wk) != target_map:
			wk.record(f"杀怪完毕，准备前往：{target_map}")
			cls.cross_map(wk, target_map)
		else:
			return super().open_task_npc_click(wk)

	@classmethod
	def run_left_right(cls, wk: Worker):
		wk.record("跑动遇敌中...")
		cls.close_pages(wk)
		for i in range(100):
			if wk.is_fight:
				return
			wk.move_click(SMALL_MAP_CENTER_X-rnd(30, 40),
						  SMALL_MAP_CENTER_Y+rnd(-10, 10))
			msleep(600)
			if wk.is_fight:
				return
			wk.move_click(SMALL_MAP_CENTER_X+rnd(30, 40),
						  SMALL_MAP_CENTER_Y+rnd(-10, 10))
			msleep(600)

	@classmethod
	def close_qiyu_talk(cls, wk: Worker):
		talk_name = cls.get_talk_name(wk)
		if not talk_name:  # 没识别出名字说明是自己先说话，这种肯定是副本的
			return
		npc_name_list = cls.MAP_NPC.get(cls.cur_map(wk), [])
		if talk_name not in npc_name_list:  # 如果是江湖奇遇，直接关闭
			wk.record(f"江湖奇遇：{talk_name} 已关闭")
			cls.close_other_talk(wk)
			return

	@classmethod
	def talk_with_fuben_npc(cls, wk: Worker):
		cls.close_qiyu_talk(wk)
		if not cls.is_talk_open(wk):
			return
		cls.talk_click_first_item(wk, timeout=200)
		if cls.is_popup_show_info(wk, "物品栏已满"):
			cls.click_confirm(wk)
			wk.record("物品栏已满，正在丢弃物品...")
			wk.team.signal_drop_garbage.leader_set()
			cls.throw_baggage(wk, thow_equip=True)

	@classmethod
	def ready_overlap_auth(cls, wk: Worker):
		# 副本打不开组队平台, 偏色要大一点
		delta_color = "202020"
		if cls.open_clan_page(wk):  # 尝试打开战队
			delta_color = "101010"
		return delta_color

	@classmethod
	def adjust_color(cls, wk: Worker, pic_list_length: int, delta_color: str):
		if delta_color == "101010":
			return delta_color
		if pic_list_length > 1:
			return cls.sub_colors(delta_color)
		if pic_list_length < 1:
			return cls.add_colors(delta_color)
		return delta_color


class TaskFuBenPeoPle(TaskFuBen):
	TASK_NAME = "人物副本"
	FU_BEN_NAME_LIST = ["70", "80", "90", "华山剑派", "五行阵", "九里桥", "万安寺"]
	MAP_NPC = {
		# 九里桥
		"九里桥": ["刑捕头", "邢捕头", "闹事匪徒", "杜欣欢", "王胆小", "赏金杀手", "钱庄家丁", "钱庄伙计", "包照"],
		"开封郊外": ["钱掌柜", "铁牛", "小芳", "杜家亲戚", "小芳粉丝", "神偷联盟成员"],
		"赤松林": ["小乞丐", "小雨", "朱武", "杜子通", "丐帮小弟", "绿林好汉"],
		# 万安寺
		"万安寺大厅": ["张无忌"],
		"万安寺二层": ["赵一伤", "王保保"],
		"万安寺三层": ["班淑娴", "扎牙笃"],
		"万安寺四层": ["高长老"],
		"万安寺五层": ["常敬之"],
		"万安寺六层": ["空性", "阿大", "阿二", "阿三"],  # 阿三是明怪
		"万安寺七层": ["周芷若", "赵敏", "鹤笔翁"],
		"万安寺八层": ["宋青书", "鹿杖客"],
		"万安寺阁楼": ["宋远桥"],
		# 梦中大侠
		"仙人谷": ["白眉道人", "海沙帮主", "海沙弟子", "神农弟子", "跳崖的人"],
		"双娇岗": [
			"司马绝情",
			"江湖败类",
			"糊涂剑仙",
			"中书府武士",
			"杨志",
			"玉面娇娃",
			"霹雳娇娃",
		],
		"隐士洞": ["白发道人", "有志青年", "公孙无忧", "毒针少侠"],
		# 华山剑派
		"华山山顶": [
			"华山派掌门",
			"衡山派掌门",
			"恒山派掌门",
			"嵩山派掌门",
			"泰山派掌门",
		],
		"华山山间": ["许开山"],
		"华山山脚": ["独孤剑魔", "元大帅"],
		# 五行阵
		"木行阵": ["灰衣人", "木行守阵人", "狂怒的野兽"],
		"土行阵": ["土行守阵人", "武林败类"],
		"水行阵": ["水行守阵人", "受伤的小道", "狂暴的师兄"],
		"金行阵": ["金行守阵人", "神秘高手", "神秘人", "白衣侠士", "追杀之人"],
		"火行阵": ["火行守阵人"],
	}

	@classmethod
	def run(cls, wk: Worker):
		cls.on_ride(wk)
		for fuben_name in cls.FU_BEN_NAME_LIST:
			count = wk.cfg_plan_task[fuben_name]
			for i in range(count):
				wk.record(f"开始执行副本 {fuben_name}")
				start_ts = settings.cur_time_stamp
				wk.team.fuben_name = fuben_name
				wk.sub_task = fuben_name
				wk.team.sub_task = fuben_name
				cls.enter_fuben(wk, fuben_name)
				cls.fuben_proc(wk, fuben_name)
				wk.record(f"{fuben_name}副本完成{i+1}次")
				cls.on_ride(wk)
			if count > 0 and wk.cfg_plan["换副本前修理忠诚"] and settings.cur_time_stamp - start_ts > 2 * 60:
				cls.do_fix_bb(wk, force=True)

	@classmethod
	def get_default_biz_config(cls):
		return {
			"九里桥": 0,
			"万安寺": 0,
			"70": 0,
			"80": 0,
			"90": 1,
			"华山剑派": 0,
			"五行阵": 0,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件 -> 控件
		super().cfg_read(plan_name)
		settings.wnd_main.spin_count_fuben_jiu_li_qiao.setValue(cls.CONFIG["九里桥"])
		settings.wnd_main.spin_count_fuben_wan_an_si.setValue(cls.CONFIG["万安寺"])
		settings.wnd_main.spin_count_fuben_70_meng.setValue(cls.CONFIG["70"])
		settings.wnd_main.spin_count_fuben_80_meng.setValue(cls.CONFIG["80"])
		settings.wnd_main.spin_count_fuben_90_meng.setValue(cls.CONFIG["90"])
		settings.wnd_main.spin_count_fuben_100_hua.setValue(cls.CONFIG["华山剑派"])
		settings.wnd_main.spin_count_fuben_110_wu.setValue(cls.CONFIG["五行阵"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件 -> 文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["九里桥"] = settings.wnd_main.spin_count_fuben_jiu_li_qiao.value()
		cls.CONFIG["万安寺"] = settings.wnd_main.spin_count_fuben_wan_an_si.value()
		cls.CONFIG["70"] = settings.wnd_main.spin_count_fuben_70_meng.value()
		cls.CONFIG["80"] = settings.wnd_main.spin_count_fuben_80_meng.value()
		cls.CONFIG["90"] = settings.wnd_main.spin_count_fuben_90_meng.value()
		cls.CONFIG["华山剑派"] = settings.wnd_main.spin_count_fuben_100_hua.value()
		cls.CONFIG["五行阵"] = settings.wnd_main.spin_count_fuben_110_wu.value()
		super().cfg_save(plan_name)

	@classmethod
	def go_to_fu_ben_npc_enter(cls, wk: Worker, fuben_name):
		if fuben_name == "九里桥":
			cls.back_to_kai_feng(wk)
			cls.talk_with_cur_map_npc(wk, "冯知府", ["协助官府破案", "九里桥疑案", "开启新副本"])
			return
		if fuben_name in ["70", "80", "90"]:
			cls.back_to_kai_feng(wk)
			cls.talk_with_cur_map_npc(wk, "江湖万事通", ["梦中大侠", fuben_name, "开启新副本"])
			return
		if fuben_name == "万安寺":
			cls.back_to_kai_feng(wk)
			cls.find_way_npc(wk, "修行老者", ["万安寺", "开启新副本"])
			return
		# 华山剑派
		if fuben_name == "华山剑派":
			cls.back_to_kai_feng(wk)
			cls.find_way_npc(wk, "华山剑派使者", ["华山论剑", "开启新副本"])
			return
		# 五行阵
		if fuben_name == "五行阵":
			cls.run_to_map(wk, "碧峰峡")
			cls.find_way_npc(wk, "五行阵使", ["五行阵", "开启新副本"])
			return


class TaskFuBenBB(TaskFuBen):
	TASK_NAME = "随从副本"

	@classmethod
	def before_run(cls, wk: Worker):
		super().before_run(wk)
		wk.fu_ben_bb_name_list = []
		if wk.cfg_plan_task["90级随从副本开关"]:
			wk.fu_ben_bb_name_list.append(wk.cfg_plan_task["90随从副本名"])
		if wk.cfg_plan_task["100级随从副本开关"]:
			wk.fu_ben_bb_name_list.append(wk.cfg_plan_task["100随从副本名"])
		if wk.cfg_plan_task["110级随从副本开关"]:
			wk.fu_ben_bb_name_list.append(wk.cfg_plan_task["110随从副本名"])
		print(wk.fu_ben_bb_name_list)

	@classmethod
	def run(cls, wk: Worker):
		cls.on_ride(wk)
		for fuben_name in wk.fu_ben_bb_name_list:  # 所有的随从副本只能一天做一次
			wk.record(f"开始执行副本 {fuben_name}")
			wk.team.fuben_name = fuben_name
			wk.sub_task = fuben_name
			wk.team.sub_task = fuben_name
			start_ts = settings.cur_time_stamp
			cls.enter_fuben(wk, fuben_name)
			cls.fuben_proc(wk, fuben_name)
			wk.record(f"{fuben_name}副本已完成")
			for i in range(3):
				if cls.cur_map(wk) in FU_BEN_MAP_LIST:
					wk.record("检测到仍在副本, 强制退出副本!")
					cls.go_out_fu_ben(wk, force_out=True)
				else:
					break
				msleep(600)
			cls.on_ride(wk)
			if wk.cfg_plan["换副本前修理忠诚"] and settings.cur_time_stamp - start_ts > 2 * 60:
				cls.do_fix_bb(wk, force=True)

	@classmethod
	def after_run(cls, wk: Worker):
		super().after_run(wk)
		del wk.fu_ben_bb_name_list

	@classmethod
	def go_out_fu_ben(cls, wk: Worker, force_out=False, timeout=0):
		status = cls.region_task_status_get_status(wk, timeout=timeout)
		if not force_out and status != "成功":
			return False
		out_map = ""
		from_map = cls.cur_map(wk)
		if from_map == "":
			return False
		if from_map == "终南山脚":
			if cls.region_task_desc_find_str(wk, "墓前拜访孙婆婆") or force_out:
				out_map = "终南山"
		elif from_map in ["活死人墓一层", "活死人墓二层"]:
			if cls.region_task_desc_find_str(wk, "小龙女交出玉女心经再次迎战") or force_out:
				out_map = "终南山"
		elif from_map in ["倭寇岛", "海边渔村"]:
			if cls.region_task_desc_find_str(wk, "击败溃逃的倭寇大头领") or force_out:
				out_map = "桃花滩"
		elif from_map == "华山绝顶":
			if cls.region_task_desc_find_str(wk, "和杨过话别") or force_out:
				out_map = "龙门"
		elif from_map == "武当大殿":
			if cls.region_task_desc_find_str(wk, "询问倚天剑的来历") or force_out:
				out_map = "有座山"
		elif from_map == "少室山":
			if cls.region_task_desc_find_str(wk, "扫地神僧询问萧远山") or force_out:
				out_map = "玄霜浅滩"
		elif from_map in ["无量山脚", "无量剑派"]:
			if cls.region_task_desc_find_str(wk, "事情圆满解决") or force_out:
				out_map = "无量山"
		elif from_map in ["桃花居", "桃花迷阵"]:
			if cls.region_task_desc_find_str(wk, "和洪七公同时出手") or force_out:
				out_map = "桃花滩"
		elif from_map in ["梅庄水牢", "梅庄", "江南郊外"]:
			if cls.region_task_desc_find_str(wk, "是日月神教教主任我行") or force_out:
				out_map = "金风山道"

		if out_map:
			cls.fu_ben_dan_shua_leave(wk)
			return cls.cross_map(wk, out_map)
		return False

	@classmethod
	def get_default_biz_config(cls):
		return {
			"90级随从副本开关": True,
			"100级随从副本开关": False,
			"110级随从副本开关": False,
			"90随从副本名": "活死人墓",
			"100随从副本名": "决战华山",
			"110随从副本名": "无量剑派",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件 -> 控件
		super().cfg_read(plan_name)
		settings.wnd_main.groupBox_fuben_bb90.setChecked(
			cls.CONFIG["90级随从副本开关"])
		settings.wnd_main.groupBox_fuben_bb100.setChecked(
			cls.CONFIG["100级随从副本开关"]
		)
		settings.wnd_main.groupBox_fuben_bb110.setChecked(
			cls.CONFIG["110级随从副本开关"]
		)
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_fuben_bb90, cls.CONFIG["90随从副本名"]
		)
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_fuben_bb100, cls.CONFIG["100随从副本名"]
		)
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_fuben_bb110, cls.CONFIG["110随从副本名"]
		)

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件 -> 文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["90级随从副本开关"] = (
			settings.wnd_main.groupBox_fuben_bb90.isChecked()
		)
		cls.CONFIG["100级随从副本开关"] = (
			settings.wnd_main.groupBox_fuben_bb100.isChecked()
		)
		cls.CONFIG["110级随从副本开关"] = (
			settings.wnd_main.groupBox_fuben_bb110.isChecked()
		)
		cls.CONFIG["90随从副本名"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_fuben_bb90
		)
		cls.CONFIG["100随从副本名"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_fuben_bb100
		)
		cls.CONFIG["110随从副本名"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_fuben_bb110
		)
		super().cfg_save(plan_name)

	@classmethod
	def go_to_fu_ben_npc_enter(cls, wk: Worker, fuben_name):
		cls.use_xmx(wk)
		if fuben_name == "活死人墓":
			cls.run_to_map(wk, "终南山")
			cls.find_way_npc(wk, "峨嵋小师妹", ["活死人墓", "开启新副本"])
		elif fuben_name == "智破倭寇":
			cls.run_to_map(wk, "桃花滩")
			cls.find_way_npc(wk, "渔翁", ["智破倭寇", "开启新副本"])
		elif fuben_name == "终南破敌":
			cls.run_to_map(wk, "终南山")
			cls.find_way_npc(wk, "郭靖", ["终南山破敌", "开启新副本"])
		elif fuben_name == "决战华山":
			cls.run_to_map(wk, "龙门")
			cls.find_way_npc(wk, "金湘玉", ["决战华山", "开启新副本"])
		elif fuben_name == "大战武当":
			cls.run_to_map(wk, "有座山")
			cls.find_way_npc(wk, "俞莲舟", ["大战武当山", "开启新副本"])
		elif fuben_name == "武林大会":
			cls.run_to_map(wk, "玄霜浅滩")
			cls.find_way_npc(wk, "丐帮三袋弟子", ["武林大会", "开启新副本"])
		elif fuben_name == "无量剑派":
			cls.run_to_map(wk, "无量山")
			cls.find_way_npc(wk, "玄慧师太", ["无量剑派", "开启新副本"])
		elif fuben_name == "桃花招亲":
			cls.run_to_map(wk, "桃花滩")
			cls.find_way_npc(wk, "姜泰功", ["桃花招亲", "开启新副本"])
		elif fuben_name == "梅庄之战":
			cls.run_to_map(wk, "金风山道")
			cls.find_way_npc(wk, "王小石", ["梅庄之战", "开启新副本"])

	FU_BEN_NAME_LIST = [
		"活死人墓",
		"智破倭寇",
		"终南破敌",
		"决战华山",
		"大战武当",
		"武林大会",
		"无量剑派",
		"桃花招亲",
		"梅庄之战",
	]
	MAP_NPC = {
		# 活死人墓
		"活死人墓一层": [
			"洪凌波",
			"古墓弟子",
			"全真教教徒",
			"全真教弟子",
			"丘处机",
			"李莫愁",
		],
		"活死人墓二层": ["洪凌波", "尹志平", "赵志敬", "小龙女", "杨过", "李莫愁"],
		# 终南破敌
		"终南山脚": ["郭靖", "蒙古士军", "杨过", "全真教弟子", "孙婆婆", "郝大通"],
		"全真教": ["丘处机", "郭靖", "蒙军士军", "全真教弟子", "霍都", "达尔巴", "金轮法王", "杨过", "郝大通"],
		# 智破倭寇
		"海边渔村": ["老渔翁", "恶霸", "商贩", "倭寇头目"],
		"倭寇岛": [
			"逃跑的渔民",
			"戚继光",
			"倭寇看守",
			"倭寇队长",
			"倭寇卫士",
			"倭寇守卫",
			"倭寇二头领",
			"倭寇三头领",
			"倭寇大头领",
		],
		# 决战华山
		"华山绝顶": [
			"杨过",
			"洪七公",
			"欧阳锋",
			"丐帮弟子",
			"蒙古军",
			"蒙古士兵",
			"藏边四丑",
			"青狼",
		],
		# 大战武当
		"武当大殿": [
			"张三丰",
			"张无忌",
			"俞岱岩",
			"赵敏",
			"阿大",
			"阿二",
			"阿三",
			"鹤笔翁",
			"鹿杖客",
		],
		"后山": [
			"张三丰",
			"武当弟子",
			"少林弟子",
		],
		# 武林大会
		"少室山": [
			"慕容复",
			"小沙弥",
			"星宿派大弟子",
			"玄慈方丈",
			"萧峰",
			"丁春秋",
			"段誉",
			"段延庆",
			"萧远山",
			"扫地僧",
			"星宿派弟子",
		],
		"少林寺后山": [
			"玄慈方丈",
			"鸠摩智",
			"少林弟子",
		],
		# 无量剑派
		"无量山脚": [
			"书生",
			"无量弟子",
			"无量高手",
			"无量派弟子",
			"樵夫"
		],
		"无量剑派": [
			"左子穆",
			"辛双清",
			"西宗高手",
			"西宗护法",
			"西宗长老",
			"书生",
		],
		# 桃花招亲
		"桃花迷阵": [
			"周伯通",
			"洪七公",
			"郭靖",
			"巨蟒",
		],
		"桃花居": [
			"欧阳锋",
			"黄药师",
			"洪七公",
			"郭靖",
			"欧阳克"
		],
		# 梅庄之战
		"江南郊外": {
			"向问天",
			"日月神教教徒",
			"童百熊",
			"官兵",
		},
		"梅庄": {
			"梅庄家丁",
			"偷酒贼",
			"丹青生",
			"黑白子",
			"黄钟公",
			"秃笔翁",
		},
		"梅庄水牢": {
			"任我行",
			"向问天",
		}
	}


class TaskFuBenCouShu(TaskBase):
	TASK_NAME = "副本凑数"
	IS_TEAM_TASK = True

	@classmethod
	def run(cls, wk: Worker):
		first_in, first_out = True, True
		while True:
			msleep(800)
			if wk.is_fight:
				cls.fight_operation(wk)
			cls.close_other_talk(wk)
			cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP)  # 同意入队
			if cls.cur_map(wk) in FU_BEN_MAP_LIST:  # 在副本则暂离
				if first_in or rnd(0, 20) == 1:
					first_in = False
					wk.record("在副本中, 暂离等待...")
				# 无队伍 则 离开副本
				if cls.get_teammate_count(wk) <= 1:
					cls.leave_team(wk)
					wk.mate_check_leave = True
					if cls.go_out_fu_ben(wk):
						wk.record("离开副本成功")
					else:
						wk.record("离开副本失败")
					continue
				total_count = cls.get_teammate_count(wk)
				leave_count = cls.get_pause_leave_teammate_count(wk)
				if total_count > 1 and total_count != leave_count + 1:
					# 有队伍 打开队伍页面
					cls.open_team_page(wk)
					# 检查自己是否暂离了
					if not cls.is_self_pause_leave(wk, is_close=False):
						if wk.find_pic_click(*RECT_FULL, "暂时离队.bmp|暂时离队2.bmp"):
							wk.record("暂离队伍成功")
			else:  # 不在副本, 且有队伍时归队
				if first_out or rnd(0, 20) == 1:
					first_out = False
					wk.record("在副本外, 归队等待...")
				cls.back_to_team(wk)

	@classmethod
	def go_out_fu_ben(cls, wk: Worker, force_out=False, timeout=0):
		from_map = cls.cur_map(wk)
		wk.record(f"当前地图: {from_map}")
		if from_map in ["仙人谷", "隐士洞"]:
			cls.cross_map(wk, "开封")
			return True
		if from_map in ["华山山脚", "华山山间", "华山山顶"]:
			cls.cross_map(wk, "开封")
			return True
		if from_map == "木行阵":
			cls.cross_map(wk, "碧峰峡")
			return True
		if from_map in ["万安寺二层", "万安寺大厅"]:
			cls.cross_map(wk, "开封")
			return True
		if from_map == "终南山脚":
			cls.cross_map(wk, "终南山")
			return True
		if from_map in ["活死人墓一层", "活死人墓二层"]:
			cls.cross_map(wk, "终南山")
			wk.move_click(CX, CY+40)
			return True
		if from_map in ["倭寇岛", "海边渔村"]:
			cls.cross_map(wk, "桃花滩")
			return True
		if from_map == "华山绝顶":
			cls.cross_map(wk, "龙门")
			return True
		if from_map == "武当大殿":
			cls.cross_map(wk, "有座山")
			return True
		if from_map == "少室山":
			cls.cross_map(wk, "玄霜浅滩")
			return True
		if from_map in ["无量山脚", "无量剑派"]:
			cls.cross_map(wk, "无量山")
			return True
		if from_map in ["桃花居", "桃花迷阵"]:
			cls.cross_map(wk, "桃花滩")
			return True
		if from_map in ["梅庄水牢", "梅庄", "江南郊外"]:
			cls.cross_map(wk, "金风山道")
			return True
		if from_map == "九里桥":
			cls.cross_map(wk, "开封")
			return True
		return False

	@classmethod
	def ready_overlap_auth(cls, wk: Worker):
		return TaskFuBen.ready_overlap_auth(wk)

	@classmethod
	def adjust_color(cls, wk: Worker, pic_list_length: int, delta_color: str):
		return TaskFuBen.adjust_color(wk, pic_list_length, delta_color)
