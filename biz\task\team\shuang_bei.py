from biz.constants.constants import *
from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskShuangBeiLingQu(TaskBase):
    TASK_NAME = "领取双倍"
    IS_TEAM_TASK = True
    IS_FREE = True

    @classmethod
    def run(cls, wk: Worker):
        if cls.cur_map(wk) in WILD_MAP_LIST:
            cls.on_ride(wk)
            cls.use_xmx(wk)
        db_time = wk.cfg_plan_task["领双时间"]
        cls.get_double(wk, db_time)

    @classmethod
    def get_default_biz_config(cls):
        return {
            "领双时间": "2小时",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_double_time.setCurrentText(cls.CONFIG["领双时间"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["领双时间"] = settings.wnd_main.cmb_double_time.currentText()
        super().cfg_save(plan_name)


class TaskShuangBeiBangHui(TaskBase):
    TASK_NAME = "帮会双倍"
    IS_FREE = True
    IS_TEAM_TASK = True

    @classmethod
    def run(cls, wk: Worker):
        cls.back_to_kai_feng(wk)
        cls.find_way_npc(wk, "帮会管理员", ["领取帮会双倍"])

