from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker

class TaskTangZhu(TaskBase):
    TASK_NAME = "挑战堂主"
    IS_DIFFICULT_TASK = True
    IS_TASK_FIX_EQUIP_BB_ENABLE = True
    
    @classmethod
    def before_run(cls, wk: Worker):
        # 重置任务类型和完成次数
        cls.set_task_config(wk)
        wk.done_count = 0
        cls.click_confirm(wk, timeout=0)
        cls.close_pages(wk)
        wk.is_stuck = True
        wk.record("开始执行")

    @classmethod
    def run(cls, wk: Worker):
        cls.leave_team(wk)
        if not cls.enter_wu_ling_meng(wk):
            return False
        color = COLOR_GREEN + "|" + COLOR_CYAN
        wk.record(f"进入武林盟成功")
        
        for i in range(100):
            npc_name = wk.cfg_plan_task["堂主"]
            total_count = wk.cfg_plan_task["次数"]
            if cls.is_talk_open(wk):
                if cls.talk_click_items(wk, ["我要挑战", "挑战"], close_talk=False):
                    msleep(800)
                if cls.is_talk_show_info(wk, "没有堂主挑战令"):
                    wk.record("没有堂主挑战令了")
                    cls.close_other_talk(wk)
                    break
            if cls.is_system_broad_show(wk, "挑战次数已经用尽"):
                wk.record("挑战次数已经用尽")
                break
            if wk.is_fight:
                cls.fight_operation(wk)
                if cls.do_fix_bb(wk):
                    cls.enter_wu_ling_meng(wk)
                wk.done_count += 1
                wk.record(f"已完成 {wk.done_count}/{total_count}")
            if wk.done_count >= total_count:
                break
            wk.record(f"挑战 {npc_name} 堂主...")
            x, y = wk.get_str_pos(*RECT_FULL, npc_name, color, timeout=800)
            if x < 0:
                if wk.is_fight:
                    continue
                if cls.cur_map(wk) != "武林盟":
                    wk.record("不在武林盟内")
                    break
                wk.record("正在复位...")
                cls.reset_pos(wk)
                continue
            wk.move_relative_click(x+rnd(33,35), y+rnd(80, 90))
            msleep(600)
        wk.record("挑战完成, 返回开封")
        cls.run_to_map(wk)
        
    @classmethod
    def enter_wu_ling_meng(cls, wk: Worker):
        if wk.is_fight:
            cls.fight_operation(wk)
            if cls.cur_map(wk) == "武林盟":
                wk.done_count += 1
                total_count = wk.cfg_plan_task["次数"]
                wk.record(f"已完成 {wk.done_count}/{total_count}")
                return True
        return cls.go_to_wu_ling_meng(wk)
    
    @classmethod
    def reset_pos(cls, wk: Worker):
        color = COLOR_GREEN + "|" + COLOR_CYAN
        for i in range(10):
            if wk.find_str(758,212,838,275, "锦衣", color):
                wk.record("已归位")
                return True
            if i == 0 or wk.is_stuck:
                cls.big_map_click(wk, 639, 185)
            msleep(600)
        return False
        
    @classmethod
    def get_default_biz_config(cls):
        return {
            "次数": 70,
            "堂主": "药神",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_tang_zhu.setValue(cls.CONFIG["次数"])
        settings.wnd_main.cmb_tang_zhu.setCurrentText(cls.CONFIG["堂主"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["次数"] = settings.wnd_main.spin_count_tang_zhu.value()
        cls.CONFIG["堂主"] = settings.wnd_main.cmb_tang_zhu.currentText()
        super().cfg_save(plan_name)