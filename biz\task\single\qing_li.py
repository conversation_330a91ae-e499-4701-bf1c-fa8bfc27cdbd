from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskQingLi(TaskBase):
    TASK_NAME = "清理背包"
    IS_FREE = True

    @classmethod
    def run(cls, wk: Worker):
        if cls.cur_map(wk) in WILD_MAP_LIST:
            wk.record("在野外地图, 直接强制回城")
            wk.key_press(VK_F8)
        cls.leave_team(wk)
        cls.back_to_kai_feng(wk)
        if cls.cur_map(wk) != "开封":
            wk.record("回城失败, 无法完成清理任务")
            return
        cls.throw_things(wk)  # 丢东西
        cls.sell_things(wk)  # 卖东西
        cls.use_things(wk)  # 用东西
        cls.clean_temp_bag(wk)  # 清理临时背包
        cls.store_things(wk)  # 存东西
        cls.close_pages(wk)  # 关闭仓库页面
        cls.huilu_zhuangbei(wk)  # 回炉装备
        cls.sell_things(wk)  # 再卖东西
        if wk.cfg_plan_task["皓石喂晶魄"]:
            cls.hao_shi(wk)
        # 最后再整理一下背包
        if wk.cfg_plan["点击整理"]:
            cls.open_bag_page(wk)
            cls.click_tidy_up(wk, timeout=300)
        cls.close_pages(wk)

    @classmethod
    def clean_temp_bag(cls, wk: Worker):
        # 清理成功或者不用清理 返回True, 失败返回False
        if not cls.is_bag_full(wk):
            return True
        if not wk.find_pic_click(*RECT_FULL, "包满.bmp"):
            return True
        wk.record("临时背包满了，开始清理...")
        msleep(400)
        if wk.find_pic_click(*RECT_FULL, "全部领取.bmp", timeout=400):
            wk.record("临时背包已经全部领取")
        msleep(600)
        if cls.click_confirm(wk):
            wk.record("临时背包全部领取失败, 包满了, 直接清空")
            wk.find_pic_click(*RECT_FULL, "清空.bmp", timeout=400)
            return False
        cls.close_pages(wk)
        wk.record("临时背包清理成功")
        return True

    @classmethod
    def use_things(cls, wk: Worker):
        wk.record("开始用东西...")
        use_pic = wk.match_pic("用_*.bmp")
        if wk.cfg_plan_task.get("通宝袋") == "用":
            use_pic = use_pic + "|" + wk.match_pic("*通宝袋.bmp")
        cls.bag_use_item_all(wk, use_pic, count=20)
        wk.record("用东西完成")
        
    @classmethod
    def hao_shi(cls, wk: Worker):
        cls.open_bag_page(wk)
        if not cls.is_bag_page_open(wk):
            wk.record("使用皓石失败, 背包页未打开")
            return
        if not wk.find_multi_color_click(*RECT_FULL, MCOLOR_JINGPO, timeout=400):
            wk.record("使用皓石失败, 晶魄页未打开")
            return
        msleep(600)
        if not wk.find_multi_color_click(*RECT_FULL, MCOLOR_WEIYANG, timeout=400):
            wk.record("使用皓石失败, 未找到喂养按钮")
            return
        msleep(600)
        wk.move_click(*POS_WEIYANG)  # 喂养整组
        pic = wk.match_pic("皓石_*.bmp")
        for i in range(20):
            wk.record(f"正在喂养晶魄:第{i+1}次...")
            if not wk.find_pic_click(*RECT_RIGHT, pic, timeout=400):
                break
            msleep(200, 300)
            wk.move_click(*POS_WEIYANG_CONFIRM)  # 喂养整组
            msleep(200, 300)
        wk.record("喂养晶魄完成")
        cls.close_pages(wk)

    @classmethod
    def throw_things(cls, wk: Worker, additional_pics=""):
        if wk.cfg_plan_task["藏宝图"] == "丢":
            if additional_pics:
                additional_pics += "|"
            additional_pics += "九州重宝图.bmp"
        super().throw_things(wk, additional_pics)

    @classmethod
    def sell_things(cls, wk: Worker):
        wk.record("开始卖东西...")
        if not cls.open_shop_page(wk):
            return
        x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
        if x > 0:
            wk.move_click(x+240, y-375)
            time.sleep(0.6)
        x, y = POS_SHOP_SELL
        wk.move_r_click(x + 80, y)  # 取消可能误点的物品
        wk.move_click(*POS_SHOP_SELL)
        sell_pic = wk.match_pic("卖_*.bmp") + "|丢_黑铁宝盒.bmp|丢_白银宝盒.bmp|金针.bmp|考题卷.bmp|\
            古董_三彩骆驼.bmp|古董_刻花白玉杯.bmp|古董_玉蝉出牙环.bmp|古董_鎏金银铜马.bmp|小增寿丹.bmp|\
            跌打白药.bmp|金创药.bmp|生津弥灵膏.bmp|人物书籍.bmp|玲珑生肌膏.bmp|静意碎片.bmp|红宝石头盔男.bmp"
        if wk.cfg_plan_task.get("药材") == "卖":
            sell_pic = sell_pic + "|" + wk.match_pic("药材_*.bmp")
        if wk.cfg_plan_task.get("主材") == "卖":
            sell_pic = sell_pic + "|" + wk.match_pic("主材_*.bmp")
        if wk.cfg_plan_task.get("辅材") == "卖":
            sell_pic = sell_pic + "|" + wk.match_pic("辅材_*.bmp")
        if wk.cfg_plan_task.get("杂货") == "卖":
            sell_pic = sell_pic + "|" + wk.match_pic("杂货_*.bmp")
        if wk.cfg_plan_task.get("灵石") == "卖":
            sell_pic = sell_pic + "|" + wk.match_pic("*灵石.bmp")
        if wk.cfg_plan_task.get("驯马材料") == "卖":
            sell_pic = sell_pic + "|亚麻线.bmp|捆绳线.bmp"
        if wk.cfg_plan_task.get("桃子") == "卖":
            sell_pic = sell_pic + "|桃子.bmp"
        if wk.cfg_plan_task.get("技能书") == "卖":
            sell_pic = sell_pic + "|随从初书.bmp|随从中书.bmp"
        if wk.cfg_plan_task.get("低级元灵") == "卖":
            sell_pic = sell_pic + "|低级元灵.bmp"
        if wk.cfg_plan_task.get("副本装备") == "卖":
            sell_pic = sell_pic + "|" + wk.match_pic("副本装备_*.bmp")
        if not wk.cfg_plan_task.get("回炉装备"):  # 不回炉装备的话, 装备就卖掉
            sell_pic = sell_pic + "|" + wk.match_pic("装备_*.bmp")

        pics = wk.match_pic("*装备_*.bmp")
        pos_list = cls.get_bag_page_pos_list(wk, RECT_FULL, timeout=400)
        super().right_click_cur_page_things(wk, sell_pic)
        cls.sell_white_equip(wk, pics)
        for pos in pos_list:
            _, x, y = pos
            wk.move_click(x, y)  # 换页
            time.sleep(0.3)
            super().right_click_cur_page_things(wk, sell_pic)
            cls.sell_white_equip(wk, pics)

        cls.click_confirm(wk)  # 确认卖出
        time.sleep(0.8)
        cls.click_confirm(wk)
        cls.close_pages(wk)
        wk.record("卖东西完成")

    @classmethod
    def sell_white_equip(cls, wk: Worker, pics: str):
        if not wk.cfg_plan_task.get("回炉装备"):
            return
        zb_pos_list = wk.find_pic_ex(*RECT_RIGHT, pics, timeout=200)
        # print(f'zb_pos_list: {zb_pos_list}')
        for zb_pos in zb_pos_list:
            _, zb_x, zb_y = zb_pos
            wk.move_to(zb_x, zb_y)
            msleep(100)
            wk.move_relative(4, 4)
            msleep(200)
            x, y = wk.get_str_pos(*RECT_FULL, "察看", COLOR_CHAKAN, timeout=800)
            msleep(200)
            if x and wk.find_color(x-25, y+30, x, y+40, COLOR_WHITE):
                wk.record(f"发现白色装备, 出售")
                wk.move_r_click(zb_x, zb_y, re_move=False)
                continue
            # 识别装备等级
            level = 0
            x2, y2 = wk.get_str_pos(
                x-90, y+75, x+100, y+136, "等级", COLOR_WHITE, zk=ZK_ALL_9, timeout=400)
            if x2:
                level_str = wk.ocr(x2 + 5, y2 -3, x2 + 45, y2 + 20, COLOR_WHITE, zk=ZK_DIGIT_9)
                if level_str:
                    level = int(level_str)
            # 看装备类型: 武器衣服<80, 头盔鞋子腰带<90, 出售
            equip_type = wk.ocr(x-13, y+37, x+44, y+65, COLOR_OBJECT_DESC, zk=ZK_ALL_9)
            if equip_type and level:
                wk.record(f"装备类型:{equip_type}, 等级:{level}")
            if equip_type and level:
                if equip_type not in ["武器", "铠甲"] or (equip_type == "武器" and level < 80) or (equip_type == "铠甲" and level < 90):
                    wk.record(f'装备类型:{equip_type}, 等级:{level}, 不符合回炉标准, 出售')
                    wk.move_r_click(zb_x, zb_y, re_move=False)
                    continue



    @classmethod
    def do_huilu_equip(cls, wk: Worker, pics: str):
        try:
            len_pics = len(wk.find_pic_ex(*RECT_RIGHT, pics))
            i = 0
            success_count = 0
            while i < len_pics:
                if cls.select_huilu(wk, pics, i):
                    success_count += 1
                    wk.record(f"回炉成功{success_count}件装备")
                    continue
                msleep(200)
                i += 1
        except:
            pass

    @classmethod
    def select_huilu(cls, wk: Worker, pics: str, i: int):
        order = i % 4
        if not wk.find_pic_r_click(*RECT_RIGHT, pics, order, timeout=200):
            return False
        time.sleep(0.2)
        if cls.is_popup_show_info(wk, "二级密码|不能回炉", timeout=400):
            cls.click_confirm(wk, RECT=RECT_POPUP)
            print("该物品回炉失败, 继续下一个")
            return False
        wk.move_click(*POS_HUILU_CONFIRM, re_move=False)
        time.sleep(0.2)
        if cls.is_popup_show_info(wk, "最少需要有|还没放入装备", timeout=400):
            cls.click_confirm(wk, RECT=RECT_POPUP)
            raise Exception("结束")
        wk.move_click(*POS_HUILU_CONFIRM2, re_move=False)
        time.sleep(3)
        cls.click_confirm(wk, timeout=600, RECT=RECT_POPUP)
        return True

    @classmethod
    def huilu_zhuangbei(cls, wk: Worker):
        if not wk.cfg_plan_task["回炉装备"]:
            wk.record("回炉装备未开启, 跳过")
            return
        wk.record("开始回炉装备...")
        cls.open_quick_func_page(wk)
        if not wk.find_multi_color_click(*RECT_LEFT, MCOLOR_ZHUANG_BEI, timeout=400):
            wk.record('打开快捷功能页失败')
            cls.close_pages(wk)
            return
        wk.record("切换到快捷装备页")
        if not wk.find_str_click(*RECT_LEFT, "回炉", COLOR_BLACK, timeout=600):
            wk.record("打开回炉页失败")
            cls.close_pages(wk)
            return
        wk.record("切换到回炉页")
        msleep(200)
        wk.move_click(*POS_HUILU_ZHUANGBEI)
        msleep(400)
        pos_list = cls.get_bag_page_pos_list(wk, RECT_FULL, timeout=400)
        wk.record("开始回炉装备...")
        pics = "首饰_70项链.bmp|首饰_70玉佩.bmp|" + PIC_EQUIP
        cls.do_huilu_equip(wk, pics)
        for pos in pos_list:
            _, x, y = pos
            wk.move_click(x, y)  # 换页
            time.sleep(0.6)
            cls.do_huilu_equip(wk, pics)
        wk.record("回炉装备完成")
        wk.key_press(VK_ESC)

    @classmethod
    def right_click_thing_check_talk_info(cls, wk: Worker):
        # 通过检查返回True, 失败抛出异常
        if cls.is_talk_show_info(wk, "你的物品箱已经满了", timeout=200):
            raise Exception("物品箱已满")
        return True

    @classmethod
    def right_click_cur_page_things(cls, wk: Worker, pic: str, RECT=RECT_SELL_THING, MAX_COUNT=None, filter_fn=None):
        # 没找到没交 或者 交了没交满, 返回False, 交满了返回True
        if not wk.cfg_plan_task["卡格子存物"]:  # 不卡格子的话直接全交
            return super().right_click_cur_page_things(wk, pic, RECT=RECT, filter_fn=filter_fn)
        return super().right_click_cur_page_things_left_one(wk, pic, RECT=RECT, MAX_COUNT=MAX_COUNT)

    @classmethod
    def store_things(cls, wk: Worker):
        wk.record("开始存放物品...")
        if not cls.open_kezhan_page(wk):
            return
        wk.move_click(*POS_DEPOT_LIST[0])  # 先切到第一页
        store_pic = wk.match_pic("存_*.bmp") + "|" + wk.match_pic("晶体石*.bmp") + "|" + wk.match_pic("发光石*.bmp")
        store_pic = store_pic + "|药引1.bmp|药引2.bmp|药引3.bmp"
        print(f'灵石: {wk.cfg_plan_task["灵石"]}')
        if wk.cfg_plan_task["药材"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("药材_*.bmp")
        if wk.cfg_plan_task["主材"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("主材_*.bmp")
        if wk.cfg_plan_task["辅材"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("辅材_*.bmp")
        if wk.cfg_plan_task["杂货"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("杂货_*.bmp")
        if wk.cfg_plan_task["灵石"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("*灵石.bmp")
        if wk.cfg_plan_task["副本装备"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("副本装备_*.bmp")
        if wk.cfg_plan_task["通宝袋"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("*通宝袋.bmp")
        if wk.cfg_plan_task["低级元灵"] == "存":
            store_pic = store_pic + "|" + "低级元灵.bmp"
        if wk.cfg_plan_task["藏宝图"] == "存":
            store_pic = store_pic + "|" + "九州重宝图.bmp"
        if wk.cfg_plan_task["桃子"] == "存":
            store_pic = store_pic + "|桃子.bmp"
        if wk.cfg_plan_task["驯马材料"] == "存":
            store_pic = store_pic + "|亚麻线.bmp|捆绳线.bmp"
        if wk.cfg_plan_task["技能书"] == "存":
            store_pic = store_pic + "|" + wk.match_pic("随从*书.bmp")

        def store_cur_page_things(before_page_x=-1, before_page_y=-1):
            try:
                cls.right_click_cur_page_things(
                    wk, store_pic, RECT=RECT_STORE_THING, MAX_COUNT=20)
            except:
                cls.close_other_talk(wk)
                wk.record("包裹已满, 自动切到仓库存入")
                wk.move_click(*POS_DEPOT_LIST[3])  # 这时会自动切到行囊第一页
                if before_page_x != -1:
                    wk.record("还原到之前清理的背包页继续存入")
                    wk.move_click(before_page_x, before_page_y, re_move=False)
                msleep(600)
                # 再继续存入, 这里再异常就抛出去了
                cls.right_click_cur_page_things(
                    wk, store_pic, RECT=RECT_STORE_THING, MAX_COUNT=20)

        pos_list = cls.get_bag_page_pos_list(wk, RECT_FULL, timeout=300)
        before_page_x, before_page_y = -1, -1
        try:
            store_cur_page_things(before_page_x, before_page_y)
            for pos in pos_list:
                _, x, y = pos
                wk.move_click(x, y)  # 换页
                msleep(800)
                before_page_x, before_page_y = x, y
                store_cur_page_things(before_page_x, before_page_y)
        except:
            wk.record("包裹和仓库都存满了!")
        cls.click_tidy_up(wk)
        wk.record("物品存放完成")

    @classmethod
    def get_default_biz_config(cls):
        return {
            "卡格子存物": False,
            "回炉装备": False,
            "皓石喂晶魄": False,
            "药材": "存",
            "主材": "存",
            "辅材": "存",
            "杂货": "存",
            "灵石": "存",
            "副本装备": "留",
            "通宝袋": "用",
            "低级元灵": "留",
            "藏宝图": "存",
            "驯马材料": "存",
            "桃子": "存",
            "技能书": "存",
        }

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["药材"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_yaocai
        )
        cls.CONFIG["主材"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_zhucai
        )
        cls.CONFIG["辅材"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_fucai
        )
        cls.CONFIG["杂货"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_zahuo
        )
        cls.CONFIG["灵石"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_lingshi
        )
        cls.CONFIG["副本装备"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_fbzb
        )
        cls.CONFIG["通宝袋"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_tongbaodai
        )
        cls.CONFIG["低级元灵"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_yuanling
        )
        cls.CONFIG["藏宝图"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_cangbaotu
        )
        cls.CONFIG["驯马材料"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_xun_ma
        )
        cls.CONFIG["桃子"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_peach
        )
        cls.CONFIG["技能书"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_ji_neng_shu
        )
        cls.CONFIG["卡格子存物"] = settings.wnd_main.chk_depot_store_left_one.isChecked()
        cls.CONFIG["回炉装备"] = settings.wnd_main.chk_huilu_zb.isChecked()
        cls.CONFIG["皓石喂晶魄"] = settings.wnd_main.chk_haoshi.isChecked()
        super().cfg_save(plan_name)

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_depot_store_left_one.setChecked(
            cls.CONFIG["卡格子存物"]
        )
        settings.wnd_main.chk_huilu_zb.setChecked(
            cls.CONFIG["回炉装备"]
        )
        settings.wnd_main.chk_haoshi.setChecked(
            cls.CONFIG["皓石喂晶魄"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_yaocai, cls.CONFIG["药材"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_zhucai, cls.CONFIG["主材"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_fucai, cls.CONFIG["辅材"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_zahuo, cls.CONFIG["杂货"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_lingshi, cls.CONFIG["灵石"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_fbzb, cls.CONFIG["副本装备"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_tongbaodai, cls.CONFIG["通宝袋"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_yuanling, cls.CONFIG["低级元灵"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_cangbaotu, cls.CONFIG["藏宝图"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_xun_ma, cls.CONFIG["驯马材料"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_peach, cls.CONFIG["桃子"]
        )
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_qing_li_ji_neng_shu, cls.CONFIG["技能书"]
        )
