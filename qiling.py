import sys

from PySide2.QtWidgets import QApplication
from PySide2.QtCore import Qt
from PySide2.QtWidgets import QStyleFactory, QSystemTrayIcon

from biz.team.team import *
from biz.constants.constants import *
from const.const import *
from utils.com import create_com_obj, reg_com_to_system
import settings
from utils import *
from wnd_main_code import WndMain
from biz.obj.worker import Worker

def set_global_var():
    settings.global_wk = Worker(0, settings.ori_obj)
    settings.SCREEN_W, settings.SCREEN_H = get_main_screen_wh()
    settings.RECT_FULL_SCREEN = (0, 0, settings.SCREEN_W, settings.SCREEN_H)
    settings.RECT_SCREEN_CENTER_GAME = utils.get_centered_region(
        settings.SCREEN_W, settings.SCREEN_H, W_GAME, H_GAME
    )
    settings.team_list = [
        Team(idx) for idx in range(TBE_CONSOLE_ROW // 5)
    ]
    settings.machine_code = get_machine_code()
    settings.user_info = {
        "os_type": str(settings.global_wk.get_os_type()),
        "screen_depth": str(settings.global_wk.get_screen_depth()),
        "display_info": settings.global_wk.get_display_info(),
    }

CHECKED = False

def check_process_vm_od():
    Thread(target=check_process_vm_od2, daemon=True).start()
    
    
def check_process_vm_od2():
    global CHECKED
    # 检测进程名
    Thread(target=check_process_name, daemon=True).start()
    # 检测虚拟机
    Thread(target=check_vm, daemon=True).start()
    # 检测调试器
    Thread(target=check_debug, daemon=True).start()
    CHECKED = True
    
    
def check_process_name():
    # 检测自己的名称
    proc_name = get_current_process_name()
    # print(f"current process name: {proc_name}")
    if proc_name != "python.exe" and not proc_name.endswith(".dll"):  # 不能直接启动, 要通过launcher
        Thread(target=settings.wnd_main.sig_close.emit, daemon=True).start()
    # 检测父进程名称
    p_proc_name = get_parent_process_name()
    # print(f"parent process name: {p_proc_name}")
    if p_proc_name == "explorer.exe":  # 不能直接启动, 要通过launcher
        Thread(target=settings.wnd_main.sig_close.emit, daemon=True).start()
        
def check_vm():
    if anti_vm():
        Thread(target=settings.wnd_main.sig_close.emit, daemon=True).start()

def check_debug():
    ret2 = anti_debug1()
    ret3 = anti_debug2()
    ret4 = anti_debug3()
    ret5 = anti_anti_debug4()
    if any([ret2, ret3, ret4, ret5]):
        Thread(target=settings.wnd_main.sig_close.emit, daemon=True).start()
        
def check_flag():
    global CHECKED
    if not CHECKED:
        Thread(target=settings.wnd_main.sig_close.emit, daemon=True).start()
    # 检测下本进程是否有多个, 防止多开
    proc_name = get_current_process_name()
    if proc_name.endswith(".dll"):
        if count_process_instances(proc_name) > 1:
            Thread(target=settings.wnd_main.sig_close.emit, daemon=True).start()
            
def remove_temp_installer():
    path = os.path.join(os.environ['USERPROFILE'], 'Downloads', 'qlinstaller.exe')
    file_remove(path)

def move_wnd():
    # ------------------------- 窗口 -------------------------
    w, h = get_main_screen_wh()
    pos_x, pos_y = w - settings.wnd_main.width(), h - settings.wnd_main.height() - 80
    settings.wnd_main.move(pos_x, pos_y)
    settings.wnd_main.sig_title.emit(f"{APP_NAME}{CLIENT_VERSION}-单开免费版")

def qi_ling_main():
    # 创建应用
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    app = QApplication()
    app.setStyle(QStyleFactory.create("fusion"))
    # 检测多开
    if detect_multi_run():
        TimeMsgBox("提示", "程序已经在运行了!").exec_()
        sys.exit(-1)
    # 注册com组件到系统
    reg_com_to_system()
    # 创建com原生对象
    settings.ori_obj = create_com_obj()
    # 创建全局com对象, 即对 原生对象 再封装一层, 以统一不同插件接口函数名
    settings.com_obj = PLUGIN_SDK(settings.ori_obj, is_first=True)
    settings.com_obj.ban_sys_sleep()
    settings.com_obj.ban_screen_protect()
    # 设置一些全局变量
    set_global_var()
    # 创建主窗口对象
    settings.wnd_main = WndMain()
    settings.wnd_main.show()
    # 初始化方案配置
    settings.wnd_main.plan_cfg_read()
    # 读取通用配置
    settings.wnd_main.common_cfg_read()
    move_wnd()
    # 发送获取自定义信息请求
    settings.wnd_main.send_request_get_custom_info_v2()
    # 删除历史遗留的废弃文件
    pics = settings.global_wk.match_pic("存_*级*灵石.bmp")
    if pics:
        pic_list = pics.split("|")
        for pic in pic_list:
            pic_path = os.path.join(DIR_RES, pic)
            file_remove(pic_path)
            print(f'已删除 {pic_path}')
    # 检查权限
    admin_res = isadmin()
    log.info(f"管理员权限运行:{admin_res}")
    if not admin_res:
        TimeMsgBox("提示", "请以管理员权限运行!\n桌面脚本图标-右键-属性-兼容性-勾选以管理员身份运行此程序-确定\n这样以后直接双击图标就都是管理员运行了").exec_()
        sys.exit(-1)
    settings.dpi = get_screen_scaling_factor()
    log.info(f"屏幕缩放率: {settings.dpi}")
    if settings.dpi != 100:
        TimeMsgBox("提示", 
                   "你的屏幕缩放率不为100%, 需额外设置一下!\n\nWin10/11设置:桌面脚本图标-右键-属性-兼容性\n更改高DPI设置-勾上缩放替代选'系统'\n\nWin7设置: 控制面板-外观和个性化-显示-100%-应用",
                   60).exec_()
        sys.exit(-1)
    # 发送获取更新信息的请求
    settings.wnd_main.send_request_get_update_info_v2()
    # 开启一个线程来做检测 进程名, 虚拟机, OD
    Thread(target=check_process_vm_od, daemon=True).start()
    # 检测 检测线程是否被干掉了
    threading.Timer(7.2452, check_flag).start()
    # 删除临时安装包
    threading.Timer(6.1544, remove_temp_installer).start()
    # 设置账号信息
    settings.wnd_main.set_account_infos()
    sys.exit(app.exec_())


if __name__.endswith('in__'):
    qi_ling_main()
