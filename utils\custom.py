class BaseObj():  # 此类用来改方法名
    def __init__(self, obj):
        # 定制版本修改函数名
        self.CreateFoobarCustom = obj.EhUpARlLbu
        self.CreateFoobarEllipse = obj.lgCWKtKp
        self.CreateFoobarRect = obj.ntFmGJtEQZ
        self.CreateFoobarRoundRect = obj.XcQGdJtguFnpYBg
        self.FoobarClearText = obj.kxwQQPRPhhCW
        self.FoobarClose = obj.eLFWSdi
        self.FoobarDrawLine = obj.GyjUsURuHPaLswc
        self.FoobarDrawPic = obj.UhnzAqYQnmb
        self.FoobarDrawText = obj.lwNfAGUXeLKg
        self.FoobarFillRect = obj.WVdygTXbZArtRH
        self.FoobarLock = obj.EPRaps
        self.FoobarPrintText = obj.jUtVN
        self.FoobarSetFont = obj.jUbXPaoBaY
        self.FoobarSetSave = obj.TcCoZrLkySlfRD
        self.FoobarStartGif = obj.BhnDMTiYLovm
        self.FoobarStopGif = obj.GjSkiPfhWuoWKI
        self.FoobarTextLineGap = obj.uECYqXBU
        self.FoobarTextPrintDir = obj.cwYLvs
        self.FoobarTextRect = obj.RXeoKWRjVugcmMj
        self.FoobarUnlock = obj.ZJqkXd
        self.FoobarUpdate = obj.fCgGqibEcEVniu
        self.FoobarSetTrans = obj.TXyFl
        self.ClientToScreen = obj.TmQwwjXLmljWJl
        self.EnumProcess = obj.mwAAfhmG
        self.EnumWindow = obj.UieFqMCYQPaen
        self.EnumWindowByProcess = obj.mfkNSHS
        self.EnumWindowByProcessId = obj.pPGqUHwUiGpBK
        self.EnumWindowSuper = obj.InSejfNtveK
        self.FindWindow = obj.BhCfg
        self.FindWindowByProcess = obj.rbzY
        self.FindWindowByProcessId = obj.genLsT
        self.FindWindowEx = obj.TkDrkZ
        self.FindWindowSuper = obj.UDtpQsFmk
        self.GetClientRect = obj.PxCDnBak
        self.GetClientSize = obj.nUhD
        self.GetForegroundFocus = obj.zcNz
        self.GetForegroundWindow = obj.vtRkyNoqrnhZX
        self.GetMousePointWindow = obj.vpFzlZALYaVgT
        self.GetPointWindow = obj.fKWFWazzcqXaNGu
        self.GetProcessInfo = obj.lFYw
        self.GetSpecialWindow = obj.HXsoZtnSEFeyHnv
        self.GetWindow = obj.vlTXcRmeuEQH
        self.GetWindowClass = obj.ZKnUzTJZyZR
        self.GetWindowProcessId = obj.rrJCQKxhSlUGQbj
        self.GetWindowProcessPath = obj.uyjryvMCTdMY
        self.GetWindowRect = obj.yRWu
        self.GetWindowState = obj.rSQuIfpoh
        self.GetWindowTitle = obj.UxiWrHXUxB
        self.MoveWindow = obj.IUfLPfw
        self.ScreenToClient = obj.GciSBjGDc
        self.SendPaste = obj.KUxNcySmLATs
        self.SendString = obj.DboIrdgPVhfbG
        self.SendString2 = obj.fYPdcJko
        self.SendStringIme = obj.audS
        self.SendStringIme2 = obj.uyRqNKMqpmcVDZ
        self.SetClientSize = obj.XlhXljfZP
        self.SetWindowSize = obj.DLKZEWRdXZSce
        self.SetWindowState = obj.YKLjYBQsGJE
        self.SetWindowText = obj.NuCTqzvojKfTEy
        self.SetWindowTransparent = obj.YlIH
        self.FaqCancel = obj.wJCzVjNXGpXLSl
        self.FaqCapture = obj.wBBnGFPtwNhK
        self.FaqCaptureFromFile = obj.lkbFvYMEzujuqHs
        self.FaqCaptureString = obj.WPksLcH
        self.FaqFetch = obj.eWbLpMeliu
        self.FaqGetSize = obj.tewWIWkDzuWLqP
        self.FaqPost = obj.ZNPlfhclgHVWv
        self.FaqSend = obj.LZilacmbQacEurx
        self.FaqIsPosted = obj.CozFmrQnyfZwSai
        self.DmGuard = obj.tZlT
        self.UnLoadDriver = obj.kBKGKLZtACiv
        self.DmGuardParams = obj.SNadiPYBF
        self.BindWindow = obj.KLprUwUyYe
        self.BindWindowEx = obj.IZcHi
        self.DownCpu = obj.XGgB
        self.EnableBind = obj.veoeNtjJcyjlw
        self.EnableFakeActive = obj.sMIRYsJWaxtIH
        self.EnableIme = obj.vpbrVbiRUGXdx
        self.EnableKeypadMsg = obj.KfDinEJ
        self.EnableKeypadPatch = obj.VMMvSfbfjU
        self.EnableKeypadSync = obj.FTfWkatMwuogm
        self.EnableMouseMsg = obj.WoFYndhrdwVIlC
        self.EnableMouseSync = obj.PYxmyt
        self.EnableRealKeypad = obj.cAaPLAY
        self.EnableRealMouse = obj.EaTcmnFGv
        self.EnableSpeedDx = obj.hvlfVB
        self.ForceUnBindWindow = obj.kmZSBhGrZbNpb
        self.GetBindWindow = obj.EYGoDwv
        self.IsBind = obj.qCiQQPz
        self.LockDisplay = obj.LeeUGc
        self.LockInput = obj.nJaMepmk
        self.LockMouseRect = obj.UYQZKJPhDWjG
        self.SetDisplayDelay = obj.GWuepJmxTT
        self.SwitchBindWindow = obj.UlfStVlLsYz
        self.UnBindWindow = obj.VWqvFMl
        self.SetAero = obj.qgkdIZRHka
        self.SetDisplayRefreshDelay = obj.gbpimGKd
        self.GetFps = obj.qfztxKcqAoS
        self.HackSpeed = obj.DbBEqDNPFABAQbh
        self.AsmAdd = obj.LgUHodDuYRoPXM
        self.AsmCall = obj.bYhZXzBG
        self.AsmClear = obj.SjBJvFgkkv
        self.DisAssemble = obj.rVvyZkRPRJqG
        self.Assemble = obj.XXvQHYlVLqMWo
        self.AsmCallEx = obj.mwTKHjqZPG
        self.AsmSetTimeout = obj.QeQPiDRFgLptVAS
        self.GetBasePath = obj.LJVcaVW
        self.GetDmCount = obj.YkepfNfW
        self.GetID = obj.foqUDNywRtqjt
        self.GetLastError = obj.FhDi
        self.GetPath = obj.EKTpCzlEgY
        self.Reg = obj.ioXTKlZ
        self.RegEx = obj.CDfXHzAgTc
        self.RegExNoMac = obj.FToaWdGEGQAqa
        self.RegNoMac = obj.pCFQuUZYHXo
        self.SetDisplayInput = obj.oPRGPq
        self.SetEnumWindowDelay = obj.bbcrh
        self.SetPath = obj.awkZJpPYUZrH
        self.SetShowErrorMsg = obj.bVAmUhmpmSaDt
        self.Ver = obj.sykFwA
        self.EnablePicCache = obj.MDgRXEUtSjJE
        self.SpeedNormalGraphic = obj.fDcTvsdiWy
        self.GetCursorPos = obj.juXWERLqVv
        self.GetCursorShape = obj.XBMIcT
        self.GetCursorShapeEx = obj.UwsWGYaQoo
        self.GetCursorSpot = obj.DmGNsPtnEZsS
        self.GetKeyState = obj.ozjTMcBhwfNQJnU
        self.KeyDown = obj.qvDU
        self.KeyDownChar = obj.MSNhDBJCWU
        self.KeyPress = obj.aGKRFugcoftZ
        self.KeyPressChar = obj.qWNaFdokRSsc
        self.KeyPressStr = obj.SbUaJnVBehL
        self.KeyUp = obj.vGxYUbRNAoew
        self.KeyUpChar = obj.zJEDsJBxqQ
        self.LeftClick = obj.kyDaLAjukVycraY
        self.LeftDoubleClick = obj.UVRioGoDkLxQRc
        self.LeftDown = obj.UmgZZlu
        self.LeftUp = obj.DKTPcrGHEfB
        self.MiddleClick = obj.cNWbWnGnzVoM
        self.MiddleDown = obj.HvNcrIYU
        self.MiddleUp = obj.zJiktWpnyI
        self.MoveR = obj.MjBZgaJKIo
        self.MoveTo = obj.tUtInvAB
        self.MoveToEx = obj.yKhPoqhiKxqxvQ
        self.RightClick = obj.dKRkHlS
        self.RightDown = obj.ImBrmWx
        self.RightUp = obj.iyQww
        self.SetKeypadDelay = obj.HomtUeYPy
        self.SetMouseDelay = obj.DyVTRgehaVBm
        self.SetSimMode = obj.VDFgIofmisHSTvf
        self.WaitKey = obj.BfRmdxDdN
        self.WheelDown = obj.eRywFINUvcbtkl
        self.WheelUp = obj.ExrwBAzyQsS
        self.GetMouseSpeed = obj.wYhSWQbNi
        self.SetMouseSpeed = obj.TAyZ
        self.EnableMouseAccuracy = obj.zGor
        self.DoubleToData = obj.wMZpYHHKEvvVkBR
        self.FindData = obj.zgAPc
        self.FindDataEx = obj.bauYhwei
        self.FindDouble = obj.BhaITY
        self.FindDoubleEx = obj.lxRp
        self.FindFloat = obj.htBZ
        self.FindFloatEx = obj.qwYRxLPxtVstqL
        self.FindInt = obj.vnBJWGERHCWXh
        self.FindIntEx = obj.kfeEwlDcNLlW
        self.FindString = obj.noMu
        self.FindStringEx = obj.diFIguSzZN
        self.FloatToData = obj.kSXVnC
        self.FreeProcessMemory = obj.bZJzFkjbVfyW
        self.GetCommandLine = obj.nsgFvWsGbPXf
        self.GetModuleBaseAddr = obj.TrdVeV
        self.IntToData = obj.GpiwxXzGz
        self.OpenProcess = obj.pyZnSzio
        self.ReadData = obj.HMHWzbBWVBF
        self.ReadDataAddr = obj.CybwUV
        self.ReadDouble = obj.LLQhnNYc
        self.ReadDoubleAddr = obj.dyuhInuivnv
        self.ReadFloat = obj.jAbYkBSKQb
        self.ReadFloatAddr = obj.sbMI
        self.ReadInt = obj.cvELYkBcWuzTEiA
        self.ReadIntAddr = obj.aKlCA
        self.ReadString = obj.aGRGSapQTQZtyL
        self.ReadStringAddr = obj.DEIza
        self.SetMemoryFindResultToFile = obj.ypzMeuKocnXwq
        self.SetMemoryHwndAsProcessId = obj.WkkrycCq
        self.StringToData = obj.FUMNfugw
        self.TerminateProcess = obj.YwglR
        self.VirtualAllocEx = obj.SZMJvf
        self.VirtualFreeEx = obj.vVumSoV
        self.WriteData = obj.JjHyzfsWKocbxw
        self.WriteDataAddr = obj.ZASzmYDGW
        self.WriteDouble = obj.xZJt
        self.WriteDoubleAddr = obj.bhKne
        self.WriteFloat = obj.BNGHkS
        self.WriteFloatAddr = obj.Eaqhdp
        self.WriteInt = obj.cTFNGpLBGrEkHYh
        self.WriteIntAddr = obj.MClrZjcQgwSUJhl
        self.WriteString = obj.iqJDKocUGgRtn
        self.WriteStringAddr = obj.RTvzj
        self.VirtualProtectEx = obj.ZDBfPVDux
        self.ReadDataToBin = obj.vWkVntbaxUhuj
        self.WriteDataFromBin = obj.zAQHB
        self.ReadDataAddrToBin = obj.KYkakaeddmLqQxT
        self.WriteDataAddrFromBin = obj.pcomjHDKrKKsGu
        self.SetParam64ToPointer = obj.EENRcWnwVxAo
        self.VirtualQueryEx = obj.geeHfrs
        self.GetRemoteApiAddress = obj.cihZrLUgBdC
        self.GetModuleSize = obj.clzCzfDkB
        self.ExcludePos = obj.SQYJ
        self.FindNearestPos = obj.SRYXeTqYfUpLq
        self.SortPosDistance = obj.piGbZseh
        self.AppendPicAddr = obj.GAZtftMFmtbh
        self.BGR2RGB = obj.VxUhDNlce
        self.Capture = obj.kYQzxxyjXPdKg
        self.CaptureGif = obj.rkFFlV
        self.CaptureJpg = obj.lTZeb
        self.CapturePng = obj.HRQQNH
        self.CapturePre = obj.nVEEPLbBM
        self.CmpColor = obj.mYjueWhY
        self.EnableDisplayDebug = obj.ILcRpCs
        self.EnableGetColorByCapture = obj.vNkRdjsapDK
        self.FindColor = obj.GsjYuLuqw
        self.FindColorBlock = obj.CTFpfS
        self.FindColorBlockEx = obj.IhDnxToNkaX
        self.FindColorE = obj.LZmYpG
        self.FindColorEx = obj.XGCtDEs
        self.FindMulColor = obj.qWdv
        self.FindMultiColor = obj.ayJmRGR
        self.FindMultiColorE = obj.zSjrzdbahcU
        self.FindMultiColorEx = obj.hUlX
        self.FindPic = obj.VXILbSmrtawg
        self.FindPicE = obj.nWhuSkjDQKkdCAn
        self.FindPicEx = obj.QNuAVaVedzePF
        self.FindPicExS = obj.nDvRmwtb
        self.FindPicMem = obj.AJouYeqtcrlgEv
        self.FindPicMemE = obj.sYZrEhIbNqDao
        self.FindPicMemEx = obj.XvQgfYK
        self.FindPicS = obj.Cdtn
        self.FindShape = obj.CjVZaZuKr
        self.FindShapeE = obj.FeUAguWNyklAoW
        self.FindShapeEx = obj.FgZH
        self.FreePic = obj.gxWWqdrMQHAbTzu
        self.GetAveHSV = obj.PtlvPItIr
        self.GetAveRGB = obj.euvaaQsvM
        self.GetColor = obj.XeXuRiCJlNbgw
        self.GetColorBGR = obj.ufVkmrQ
        self.GetColorHSV = obj.fnGouIjsfNXP
        self.GetColorNum = obj.kKluPeE
        self.GetPicSize = obj.GtbWDwrjJA
        self.GetScreenData = obj.njYitXhQM
        self.GetScreenDataBmp = obj.dgvvWzSYGK
        self.ImageToBmp = obj.nKvkuU
        self.IsDisplayDead = obj.iDzaZaRuIsZU
        self.LoadPic = obj.uiiqvBP
        self.MatchPicName = obj.VXoLeWKxgMhMuc
        self.RGB2BGR = obj.aaRFcTgYxxnf
        self.SetPicPwd = obj.hGeMyeSRVfKQ
        self.LoadPicByte = obj.LXLeD
        self.SetExcludeRegion = obj.mAIgdj
        self.EnableFindPicMultithread = obj.FSKsIlPk
        self.CopyFile = obj.PabUvUoKPPQ
        self.CreateFolder = obj.qDZbFRXDjmY
        self.DecodeFile = obj.bYBXIfnAyYqy
        self.DeleteFile = obj.sVqZeEwLHsKeN
        self.DeleteFolder = obj.ZudUoIZqmAbzEN
        self.DeleteIni = obj.dCzELknbdXiU
        self.DeleteIniPwd = obj.ihbRWLVAU
        self.DownloadFile = obj.HVDbZ
        self.EncodeFile = obj.YissQwTJnkrXTKJ
        self.EnumIniKey = obj.ZBCVhmC
        self.EnumIniKeyPwd = obj.DKfJwzXXHFV
        self.EnumIniSection = obj.aVyocvjHMk
        self.EnumIniSectionPwd = obj.tiayVZVwX
        self.GetFileLength = obj.Mbue
        self.IsFileExist = obj.cFNYJLxaYnbb
        self.MoveFile = obj.RfXCXaqQwVwJJr
        self.ReadFile = obj.wKRYWgHD
        self.ReadIni = obj.qLkyCUabEJmZX
        self.ReadIniPwd = obj.fAYufhdjxDte
        self.SelectDirectory = obj.RRXMSztNTLZCjk
        self.SelectFile = obj.fyYhtAWoyeTA
        self.WriteFile = obj.qQaQCefrnkH
        self.WriteIni = obj.UyDEwDzz
        self.WriteIniPwd = obj.MzDuizuo
        self.IsFolderExist = obj.IXAkYYRfcun
        self.GetRealPath = obj.yPFWSyyAw
        self.AddDict = obj.lUCMECSACUvB
        self.ClearDict = obj.FBHYFqXuvCFWejZ
        self.FetchWord = obj.tbffgmSYRgQ
        self.FindStr = obj.AnHcQTuvPbUztE
        self.FindStrE = obj.JTiF
        self.FindStrEx = obj.AEvrE
        self.FindStrExS = obj.lEYctC
        self.FindStrFast = obj.DvRCqMvzPTe
        self.FindStrFastE = obj.dkZbsBman
        self.FindStrFastEx = obj.TCvwZinmcS
        self.FindStrFastExS = obj.iHGRuRbflSDhBbu
        self.FindStrFastS = obj.tsqMdRuW
        self.FindStrS = obj.hUutZ
        self.FindStrWithFont = obj.fQLPSxhFV
        self.FindStrWithFontE = obj.tbyi
        self.FindStrWithFontEx = obj.vKveqzo
        self.GetDict = obj.SVaiWRHLHmIv
        self.GetDictCount = obj.UaDsbbwoSb
        self.GetDictInfo = obj.KAVcL
        self.GetNowDict = obj.StFHoWzu
        self.GetResultCount = obj.crAnPeP
        self.GetResultPos = obj.ZPdVqQEkulT
        self.GetWordResultCount = obj.QuTRJ
        self.GetWordResultPos = obj.zQRxNdP
        self.GetWordResultStr = obj.ClruncBpDM
        self.GetWords = obj.NJxgZpvgvQmfg
        self.GetWordsNoDict = obj.GklQWDFJM
        self.Ocr = obj.keEymEzH
        self.OcrEx = obj.meFECTiSk
        self.OcrExOne = obj.RPeMbjNT
        self.OcrInFile = obj.cvaKQ
        self.SaveDict = obj.rNHfTNckn
        self.SetColGapNoDict = obj.FlnGLERsytYkrc
        self.SetDict = obj.nuxHjoHKgHqo
        self.SetDictMem = obj.FXcTJLp
        self.SetDictPwd = obj.qExUB
        self.SetExactOcr = obj.DotoYUWJRmSJrZr
        self.SetMinColGap = obj.kJCPVXwcEsx
        self.SetMinRowGap = obj.BBoxuqyYUzA
        self.SetRowGapNoDict = obj.HGccyljoXfy
        self.SetWordGap = obj.ckfCzD
        self.SetWordGapNoDict = obj.fcaeW
        self.SetWordLineHeight = obj.WheILcf
        self.SetWordLineHeightNoDict = obj.VayXx
        self.UseDict = obj.CeuVduBgkLV
        self.EnableShareDict = obj.KNPbYpDQLooWryu
        self.Beep = obj.YfirrVCZcUzByx
        self.CheckFontSmooth = obj.sXvWAjveLXGqE
        self.CheckUAC = obj.vJbbkNNIyEQdwp
        self.Delay = obj.DdAQyBWjLt
        self.Delays = obj.BrvDDDhxWPRkH
        self.DisableFontSmooth = obj.fvFJsk
        self.DisablePowerSave = obj.cQiHAuCD
        self.DisableScreenSave = obj.QxZgrIJoP
        self.ExitOs = obj.ZjFFcEqSiH
        self.GetClipboard = obj.KJrDomcTUDYR
        self.GetDir = obj.tUEg
        self.GetDiskSerial = obj.RXGb
        self.GetDisplayInfo = obj.WkQsXUVqTARggTc
        self.GetMachineCode = obj.irMCkETVVoL
        self.GetMachineCodeNoMac = obj.vbGgGl
        self.GetNetTime = obj.nFcezFHZuzFw
        self.GetNetTimeByIp = obj.ZWsLNpnJFcIIV
        self.GetNetTimeSafe = obj.lmTt
        self.GetOsType = obj.ylUzeknuVSY
        self.GetScreenDepth = obj.aFamlRQjZ
        self.GetScreenHeight = obj.sREomrGQisRe
        self.GetScreenWidth = obj.KDjywf
        self.GetTime = obj.EpYrRxHWcj
        self.Is64Bit = obj.vtzDPu
        self.Play = obj.ZYdIoYPUCHDk
        self.RunApp = obj.iQSkun
        self.SetClipboard = obj.SKME
        self.SetDisplayAcceler = obj.lzDmDSZgICI
        self.SetScreen = obj.VNonF
        self.SetUAC = obj.lrib
        self.Stop = obj.hcXf
        self.EnableFontSmooth = obj.oyIbRHnpHVE
        self.DisableCloseDisplayAndSleep = obj.wRnWNKwiX
        self.SetLocale = obj.HSShfMQmDFuVUvU
        self.GetLocale = obj.JwGwNWkVg
        self.Int64ToInt32 = obj.hRxaoipClYjFqkM
        self.GetDPI = obj.eIkdntG
        self.GetCpuType = obj.TzzoedEHPxLqpWZ
        self.GetOsBuildNumber = obj.mRaUUDdC
        self.ShowTaskBarIcon = obj.XxHQVdXz
        self.IsSurrpotVt = obj.VYgCBR
        self.GetDiskModel = obj.BDWb
        self.GetDiskReversion = obj.SqpMH
        self.GetCpuUsage = obj.ZIyHVQs
        self.GetMemoryUsage = obj.tnlUnBbAKVHSW
        self.ActiveInputMethod = obj.pBfpDJy
        self.CheckInputMethod = obj.nBNxVB
        self.EnterCri = obj.SJQMPByPwLPNGpr
        self.FindInputMethod = obj.vNloQFZCdmoqpsr
        self.InitCri = obj.WuWzCrRbhF
        self.LeaveCri = obj.JENCwZX
        self.ShowScrMsg = obj.UfXzRVi
        self.Md5 = obj.QnFBpC
        self.GetMac = obj.zwfrBiETeeHLYCZ
        self.SetExportDict = obj.JxMg
        self.ReadFileData = obj.bShCXtHgYrFCJ
        self.ReleaseRef = obj.sjrjrqrwkx
        self.SetExitThread = obj.Hdupsz
        self.ExecuteCmd = obj.SHjIRxvp
        self.Hex32 = obj.HPypgHhxbNWwyq
        self.Hex64 = obj.bxzGRstwLRoeVmz
