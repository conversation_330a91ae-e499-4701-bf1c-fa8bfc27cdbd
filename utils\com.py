import ctypes
import subprocess
import winreg
from win32com.client import Dispatch
from comtypes.client import CreateObject
from const.const import *
from utils.plugins import *


def reg_com_to_system() -> bool:
    """
    注意事项：
    1. 免注册插件要用32位py
    2. regsvr32需要以管理员运行, 但我们这里不需要sudo, 因为我们程序必须要管理员运行才能正常
    3. 免注册接口返回ret为1是成功
    """
    path_dll = f"{DIR_DLL}\\{DLL_NAME_DM}"
    path_reg = f"{DIR_DLL}\\{DLL_NAME_REGDM}"
    if PLUGIN_SDK == Dm:
        print("dm免注册")
        DmReg = ctypes.windll.LoadLibrary(path_reg)
        ret = DmReg.SetDllPathW(path_dll, 1)
    elif PLUGIN_SDK == Op:
        print("op免注册")
        OpReg = ctypes.windll.LoadLibrary(path_reg)
        ret = OpReg.setupW(path_dll)
    else:
        print("lw直接注册")
        ret = run_command(f"regsvr32 {path_dll} /s")
    print("com注册结果:", ret)
    ret = True if ret == 1 else False
    return ret


def create_com_obj():
    obj = None
    try:
        if PLUGIN_SDK in [Dm, Op]:
            obj = Dispatch(PLUGIN_SDK_COM_NAME)  # dm, tr, op
        else:
            obj = CreateObject(PLUGIN_SDK_COM_NAME)  # lw
    except:
        print(f"创建com对象{PLUGIN_SDK_COM_NAME}失败")
    return obj


def run_command(command):
    print(f"执行命令: {command}")
    # 禁止弹出窗口
    res = subprocess.run(command,
                         check=True,
                         capture_output=True,
                         text=True,
                         creationflags=subprocess.CREATE_NO_WINDOW)
    return res




