from PySide2.QtCore import QTime

from biz.task._jrw_xl_dg import TaskJrwXlDg
from biz.constants.constants import *
from biz.obj.worker import Worker
from utils import *


class TaskWeiWang(TaskJrwXlDg):
    TASK_NAME = "带队威望"
    
    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        if wk.cfg_plan_task["切首发宠"]:
            wk.team.is_switch_primary_bb_enable = True
        else:
            wk.team.is_switch_primary_bb_enable = False

    @classmethod
    def get_task_name(cls) -> str:
        return "除恶扶正"

    @classmethod
    def get_task_publisher_name(cls) -> str:
        return "武林盟管事"
    
    @classmethod
    def fast_fly_kaifeng(cls, wk: Worker, npc_name: str):
        # 武林盟飞不了
        return False

    @classmethod
    def goto_recv_task_place(cls, wk: Worker):
        if cls.cur_map(wk) == "武林盟":
            return
        cls.back_to_kai_feng(wk)

    @classmethod
    def click_system_task_name(cls, wk: Worker, enable_white=True):
        res = super().click_system_task_name(wk, enable_white)

    @classmethod
    def talk_recv_task(cls, wk: Worker) -> bool:
        res = wk.find_str_click(*RECT_TALK, "除恶扶正", COLOR_TALK_ITEM, is_sleep=False)
        if cls.is_talk_show_info(wk, "已完成所有", COLOR_BLACK, timeout=200):
            wk.record("任务完成达到上限")
            cls.close_other_talk(wk)
            raise Exception("任务完成达到上限")
        return res
    
    @classmethod
    def handle_find_way_often(cls, wk: Worker):
        raise Exception("寻路超过次数")
    
    @classmethod
    def find_way_fight(cls, wk: Worker):
        try:
            super().find_way_fight(wk)
        except:  # 只会有 任务已进入终态的异常
            cls.refresh_task_list(wk)
            

    @classmethod
    def get_task_setting_count(cls, wk: Worker) -> int:
        return wk.cfg_plan_task["次数"]
    
    @classmethod
    def is_reach_time(cls, wk: Worker):
        if wk.cfg_plan_task["定时结束"]:
            if wk.cur_task_start_time_fmt > wk.cfg_plan_task["定时结束时间"]:
                wk.record("任务开始时间超过定时结束时间, 忽略定时结束")
                wk.cfg_plan_task["定时结束"] = False
                return False
            if settings.cur_time_fmt[:5] >= wk.cfg_plan_task["定时结束时间"]:
                wk.record("定时结束时间到")
                return True
        return False
    
    @classmethod
    def enable_task_fix_equip_bb(cls, wk: Worker):
        return wk.cfg_plan_task["修理忠诚"]
    
    @classmethod
    def after_fight_switch_primary_bb(cls, wk: Worker):
        if wk.cfg_plan_task["切首发宠"]:
            wk.team.is_switch_primary_bb_enable = True
        else:
            wk.team.is_switch_primary_bb_enable = False
        if wk.team.is_switch_primary_bb_enable:
            cls.switch_primary_bb(wk)

    @classmethod
    def get_default_biz_config(cls):
        return {
            "次数": 210,
            "定时结束": False,
            "定时结束时间": "14:04",
            "修理忠诚": False,
            "切首发宠": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_wei_wang.setValue(cls.CONFIG["次数"])
        settings.wnd_main.groupBox_time_stop_wei_wang.setChecked(cls.CONFIG["定时结束"])
        settings.wnd_main.tedt_timer_wei_wang.setTime(QTime.fromString(cls.CONFIG["定时结束时间"], "HH:mm"))
        settings.wnd_main.chk_wei_wang_fix_bb.setChecked(cls.CONFIG["修理忠诚"])
        settings.wnd_main.chk_wei_wang_switch_primary_bb.setChecked(cls.CONFIG["切首发宠"])
        

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["次数"] = int(settings.wnd_main.spin_count_wei_wang.value())
        cls.CONFIG["定时结束"] = settings.wnd_main.groupBox_time_stop_wei_wang.isChecked()
        cls.CONFIG["定时结束时间"] = settings.wnd_main.tedt_timer_wei_wang.time().toString("HH:mm")
        cls.CONFIG["修理忠诚"] = settings.wnd_main.chk_wei_wang_fix_bb.isChecked()
        cls.CONFIG["切首发宠"] = settings.wnd_main.chk_wei_wang_switch_primary_bb.isChecked()
        super().cfg_save(plan_name)