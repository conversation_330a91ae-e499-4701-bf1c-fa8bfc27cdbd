from biz.constants.constants import *
from biz.task.__base import TaskBase
from biz.task.single.shi_men import TaskShiMen
from utils import *
from biz.obj.worker import Worker



class TaskRenWuLian(TaskBase):
	TASK_NAME = "芳芳任务链"
	NEED_AVOID_FIGHT = False
	# 猴儿酒 刺帽针 眼罩 相思玉 旧马鞍 古墓散卷

	@classmethod
	def run(cls, wk: Worker):
		while wk.done_count < cls.get_task_setting_count(wk):
			try:
				cls.receive_task(wk)
			except Exception as e:
				wk.record("任务完成达到上限")
				break
			try:
				cls.do_task(wk)
			except:  # 任务终态
				cls.click_refresh_task_pic(wk)
	
	@classmethod
	def receive_task(cls, wk: Worker):
		wk.record("接任务中...")
		cls.back_to_kai_feng(wk)
		# 直接点任务链是可以交任务的
		cls.talk_with_cur_map_npc(wk, "芳芳", ["任务链"], reply_first=True)
		
	@classmethod
	def get_task_setting_count(cls, wk: Worker):
		return 10
		
	@classmethod
	def do_task(cls, wk: Worker):
		cls.open_task_page(wk)
		msleep(400)
		if cls.region_task_status_get_status(wk) in ["回复", "成功"]:
			cls.refresh_task_list(wk)
			return
		if cls.region_task_desc_find_str(wk, "送到的"):
			cls.do_task_send_mail(wk)
			cls.talk_with_cur_map_npc(wk, "芳芳", ["任务链"], reply_first=True)
			return
		
		npc_name = cls.region_task_status_get_npc_name_pro(wk)
		if '芳芳' not in npc_name:
			wk.record("任务被干扰, 重新接任务...")
			return
		if cls.region_task_desc_find_str(wk, "到杀个"):
			return cls.do_task_kill_monster(wk)
		if cls.region_task_desc_find_str(wk, "寻找个"):
			return cls.do_task_find_thing(wk)
		if cls.region_task_desc_find_str(wk, "施舍古董"):
			return cls.do_task_give_gudong(wk)
		
	@classmethod
	def is_task_status_turn_reply(cls, wk: Worker) -> bool:
		cls.open_task_page(wk)
		if cls.region_task_status_get_npc_name_pro(wk) != '芳芳' or cls.region_task_status_get_status(wk) == "回复":
			cls.close_pages(wk)
			wk.record("杀怪数量已达标")
			return True  # 表示跳出杀怪任务, 去接下一个任务
		cls.close_pages(wk)
		return False
	
	@classmethod
	def do_task_find_thing(cls, wk: Worker):
		# 优先直接交任务试试, 看能不能点到直接回复
		if cls.talk_with_cur_map_npc(wk, "芳芳", ["任务"], only_reply=True) is None:
			wk.record("找物品任务直接回复成功")
			return
		wk.record("任务品准备不足, 前往寄售购买...")
		cls.open_task_page(wk)
		msleep(400)
		thing_name = cls.region_task_desc_ocr(wk, COLOR_GREEN)
		thing_num = cls.region_task_desc_ocr_one_line(wk, COLOR_GREEN, zk=ZK_DIGIT_11)
		wk.record(f"寻找物品:{thing_name} 数量:{thing_num}")
		cls.ji_shou_buy_thing(wk, thing_name, thing_num)

	@classmethod
	def do_task_send_mail(cls, wk: Worker, run_away=True):
		npc_name = cls.region_task_status_get_npc_name_pro(wk)
		wk.record(f"送口信给 {npc_name}")
		cls.close_pages(wk)
		if run_away:
			cls.task_npc_find_way_run_away(
				wk, "送口信", npc_name=npc_name, color=COLOR_TALK_ITEM_TASK)
		else:
			cls.task_npc_find_way(
				wk, "送口信", npc_name=npc_name, color=COLOR_TALK_ITEM_TASK)
			
	@classmethod
	def do_task_give_gudong(cls, wk: Worker):
		wk.record("正在交古董...")
		cls.talk_with_cur_map_npc(wk, "无敌小乞丐", ["施舍"], close_talk=False)
		wk.record("请手动给予后继续运行")
		wk.pause()

	@classmethod
	def do_task_kill_monster(cls, wk: Worker, run_away=True):
		target_map_name = cls.region_task_desc_ocr_one_line(wk, COLOR_RED)
		wk.record(f"到{target_map_name}消灭怪物...")
		if run_away:
			cls.run_to_map_run_away(wk, target_map_name)
		else:
			cls.run_to_map(wk, target_map_name)
		cls.use_hls(wk)
		for _ in range(500):
			if wk.is_fight:
				cls.fight_operation(wk, check_xmx=False)
				if cls.do_fix_bb(wk):  # 如果修理忠诚 要再回来原地图
					if run_away:
						cls.run_to_map_run_away(wk, target_map_name)
					else:
						cls.run_to_map(wk, target_map_name)
				cls.stop_auto_find_way(wk, force=True)
				if cls.is_task_status_turn_reply(wk):
					if not run_away:
						cls.use_xmx(wk)
					break
				wk.is_stuck = True
			if wk.is_stuck:
				cls.auto_meet_enemy_by_shortcut(wk)
			msleep(800)