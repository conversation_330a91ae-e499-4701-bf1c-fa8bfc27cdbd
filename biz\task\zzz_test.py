from biz.task import TaskBase
from biz.task.single.qing_li import TaskQingLi
from biz.task.single.shua_ye_single import TaskShuaYeSingle
from biz.obj.worker import Worker
from utils import *
from biz.constants.constants import *
from PySide2.QtWidgets import QTreeWidgetItemIterator


class TaskCeShi(TaskBase):
    TASK_NAME = "测试专用"
    NEED_PLAYER_NAME = False

    @classmethod
    def before_run(cls, wk: Worker):
        pass

    @classmethod
    def run(cls, wk: Worker):
        pics = wk.match_pic("装备_*.bmp")
        print(pics)

    
    @classmethod
    def after_run(cls, wk: Worker):
        pass


    @classmethod
    def capture_pic_name(cls, wk: Worker):
        while True:
            x, y = wk.get_str_pos(*RECT_FULL, "类型", COLOR_OBJECT_DESC, zk=ZK_ALL_9)
            if x > 0:
                wk.record(f"找到物品: {x}, {y}")
                obj_type = wk.ocr(
                    x + 28, y - 1, x + 100, y + 12, COLOR_OBJECT_DESC, zk=ZK_ALL_9
                )
                obj_name = wk.ocr(
                    x - 1,
                    y - 17,
                    x + 120,
                    y - 1,
                    COLOR_OBJECT_DESC
                    + "|"
                    + COLOR_GOLD
                    + "|"
                    + COLOR_WHITE
                    + "|"
                    + COLOR_QUALITY_BLUE
                    + "|"
                    + COLOR_QUALITY_GREEN,
                    zk=ZK_ALL_11,
                ).lstrip("华丽")
                if not obj_name:
                    continue
                x2, y2 = wk.get_str_pos(*RECT_FULL, "等级", COLOR_WHITE, zk=ZK_ALL_9)
                obj_level = ""
                if x2 > 0:
                    obj_level = wk.ocr(
                        x2, y2 - 1, x2 + 50, y2 + 12, COLOR_WHITE, zk=ZK_DIGIT_9
                    )
                obj_gender = ""
                x3, y3 = wk.get_str_pos(
                    *RECT_FULL, "性别", COLOR_WHITE + "|" + COLOR_RED, zk=ZK_ALL_9
                )
                if x3 > 0:
                    if wk.find_str(
                        *RECT_FULL, "男", COLOR_WHITE + "|" + COLOR_RED, zk=ZK_ALL_9
                    ):
                        obj_gender = "男"
                    else:
                        obj_gender = "女"
                kind = ""
                if obj_type in [
                    "铠甲",
                    "武器",
                    "冠帽",
                    "靴子",
                    "项链",
                    "戒指",
                    "护腕",
                    "腰带",
                ]:
                    kind = "装备"
                elif obj_name.endswith("碎片"):
                    kind = "存"
                elif obj_type.endswith("主材"):
                    kind = "主材"
                elif obj_type.endswith("辅材"):
                    kind = "辅材"
                elif obj_type == "物品":
                    if wk.find_str(
                        *RECT_FULL, "回收单价|出售价格", COLOR_WHITE, zk=ZK_ALL_9
                    ):
                        kind = "杂货"
                else:
                    kind = obj_type
                pic_name = (
                    f"{kind}_{obj_name}.bmp"
                    if not obj_gender
                    else f"{kind}_{obj_name}{obj_gender}.bmp"
                )
                wk.record(
                    f"物品类型: {obj_type}, 名称: {obj_name}, 等级: {obj_level}, 种类: {kind}, 图片: {pic_name}"
                )
                if os.path.exists(pic_name):
                    wk.record(f"已存在: {pic_name}")
                    wk.beep()
                    continue
                wk.capture(x - 33, y - 1, x - 23, y + 9, pic_name)
            msleep(1000)



def iterate_items(item, level=0):
    print("  " * level + item.text(0))  # 打印当前节点的文字
    
    # 获取当前节点下的所有子节点数量
    child_count = item.childCount()
    print(f"child_count: {child_count}")
    
    # 如果有子节点，则递归地遍历它们
    for i in range(child_count):
        child_item = item.child(i)
        iterate_items(child_item, level + 1)