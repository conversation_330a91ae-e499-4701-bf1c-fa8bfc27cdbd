from threading import Thread
import win32con
import win32gui
from win32gui import GetWindowRect, SetWindowPos, SetForegroundWindow, ShowWindow
from win32con import PROCESS_ALL_ACCESS, HWND_TOP, SWP_NOSIZE
from win32process import GetWindowThreadProcessId, TerminateProcess
from win32api import OpenProcess
from win32com.client import Dispatch
import pythoncom
from PySide2.QtWidgets import QApplication

import settings


def get_wnd_pos(hwnd):
    rect = GetWindowRect(hwnd)
    x, y = rect[0], rect[1]
    return x, y

def send_key():
    pass
    # pythoncom.CoInitialize()
    # shell = Dispatch("WScript.Shell")
    # # 发送backspace
    # shell.SendKeys("{END}")  # 发送ALT键，ALT-%, CTRL-^, SHIFT-+, ENTER-~


def set_wnd_pos(hwnd, x, y):
    send_key()
    try:
        ret = SetWindowPos(hwnd, HWND_TOP, x, y, 0, 0, SWP_NOSIZE)
        return ret
    except:
        return False


def activate_wnd(hwnd):
    # 在该函数调用前，需要先发送一个其他键给屏幕，如ALT键, 否则可能报错
    send_key()
    try:
        SetForegroundWindow(hwnd)
        ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
    except:
        pass


def is_wnd_in_list(hwnd: int, worker_list) -> bool:
    for wk in worker_list:
        if wk is None:
            continue
        if wk.hwnd == hwnd:
            return True
    return False

def terminate_wnd(hwnd):
    try:
        tid, pid = GetWindowThreadProcessId(hwnd)
        hProcess = OpenProcess(PROCESS_ALL_ACCESS, False, pid)
        TerminateProcess(hProcess, 0)
    except:
        pass
    
def get_pid_by_hwnd(hwnd):
    try:
        _, pid = GetWindowThreadProcessId(hwnd)
        return pid
    except:
        return -1


# 获取 主屏幕 宽高
def get_main_screen_wh():
    pri_screen = QApplication.primaryScreen()
    rect = pri_screen.geometry()
    w = rect.width()
    h = rect.height()
    return w, h


# 排列所有窗口
def arrange_all_wnd(idx, w_game, h_game, screen_w, screen_h):
    w_left_space = 60  # 左侧预留宽度
    h_status_bar = 40  # 状态栏高度
    h_wnd_title = 25  # 窗口标题高度
    w_script = 700  # 脚本窗口宽度
    w_min = w_game + w_left_space
    h_min = h_game + h_status_bar + h_wnd_title
    if screen_h <= h_min or screen_w <= w_min:
        return
    # 获取 游戏窗口数
    wnd_num = 0
    for hwnd in settings.hwnd_list:
        if hwnd is None:
            continue
        wnd_num += 1
    if wnd_num < 2:
        return

    if idx == 0:  # 垂直紧密排列
        """
        第0个窗口位置在(w_left_space, h_wnd_title*0),
        第1个窗口位置在(w_left_space, h_wnd_title*1),
        第2个窗口位置在(w_left_space, h_wnd_title*2)...
        第5个窗口位置在(w_left_space, h_wnd_title*5),
        第6个窗口位置在(w_left_space, h_wnd_title*6)...
        """
        while h_wnd_title*wnd_num + h_status_bar + h_game > screen_h:
            h_wnd_title -= 1
        # 根据屏幕宽度来算宽度gap
        for wk in settings.worker_list:
            if wk is None:
                continue
            row = wk.row
            pos_x = w_left_space
            pos_y = h_wnd_title * row
            Thread(target=set_wnd_pos, args=(wk.hwnd, pos_x, pos_y), daemon=True).start()
    elif idx == 1:  # 垂直扩展
        """
        screen_h = h_wnd_title + h_status_bar + h_game + delta_y*4
        screen_w = w_left_space + w_game + delta_x*4
        delta_y = (screen_h - h_status_bar - h_game - h_wnd_title) // 4
        delta_x = (screen_w - w_left_space - w_game) // 4
        第一个窗口在(w_left_space, 0), 第二个窗口在(w_left_space, delta_y*1), 第三个窗口在(w_left_space, delta_y*2), 第四个窗口在(w_left_space, delta_y*3)
        第六个窗口在(w_left_space+delta_x, 0), 第七个窗口在(w_left_space+delta_x, delta_y*1), 第八个窗口在(w_left_space+delta_x, delta_y*2), 第九个窗口在(w_left_space+delta_x, delta_y*3)
        """
        delta_y = (screen_h - h_status_bar - h_game - h_wnd_title) // 4
        delta_x = (screen_w - w_left_space - w_game - w_script) // 4
        pos_x = w_left_space
        pos_y = 0
        for wk in settings.worker_list:
            if wk is None:
                continue
            Thread(target=set_wnd_pos, args=(wk.hwnd, pos_x, pos_y), daemon=True).start()
            wk.x, wk.y = pos_x, pos_y
            pos_y += delta_y
            if pos_y >= (screen_h - h_game - h_status_bar):
                pos_x += delta_x
                pos_y = 0
    elif idx == 2:  # 平铺4K
        """
        第一个窗口位置在(w_left_space, 0), 第二个窗口位置在(w_left_space+w_game, 0), 
        第三个窗口位置在(w_left_space+2*w_game, 0), 以此类推，
        如果后面的窗口宽度超出屏幕，则从(w_left_space, h_game+h_wnd_title)开始排列
        """
        pos_x = w_left_space
        pos_y = 0
        for wk in settings.worker_list:
            if wk is None:
                continue
            if pos_x + w_game <= screen_w:
                Thread(target=set_wnd_pos, args=(wk.hwnd, pos_x, pos_y), daemon=True).start()
                wk.x, wk.y = pos_x, pos_y
                pos_x += w_game
            else:
                pos_x = w_left_space
                pos_y += h_game + h_wnd_title
                Thread(target=set_wnd_pos, args=(wk.hwnd, pos_x, pos_y), daemon=True).start()
                wk.x, wk.y = pos_x, pos_y
                pos_x += w_game
    elif idx == 3:  # 对角紧密
        while h_wnd_title*wnd_num + h_status_bar + h_game > screen_h:
            h_wnd_title -= 1
        w_gap = 30
        while w_left_space + w_game + w_script + w_gap*wnd_num > screen_w:
            w_gap -= 1
        # 根据屏幕宽度来算宽度gap
        for wk in settings.worker_list:
            if wk is None:
                continue
            row = wk.row
            pos_x = w_left_space + row * w_gap
            pos_y = h_wnd_title * row
            Thread(target=set_wnd_pos, args=(wk.hwnd, pos_x, pos_y), daemon=True).start()
    elif idx == 4:  # 左上重叠
        for wk in settings.worker_list:
            if wk is None:
                continue
            Thread(target=set_wnd_pos, args=(wk.hwnd, 0, 0), daemon=True).start()
