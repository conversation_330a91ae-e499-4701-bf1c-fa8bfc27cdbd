from win32gui import SetForegroundWindow
from PySide2.QtCore import QThread
from win32com.client import Dispatch
import random
import pythoncom

# 随机数
def rnd(min: int, max: int):
    return random.randint(min, max)


def activate_wnd(hwnd):
    # 在该函数调用前，需要先发送一个其他键给屏幕，如ALT键, 否则可能报错
    pythoncom.CoInitialize()
    shell = Dispatch("WScript.Shell")
    shell.SendKeys("%")  # 发送ALT键，ALT-%, CTRL-^, SHIFT-+, ENTER-~
    try:
        SetForegroundWindow(hwnd)
    except:
        pass


# 线程阻塞毫秒
def msleep(min_ms: int, max_ms=None):
    if max_ms is None:
        t_ms = min_ms
    else:
        t_ms = rnd(min_ms, max_ms)
    QThread.msleep(t_ms)

