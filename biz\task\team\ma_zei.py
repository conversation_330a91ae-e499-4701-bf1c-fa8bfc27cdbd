from biz.constants.constants import *
from biz.task.__base import TaskBase
from utils import *
from biz.obj.worker import Worker
import settings


class TaskMaZei(TaskBase):
    TASK_NAME = "挖宝马贼"
    IS_TEAM_TASK = True
    NEED_AVOID_FIGHT = True
    MONSTER_NAME = "马贼"

    @classmethod
    def run(cls, wk: Worker):
        if cls.cur_map(wk) in WILD_MAP_LIST:
            cls.fight_cur_map_monster(wk)
        cls.wa_bao(wk)
        if wk.cfg_plan_task["继续监听马贼"]:
            wk.record("挖宝完毕, 继续监听系统马贼消息...")
            while True:
                cls.fight_ma_zei(wk)
                msleep(2000)
                
    @classmethod
    def wa_bao(cls, wk: Worker):
        cls.open_bag_page(wk)
        cls.click_tidy_up(wk, timeout=300)
        last_use_time = 0
        fail_count = 0
        for i in range(10000000):
            if wk.is_fight:
                cls.fight_operation(wk)
                cls.do_fix_bb(wk)
            cls.fight_ma_zei(wk)
            if (i == 0 or wk.is_stuck) and (
                settings.cur_time_stamp - last_use_time
            ) > 3:
                wk.record("正在使用藏宝图...")
                cls.handle_pass_map(wk)
                if cls.bag_use_item(wk, "九州重宝图.bmp|存_超级藏宝图.bmp"):
                    fail_count = 0
                    last_use_time = settings.cur_time_stamp
                    wk.is_stuck = True
                    msleep(200)
                    cls.handle_pass_map(wk)
                    cls.close_pages(wk)
                else:
                    fail_count += 1
                    if fail_count >= 2:
                        wk.record("没有藏宝图了，挖宝结束")
                        break
            msleep(800)

    @classmethod
    def guard_do_something(cls, wk: Worker):
        x1, y1 = wk.get_str_pos(*RECT_SYSTEM_BROADCAST, "马贼团伙正在", COLOR_GREEN)
        if x1 < 0:
            return
        print(f"马贼团伙正在: {x1}, {y1}")
        x2, y2 = wk.get_str_pos(*RECT_SYSTEM_BROADCAST, "四处", COLOR_GREEN)
        if x2 < 0:
            return
        print(f"四处: {x2}, {y2}")
        ma_zei_map = wk.ocr(x1+82, y1-2, x2, y1+16, COLOR_GREEN).strip("_一灬")
        print(f"ma_zei_map: {ma_zei_map}")
        if not ma_zei_map or ma_zei_map not in WILD_MAP_LIST:
            return
        if ma_zei_map == wk.last_ma_zei_map:
            return
        wk.record(f"世界放马贼了: {ma_zei_map}")
        wk.last_ma_zei_map = ma_zei_map
        ma_zei_map_idx = WILD_MAP_LIST.index(ma_zei_map)
        low_map = wk.cfg_plan_task["打马贼地图低级"]
        high_map = wk.cfg_plan_task["打马贼地图高级"]
        low_map_idx = WILD_MAP_LIST.index(low_map)
        high_map_idx = WILD_MAP_LIST.index(high_map)
        if ma_zei_map_idx < low_map_idx or ma_zei_map_idx > high_map_idx:
            wk.record(f"{ma_zei_map}不在打马贼地图范围内:{low_map}-{high_map}, 跳过")
            return
        if ma_zei_map not in wk.ma_zei_map_set:
            wk.ma_zei_map_set.add(ma_zei_map)
            wk.record(f"目前监听到的马贼地图有: {wk.ma_zei_map_set}")
            msleep(1000)

    @classmethod
    def fight_ma_zei(cls, wk: Worker):
        if not wk.ma_zei_map_set:
            if rnd(0, 30) == 10:
                wk.record(f"正在等待世界放马贼...")
            return
        ma_zei_map = wk.ma_zei_map_set.pop()
        wk.record(f"正在前往地图: {ma_zei_map} 消灭马贼...")
        cls.use_xmx(wk)
        cls.run_to_map(wk, ma_zei_map)
        wk.record(f"到达{ma_zei_map}地图，开始打马贼...")
        if not cls.search_click_npc(wk, "马贼", input=False):
            wk.record("没有找到马贼")
            return
        cls.fight_cur_map_monster(wk)
        wk.record(f"地图{ma_zei_map}马贼消灭完毕")

    @classmethod
    def fight_cur_map_monster(cls, wk: Worker):
        enter_bull_fight = False
        try:
            for i in range(100000):
                if wk.is_fight:
                    cls.fight_operation(wk)
                    if enter_bull_fight:
                        wk.done_count += 1
                        wk.record(f"已完成 {wk.done_count} 次")
                        enter_bull_fight = False
                        i = 0  # 立刻触发地图找马贼
                cls.click_confirm(wk)
                if wk.find_str_click(*RECT_TALK, "进入战斗", COLOR_TALK_ITEM, timeout=400):  # 进到和马贼的对话
                    msleep(500)
                    enter_bull_fight = True
                else:  # 对话的不是马贼，优先在当前位置直接点马贼
                    cls.close_pages(wk)
                    if not cls.click_monster(wk):
                        # 当前位置没点到，就打开大地图找
                        if wk.is_stuck or i == 0:
                            if not cls.map_find_monster_click(wk):
                                break
                msleep(500)
        except Exception as e:
            wk.record(f"打马贼时出现异常:{e}")
            pass
             
            
    @classmethod
    def map_find_monster_click(cls, wk: Worker):
        cls.open_big_map(wk)
        # 先找到自己的位置
        cx, cy = wk.get_pic_pos(*RECT_FULL, "我的位置.bmp")
        if cx > 0:  # 找到自己的位置，则选离自己最近的马贼
            x, y = wk.find_pic_nearest_pos(*RECT_FULL, "明怪黄点.bmp", cx + 5, cy + 4)
            if x > 0 and (x, y) not in wk.exclude_pos:
                wk.record(f"找到了最近的马贼:{x},{y}")
                wk.move_click(x + 2, y + 3)
                wk.key_press(VK_TAB)  # 关闭大地图
                wk.exclude_pos.add((x, y))
                return True
        # 没找到的话就全图找马贼
        # [(0,333,444), (0, 444,555)]
        pos_list = wk.find_pic_ex(*RECT_FULL, "明怪黄点.bmp")
        if not pos_list:
            wk.key_press(VK_TAB)  # 关闭大地图
            return False
        # 然后排除掉任务NPC的坐标
        set1 = set([(x, y) for _, x, y in pos_list])
        set2 = wk.exclude_pos
        pos_list = list(set1 - set2)
        # 如果出了对话框
        if cls.is_talk_open(wk):
            return False
        # 然后从列表中随机取一个
        if len(pos_list):
            x, y = random.choice(pos_list)
            wk.record(f"随机选择了:{x},{y}")
            wk.move_click(x + 2, y + 4)
            wk.key_press(VK_TAB)  # 关闭大地图
            wk.exclude_pos.add((x, y))
            return True
        wk.record("地图上没找到...")
        return False
    
    @classmethod
    def get_dx(cls):
        return rnd(12, 16)
    
    @classmethod
    def get_dy(cls):
        return rnd(60, 90)

    @classmethod
    def click_monster(cls, wk: Worker):
        def get_bull_body_pos():
            x, y = wk.find_str_nearest_pos(
                *RECT_FIND, cls.MONSTER_NAME, COLOR_GOLD + "|" + COLOR_CYAN, cx=PEOPLE_NAME_X, cy=PEOPLE_NAME_Y, sim=0.9
            )
            if x < 0:
                return -1, -1
            return x + cls.get_dx(), y + cls.get_dy()

        cls.close_big_map(wk)
        for _ in range(20):
            if wk.is_fight or cls.is_talk_open(wk):
                return True
            x, y = get_bull_body_pos()
            if x > 0:
                wk.move_relative_click(x, y, re_move=False, limit_border=True, limit_y=Y_LIMIT_CLICK)
            if (x, y) == (-1, -1):
                wk.record("当前位置没找到...")
                return False
            msleep(50)
        return False
        
    @classmethod
    def get_default_biz_config(cls):
        return {
            "继续监听马贼": False,
            "打马贼地图低级": "碧峰峡",
            "打马贼地图高级": "秦陵二层",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_ma_zei_map1.setCurrentText(cls.CONFIG["打马贼地图低级"])
        settings.wnd_main.cmb_ma_zei_map2.setCurrentText(cls.CONFIG["打马贼地图高级"])
        settings.wnd_main.chk_ma_zei_continue.setChecked(cls.CONFIG["继续监听马贼"])
        
    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["打马贼地图低级"] = settings.wnd_main.cmb_ma_zei_map1.currentText()
        cls.CONFIG["打马贼地图高级"] = settings.wnd_main.cmb_ma_zei_map2.currentText()
        cls.CONFIG["继续监听马贼"] = settings.wnd_main.chk_ma_zei_continue.isChecked()
        super().cfg_save(plan_name)
