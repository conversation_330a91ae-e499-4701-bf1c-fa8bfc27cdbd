import copy
from PySide2.QtCore import QTime

from biz.task.__base import TaskBase
from biz.constants.constants import *
from biz.task.single.difficult_single import TaskDaNei
from utils import *
from biz.obj.worker import Worker


class TaskXiuLi(TaskBase):
	TASK_NAME = "修理忠诚"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		fix_method = wk.cfg_plan["修理忠诚方式"]
		wk.record(f"修理忠诚方式: {fix_method}")
		if fix_method == "会员快捷":
			cls.fix_bb_by_member(wk)
		else:
			cls.fix_equip(wk)
			cls.fix_bb(wk)


class TaskLeaveTeam(TaskBase):
	TASK_NAME = "离开队伍"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		wk.team.signal_leave_team.leader_set(True)
		cls.leave_team(wk)
		wk.team.signal_leave_team.mate_resp(wk.row)


class TaskDingShi(TaskBase):
	TASK_NAME = "定时启动"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		# 获取到当前是第几个定时启动
		start_time = cls.get_start_time(wk)
		wk.record(f"等待 {start_time} 启动...")
		while settings.cur_time_fmt < start_time:
			if rnd(0, 10) == 1:
				wk.record(f"等待 {start_time} 启动...")
			msleep(1000)

	@classmethod
	def get_start_time(cls, wk: Worker):
		task_list = wk.cfg_plan["执行列表"]
		di_ji_ge = 0  # 第几个默认是第0个
		for idx, task_name in enumerate(task_list):
			if task_name == cls.TASK_NAME:
				di_ji_ge += 1
			if idx >= wk.cur_task_idx:
				break
		start_time = wk.cfg_plan_task.get(f"启动时间{di_ji_ge}", "00:00")
		print(f"第{di_ji_ge}个定时启动, 启动时间:",
			  wk.cfg_plan_task.get(f"启动时间{di_ji_ge}", "00:00"))
		return start_time

	@classmethod
	def get_default_biz_config(cls):
		return {
			"启动时间1": "14:04",
			"启动时间2": "19:31",
			"启动时间3": "22:30",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):
		super().cfg_read(plan_name)
		settings.wnd_main.tedt_timer_run1.setTime(
			QTime.fromString(cls.CONFIG["启动时间1"], "HH:mm"))
		settings.wnd_main.tedt_timer_run2.setTime(
			QTime.fromString(cls.CONFIG["启动时间2"], "HH:mm"))
		settings.wnd_main.tedt_timer_run3.setTime(
			QTime.fromString(cls.CONFIG["启动时间3"], "HH:mm"))

	@classmethod
	def cfg_save(cls, plan_name: str):
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["启动时间1"] = settings.wnd_main.tedt_timer_run1.time().toString(
			"HH:mm")
		cls.CONFIG["启动时间2"] = settings.wnd_main.tedt_timer_run2.time().toString(
			"HH:mm")
		cls.CONFIG["启动时间3"] = settings.wnd_main.tedt_timer_run3.time().toString(
			"HH:mm")
		super().cfg_save(plan_name)


class TaskSwitchLine(TaskBase):
	TASK_NAME = "换线"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		line = wk.cfg_plan_task["线路"]
		wk.team.signal_switch_line.leader_set(line)
		cls.switch_line(wk, line)
		wk.team.signal_switch_line.mate_resp(wk.row)
		wk.record("等待中...")
		msleep(2500)

	@classmethod
	def get_default_biz_config(cls):
		return {
			"线路": "一",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.cmb_switch_line.setCurrentText(cls.CONFIG["线路"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["线路"] = settings.wnd_main.cmb_switch_line.currentText()
		super().cfg_save(plan_name)


class TaskMakeTeam(TaskBase):
	TASK_NAME = "组队"
	IS_TEAM_TASK = True
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.make_team(wk)

	@classmethod
	def after_run(cls, wk: Worker):
		cls.wait_mate_return_team(wk)
		super().after_run(wk)



class TaskBuy(TaskBase):
	TASK_NAME = "购买物品"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		# 先整理背包, 识别下存量, 计算差量
		wk.record("正在计算背包已有的消耗品数量...")
		num_xbz, num_kls, num_xmx, num_bcml, num_hslh, num_red, num_blue = cls.get_things_exist_count(
			wk)
		wk.record(
			f"背包中现有 熊猫香{num_xmx}个, 百草蜜酿{num_bcml}个, 贺岁礼花{num_hslh}个, 4000红药{num_red}个, 4000蓝药{num_blue}个, 玄冰针{num_xbz}个, 狂乱散{num_kls}个")
		if wk.cfg_plan_task["熊猫香"]:
			num = wk.cfg_plan_task["熊猫香数量"] - num_xmx
			if num > 0:
				wk.record(f"购买熊猫香{num}个...")
				cls.buy_thing(wk, "熊猫香", num)
		if wk.cfg_plan_task["百草蜜酿"]:
			num = wk.cfg_plan_task["百草蜜酿数量"] - num_bcml
			if num > 0:
				wk.record(f"购买百草蜜酿{num}个...")
				cls.buy_thing(wk, "百草蜜酿", num)
		if wk.cfg_plan_task["玄冰针"]:
			num = wk.cfg_plan_task["玄冰针数量"] - num_xbz
			if num > 0:
				wk.record(f"购买玄冰针{num}个...")
				cls.buy_thing(wk, "玄冰针", num)
		if wk.cfg_plan_task["4000红药"]:
			num = wk.cfg_plan_task["4000红药数量"] - num_red
			if num > 0:
				wk.record(f"购买4000红药{num}个...")
				cls.buy_thing(wk, "4000红药", num)
		if wk.cfg_plan_task["4000蓝药"]:
			num = wk.cfg_plan_task["4000蓝药数量"] - num_blue
			if num > 0:
				wk.record(f"购买4000蓝药{num}个...")
				cls.buy_thing(wk, "4000蓝药", num)
		cls.close_pages(wk)
		if wk.cfg_plan_task["贺岁礼花"]:
			num = wk.cfg_plan_task["贺岁礼花数量"] - num_hslh
			if num > 0:
				wk.record(f"购买贺岁礼花{num}个...")
				cls.buy_thing(wk, "贺岁礼花", num)
		if wk.cfg_plan_task["狂乱散"] and wk.find_pic(*RECT_SKILL, "技能图标_狮吼.bmp"):
			num = wk.cfg_plan_task["狂乱散数量"] - num_kls
			if num > 0:
				wk.record(f"购买狂乱散{num}个...")
				cls.buy_thing(wk, "狂乱散", num)
		cls.close_pages(wk)
		if wk.cfg_plan_task["桃子"]:
			num = wk.cfg_plan_task["桃子数量"]
			wk.record(f"购买桃子{num}个...")
			cls.buy_thing(wk, "桃子", num)

	@classmethod
	def get_things_exist_count(cls, wk: Worker):
		cls.open_bag_page(wk)
		wk.re_move()  # 鼠标移开避免挡到
		x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
		if x < 0:
			wk.record("背包页面未打开, 统计消耗品数量失败")
			return
		begin_x, begin_y = x + 18, y - 300
		RECT_BAG = (begin_x, begin_y, begin_x + 260, begin_y + 300)
		num_xbz, num_kls, num_xmx, num_bcml, num_hslh, num_red, num_blue = cls.get_cur_bag_page_thing_count(
			wk)
		pos_list = cls.get_bag_page_pos_list(wk, RECT_BAG)
		for pos in pos_list:
			_, x, y = pos
			wk.move_click(x, y)  # 换页
			msleep(600)
			num_xbz2, num_kls2, num_xmx2, num_bcml2, num_hslh2, num_red2, num_blue2 = cls.get_cur_bag_page_thing_count(
				wk, timeout=300)
			num_xbz += num_xbz2
			num_kls += num_kls2
			num_xmx += num_xmx2
			num_bcml += num_bcml2
			num_hslh += num_hslh2
			num_red += num_red2
			num_blue += num_blue2
		cls.close_pages(wk)
		return num_xbz, num_kls, num_xmx, num_bcml, num_hslh, num_red, num_blue

	@classmethod
	def get_cur_bag_page_thing_count(cls, wk: Worker, timeout=0):
		num_xbz, num_kls, num_xmx, num_bcml, num_hslh, num_red, num_blue = 0, 0, 0, 0, 0, 0, 0
		thing_pic = "玄冰针.bmp|狂乱散.bmp|熊猫香.bmp|百草蜜酿.bmp|贺岁礼花.bmp|4000红药.bmp|4000蓝药.bmp"
		pos_list = wk.find_pic_ex(*RECT_RIGHT, thing_pic, timeout=timeout)
		for idx, x, y in pos_list:
			num_str = wk.ocr(x, y, x+50, y+50, COLOR_WHITE, zk=ZK_DIGIT_9)
			if idx == 0:
				num_xbz += int(num_str or 1)
			elif idx == 1:
				num_kls += int(num_str or 1)
			elif idx == 2:
				num_xmx += int(num_str or 1)
			elif idx == 3:
				num_bcml += int(num_str or 1)
			elif idx == 4:
				num_hslh += int(num_str or 1)
			elif idx == 5:
				num_red += int(num_str or 1)
			elif idx == 6:
				num_blue += int(num_str or 1)
		return num_xbz, num_kls, num_xmx, num_bcml, num_hslh, num_red, num_blue

	@classmethod
	def get_default_biz_config(cls):
		return {
			"熊猫香": False,
			"熊猫香数量": 99,
			"百草蜜酿": False,
			"百草蜜酿数量": 30,
			"贺岁礼花": False,
			"贺岁礼花数量": 198,
			"玄冰针": False,
			"玄冰针数量": 396,
			"狂乱散": False,
			"狂乱散数量": 396,
			"桃子": False,
			"桃子数量": 1000,
			"4000红药": False,
			"4000红药数量": 396,
			"4000蓝药": False,
			"4000蓝药数量": 396,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.chk_buy_xmx.setChecked(cls.CONFIG["熊猫香"])
		settings.wnd_main.edt_buy_xmx.setText(str(cls.CONFIG["熊猫香数量"]))
		settings.wnd_main.chk_buy_bcml.setChecked(cls.CONFIG["百草蜜酿"])
		settings.wnd_main.edt_buy_bcml.setText(str(cls.CONFIG["百草蜜酿数量"]))
		settings.wnd_main.chk_buy_hslh.setChecked(cls.CONFIG["贺岁礼花"])
		settings.wnd_main.edt_buy_hslh.setText(str(cls.CONFIG["贺岁礼花数量"]))
		settings.wnd_main.chk_buy_xbz.setChecked(cls.CONFIG["玄冰针"])
		settings.wnd_main.edt_buy_xbz.setText(str(cls.CONFIG["玄冰针数量"]))
		settings.wnd_main.chk_buy_kls.setChecked(cls.CONFIG["狂乱散"])
		settings.wnd_main.edt_buy_kls.setText(str(cls.CONFIG["狂乱散数量"]))
		settings.wnd_main.chk_buy_peach.setChecked(cls.CONFIG["桃子"])
		settings.wnd_main.edt_buy_peach.setText(str(cls.CONFIG["桃子数量"]))
		settings.wnd_main.chk_buy_red.setChecked(cls.CONFIG["4000红药"])
		settings.wnd_main.edt_buy_red.setText(str(cls.CONFIG["4000红药数量"]))
		settings.wnd_main.chk_buy_blue.setChecked(cls.CONFIG["4000蓝药"])
		settings.wnd_main.edt_buy_blue.setText(str(cls.CONFIG["4000蓝药数量"]))

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["熊猫香"] = settings.wnd_main.chk_buy_xmx.isChecked()
		cls.CONFIG["熊猫香数量"] = int(settings.wnd_main.edt_buy_xmx.text())
		cls.CONFIG["百草蜜酿"] = settings.wnd_main.chk_buy_bcml.isChecked()
		cls.CONFIG["百草蜜酿数量"] = int(settings.wnd_main.edt_buy_bcml.text())
		cls.CONFIG["贺岁礼花"] = settings.wnd_main.chk_buy_hslh.isChecked()
		cls.CONFIG["贺岁礼花数量"] = int(settings.wnd_main.edt_buy_hslh.text())
		cls.CONFIG["玄冰针"] = settings.wnd_main.chk_buy_xbz.isChecked()
		cls.CONFIG["玄冰针数量"] = int(settings.wnd_main.edt_buy_xbz.text())
		cls.CONFIG["狂乱散"] = settings.wnd_main.chk_buy_kls.isChecked()
		cls.CONFIG["狂乱散数量"] = int(settings.wnd_main.edt_buy_kls.text())
		cls.CONFIG["桃子"] = settings.wnd_main.chk_buy_peach.isChecked()
		cls.CONFIG["桃子数量"] = int(settings.wnd_main.edt_buy_peach.text())
		cls.CONFIG["4000红药"] = settings.wnd_main.chk_buy_red.isChecked()
		cls.CONFIG["4000红药数量"] = int(settings.wnd_main.edt_buy_red.text())
		cls.CONFIG["4000蓝药"] = settings.wnd_main.chk_buy_blue.isChecked()
		cls.CONFIG["4000蓝药数量"] = int(settings.wnd_main.edt_buy_blue.text())
		super().cfg_save(plan_name)


class TaskTerseBB(TaskBase):
	TASK_NAME = "精炼随从"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.terse_bb(wk)


class TaskBianXunMa(TaskBase):
	TASK_NAME = "编驯马绳"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.bian_xun_ma(wk)


class TaskJieRi(TaskBase):
	TASK_NAME = "节日礼品"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.back_to_kai_feng(wk)
		wk.record("正在前往领取节日礼品...")
		festival = "新年前夕|元旦|除夕|平安夜|立春|雨水|惊蛰|春分|清明|谷雨|立夏|小满|芒种|夏至|小暑|大暑|立秋|白露|秋分|寒露|霜降|立冬|小雪|大雪|冬至|小寒|大寒|节|日"
		cls.talk_with_cur_map_npc(wk, "节日使者", [festival, "领取礼品"])


class TaskDaNeiLingJiang(TaskBase):
	TASK_NAME = "大内领奖"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.get_da_nei_award(wk)


class TaskLingFeiYu(TaskBase):
	TASK_NAME = "领取飞羽"
	IS_FREE = True
	RECT_SHUN_FENG_ER = (383, 216, 547, 327)
	POS_SHUN_FENG_ER = (652, 273)

	@classmethod
	def run(cls, wk: Worker):
		cls.pause_leave_team(wk)
		cls.back_to_kai_feng(wk)
		if cls.cur_map(wk) != "开封":
			wk.record("回到开封失败, 自动结束")
			return
		wk.record("寻路到飞羽使者...")
		if not cls.go_to_fei_yu_shi_zhe(wk):
			wk.record("寻路到飞羽使者失败")
			return
		wk.record("寻路到飞羽使者成功")
		color = COLOR_GREEN + "|" + COLOR_CYAN
		for name in ["千里眼", "顺风耳", "会先驱"]:
			msleep(600)
			wk.record(f"正在找 飞羽{name} 接任务...")
			dx, dy = rnd(6, 7), rnd(60, 80)
			x, y = wk.get_str_pos(*RECT_FULL, name, color, timeout=600)
			if x > 0:
				wk.move_relative_click(x+dx, y+dy)
				msleep(600)
				if cls.talk_click_specify_item(wk, "领取任务"):
					msleep(600)
					cls.click_confirm(wk, timeout=600)
					for _ in range(20):
						msleep(500)
						if cls.is_talk_open(wk):
							wk.record(f"从 飞羽{name} 处接取任务成功!")
							msleep(400)  # 有人电脑卡, 多等会儿
							cls.close_other_talk(wk)
							break
					continue
			wk.record(f"从 飞羽{name} 处接取任务失败!")
		cls.back_to_team(wk)

	@classmethod
	def go_to_fei_yu_shi_zhe(cls, wk: Worker):
		color = COLOR_GREEN + "|" + COLOR_CYAN
		for i in range(200):
			if i == 0 or wk.is_stuck:
				if wk.find_str(*cls.RECT_SHUN_FENG_ER, "飞羽顺风耳", color):
					return True
				cls.big_map_click(wk, *cls.POS_SHUN_FENG_ER)
				cls.close_other_talk(wk)
			msleep(600)
		return False


class TaskPayFine(TaskBase):
	TASK_NAME = "交罚款"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.pay_fine(wk)


class TaskOffRide(TaskBase):
	TASK_NAME = "卸下坐骑"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		wk.team.signal_off_ride.leader_set()
		wk.team.signal_off_ride.mate_resp(wk.row)
		cls.stop_auto_find_way(wk, force=True)
		cls.off_zuo_qi(wk)
		cls.off_nei_gong_jue_yi(wk)
		cls.rest_bb(wk)

	@classmethod
	def off_zuo_qi(cls, wk: Worker, try_again=True):
		if not wk.cfg_plan_task["卸坐骑"]:
			wk.record("未配置卸坐骑")
			return
		wk.record("正在卸下坐骑...")
		cls.off_ride(wk)
		cls.open_bag_page(wk)
		x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
		if x < 0:
			wk.record("背包打开失败, 卸下坐骑失败")
			return
		if x-191 < 5:
			wk.move_drag_to(x, y, x+300, y)
			msleep(500)
			x, y = wk.get_pic_pos(*RECT_FULL, "界_背包.bmp")
		wk.move_r_click(x-191, y-43)
		if cls.click_confirm(wk, timeout=300):
			wk.record(f"卸下坐骑失败, 重新尝试")
			if try_again:
				cls.off_zuo_qi(wk, try_again=False)
		else:
			wk.record("卸下坐骑成功")
		cls.close_page(wk)

	@classmethod
	def off_nei_gong_jue_yi(cls, wk: Worker):
		if not wk.cfg_plan_task["卸内功绝艺"]:
			wk.record("未配置卸内功绝艺")
			return
		wk.record("正在卸下内功绝艺...")
		cls.open_fight_page(wk)
		if wk.find_str(*RECT_FULL, "黑暗气诀", COLOR_WHITE):
			wk.record("发现黑暗气诀, 明教号不需要卸下内功绝艺")
			wk.key_press(VK_ESC)
			cls.close_pages(wk)
			return
		x, y = wk.get_pic_pos(*RECT_FULL, "内功.bmp")
		if x < 0:
			wk.record("战斗页打开失败, 卸内功绝艺失败")
			return
		# 卸内功
		if not wk.find_str(x, y-2, x+100, y+22, "无", COLOR_DISABLE):
			wk.move_click(x + 70, y + 5)
			msleep(400)
			wk.move_click(x - 50, y + 7)
		# 卸绝艺
		if not wk.find_str(x, y+18, x+100, y+48, "无", COLOR_DISABLE):
			wk.move_click(x + 70, y + 33)
			msleep(400)
			wk.move_click(x - 50, y + 18)
		wk.key_press(VK_ESC)
		cls.close_pages(wk)
		wk.record("卸内功绝艺成功")

	@classmethod
	def rest_bb(cls, wk: Worker):
		if not wk.cfg_plan_task["宝宝休息"]:
			wk.record("未配置宝宝休息")
			return
		wk.record("正在让宝宝休息...")
		cls.open_bb_page(wk)
		wk.find_pic_click(*RECT_FULL, "bb休息.bmp", timeout=400)
		cls.close_pages(wk)
		wk.record("宝宝休息成功")

	@classmethod
	def get_default_biz_config(cls):
		return {
			"卸内功绝艺": True,
			"宝宝休息": True,
			"卸坐骑": True,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.chk_off_nei_gong.setChecked(cls.CONFIG["卸内功绝艺"])
		settings.wnd_main.chk_off_bb.setChecked(cls.CONFIG["宝宝休息"])
		settings.wnd_main.chk_off_ride.setChecked(cls.CONFIG["卸坐骑"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["卸内功绝艺"] = settings.wnd_main.chk_off_nei_gong.isChecked()
		cls.CONFIG["宝宝休息"] = settings.wnd_main.chk_off_bb.isChecked()
		cls.CONFIG["卸坐骑"] = settings.wnd_main.chk_off_ride.isChecked()
		super().cfg_save(plan_name)


class TaskOnRide(TaskOffRide):
	TASK_NAME = "装备坐骑"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		wk.team.signal_on_ride.leader_set()
		wk.team.signal_on_ride.mate_resp(wk.row)
		cls.on_zuo_qi(wk)
		cls.on_nei_gong_jue_yi(wk)
		cls.let_bb_fight(wk)
		cls.on_ride(wk)
		wk.record("装备坐骑完成")

	@classmethod
	def on_zuo_qi(cls, wk: Worker):
		if not wk.cfg_plan_task["卸坐骑"]:
			wk.record("未配置装备坐骑")
			return
		pics = wk.match_pic("坐骑_*.bmp")
		if cls.bag_use_item(wk, pics, item_name="坐骑"):
			wk.have_ride = True
		cls.click_confirm(wk)

	@classmethod
	def on_nei_gong_jue_yi(cls, wk: Worker):
		if not wk.cfg_plan_task["卸内功绝艺"]:
			wk.record("未配置上内功绝艺")
			return
		wk.record("正在上内功绝艺...")
		cls.open_fight_page(wk)
		if wk.find_str(*RECT_FULL, "黑暗气诀", COLOR_WHITE):
			wk.record("发现黑暗气诀, 明教号不需要上内功绝艺")
			wk.key_press(VK_ESC)
			cls.close_pages(wk)
			return
		x, y = wk.get_pic_pos(*RECT_FULL, "内功.bmp")
		if x < 0:
			wk.record("战斗页打开失败, 上内功绝艺失败")
			return
		# 上内功
		if wk.find_str(x, y-2, x+100, y+22, "无", COLOR_DISABLE):
			wk.record("内功未配置, 正在上内功")
			wk.move_click(x + 70, y + 5)
			msleep(400)
			if cls.click_confirm(wk):
				wk.record("上内功失败, 弹窗报错了")
			elif wk.find_color_click(x-70, y+20, x-56, y+116, COLOR_ENABLE, order=ORDER_RLDU):
				wk.record("上内功成功")
		# 上绝艺
		if wk.find_str(x, y+18, x+100, y+48, "无", COLOR_DISABLE):
			wk.record("绝艺未配置, 正在上绝艺")
			wk.move_click(x + 70, y + 33)
			msleep(400)
			if cls.click_confirm(wk):
				wk.record("上绝艺失败, 弹窗报错了")
			elif wk.find_color_click(x-70, y+28, x-56, y+124, COLOR_ENABLE, order=ORDER_RLDU):
				wk.record("上绝艺成功")
		wk.key_press(VK_ESC)
		cls.close_pages(wk)
		wk.record("上内功绝艺完成")

	@classmethod
	def let_bb_fight(cls, wk: Worker):
		if not wk.cfg_plan_task["宝宝休息"]:
			wk.record("未配置宝宝休息, 则不出战BB")
			return
		wk.record("宝宝出战中...")
		cls.switch_primary_bb(wk)
		wk.record("宝宝出战完成")


class Taskxxx(TaskBase):
	TASK_NAME = "喂养坐骑"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.close_pages(wk)
		msleep(400)
		wk.key_press_combo(VK_ALT, VK_C)
		msleep(400)
		wk.find_pic_click(*RECT_FULL, "人物坐骑页.bmp", timeout=600)
		msleep(400)
		if wk.find_pic_click(*RECT_FULL, "喂养.bmp", timeout=600):
			wk.record("喂养坐骑成功")
		else:
			wk.record("喂养坐骑失败")
		msleep(200)
		cls.on_ride(wk)


class TaskFindWayTalk(TaskBase):
	TASK_NAME = "寻路对话"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		map_name = wk.cfg_plan_task["地图"]
		npc_name = wk.cfg_plan_task["NPC名称"]
		talk_content = wk.cfg_plan_task["对话内容"]
		talk_content_list = talk_content.split("-")
		cls.run_to_map(wk, map_name)
		if map_name != "当前地图" and map_name != "帮会领地" and cls.cur_map(wk) != map_name:
			wk.record(f"当前地图不是 {map_name}, 无法寻路对话")
			return
		wk.record(f"寻路到{npc_name}对话...")
		if cls.find_way_npc(wk, npc_name, talk_content_list, close_talk=False, close_find_way=False, reply_first=True):
			wk.record("寻路对话成功")
			cls.click_confirm(wk, RECT=RECT_POPUP)

	@classmethod
	def after_run(cls, wk: Worker):
		msleep(500)  # 不要把界面关掉了
		wk.record("执行完成")

	@classmethod
	def get_default_biz_config(cls):
		return {
			"地图": "开封",
			"NPC名称": "吉禾",
			"对话内容": "领取奖励",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.cmb_find_way_talk_map.setCurrentText(
			cls.CONFIG["地图"])
		settings.wnd_main.edt_find_way_talk_npc_name.setText(
			cls.CONFIG["NPC名称"])
		settings.wnd_main.edt_find_way_talk_content.setText(cls.CONFIG["对话内容"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["地图"] = settings.wnd_main.cmb_find_way_talk_map.currentText()
		cls.CONFIG["NPC名称"] = settings.wnd_main.edt_find_way_talk_npc_name.text()
		cls.CONFIG["对话内容"] = settings.wnd_main.edt_find_way_talk_content.text()
		super().cfg_save(plan_name)


class TaskOnShelf(TaskBase):
	TASK_NAME = "上架寄售"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.open_ji_shou_page(wk)
		if not cls.is_ji_shou_open(wk):
			wk.record("寄售页面打开失败")
			return
		wk.move_click(*POS_JI_SHOU_MY)
		msleep(600)
		cls.continue_ji_shou(wk)
		cls.on_shelf_goods(wk)
		msleep(600)
		cls.close_pages(wk)
		wk.key_press(VK_ESC)
		msleep(600)
		cls.close_pages(wk)


	@classmethod
	def continue_ji_shou(cls, wk: Worker):
		# 点击临时仓库, 一键寄售
		wk.record("正在续寄售商品...")
		wk.move_click(*POS_JI_SHOU_TMP_DEPOT)
		msleep(600)
		wk.move_click(*POS_JI_SHOU_ONE_KEY)
		msleep(600)
		cls.click_confirm(wk)  # 关掉 寄售成功 提示框
		msleep(600)
		
		wk.record("续寄售商品完成")

	@classmethod
	def on_shelf_goods(cls, wk: Worker):
		shelf_pic = wk.cfg_plan_task["上架图片名"]
		if not shelf_pic:
			wk.record("当前方案没有勾选任何商品, 不做上架")
			return
		wk.record("切换到 在售物品 开始上架...")
		wk.move_click(*POS_JI_SHOU_ZAI_SHOU)
		msleep(600)
		if not wk.find_str(*RECT_FULL, "剩余可寄售数量", COLOR_BLACK, timeout=600):
			wk.record("切换寄售页失败")
			return
		wk.move_click(*POS_JI_SHOU_JI_SHOU)
		msleep(600)
		try:
			cls.do_on_shelf(wk, shelf_pic)
			wk.record("上架商品成功, 未达到寄售上限")
		except:
			wk.record("上架商品完成, 达到了寄售上限")

	@classmethod
	def do_on_shelf(cls, wk: Worker, shelf_pic):
		"""
		right_click_cur_page_things 
		返回True代表刚刚成功上架了商品, 那我们要从头开始遍历背包页
		返回False代表这页没找到, 则换页, 如果遍历完了, 才结束
		"""
		pos_list = cls.get_bag_page_pos_list(
			wk, RECT_JI_SHOU_GOODS, timeout=200)
		if cls.right_click_cur_page_things(wk, shelf_pic):
			return cls.do_on_shelf(wk, shelf_pic)
		for pos in pos_list:
			_, x, y = pos
			wk.move_click(x, y)  # 换页
			msleep(400)
			if cls.right_click_cur_page_things(wk, shelf_pic):
				return cls.do_on_shelf(wk, shelf_pic)

	@classmethod
	def right_click_do_something(cls, wk: Worker, RECT=RECT_SELL_THING):
		if cls.is_popup_show_info(wk, "数量已达到上限", timeout=200):
			wk.record("上架数量已达上限")
			cls.click_confirm(wk)
			raise Exception("上架数量已达上限")
		cls.close_other_talk(wk)
		NEW_X, NEW_Y = 26, 190
		wk.find_pic_drag_to(*RECT_FULL, "寄售数量.bmp",
							new_x=NEW_X, new_y=NEW_Y, timeout=300)
		x, y = wk.get_pic_pos(*RECT_FULL, "寄售数量.bmp")
		if (x, y) != (NEW_X, NEW_Y):
			wk.record("价格输入框位置不对")
			return False
		goods_name = wk.ocr(*RECT_SELL_GOODS_NAME, COLOR_WHITE, timeout=200)
		if not goods_name or goods_name not in wk.cfg_plan_task["上架物品名列表"]:
			wk.record(f"商品名:{goods_name} 未配置在物价表中或未选择上架!")
			return
		price = cls.get_goods_price(wk, goods_name)
		if price == 0:
			wk.record(f"商品名:{goods_name} 价格配置错误!")
			return
		goods_num = wk.ocr(*RECT_SELL_GOODS_NUM, COLOR_WHITE,
						   zk=ZK_DIGIT_11, timeout=200)
		wk.record(f"上架的商品为 {goods_name}, 单价:{price}, 数量:{goods_num}")
		# 输入价格
		wk.move_click(*POS_JI_SHOU_SINGLE_PRICE)
		wk.key_press(VK_BACK, 10)
		msleep(600)
		wk.send_string(str(price))
		msleep(600)
		wk.move_click(*POS_JI_SHOU_CONFIRM1)
		msleep(600)
		wk.move_click(*POS_JI_SHOU_CONFIRM2)
		msleep(600)
		cls.click_confirm(wk, timeout=600)  # 首次都要收10000寄售费
		msleep(600)
		cls.click_confirm(wk, timeout=600)  # 寄售成功的确认
		msleep(800)
		wk.move_click(*POS_JI_SHOU_JI_SHOU)  # 再点击寄售, 继续下一轮
		msleep(800)
		return True

	@classmethod
	def get_default_biz_config(cls):
		return {
			"上架图片名": "",
			"上架物品名列表": [],
			# 这个才是用来配置的, 上面的是方便业务逻辑取用才保存的, 跟控件无关
			"上架行列表": [],
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		tbe_price = settings.wnd_main.tbe_price
		for row in range(tbe_price.rowCount()):
			chk_on_shelf = tbe_price.cellWidget(row, COL_GOODS_ONSHELF)
			if row in cls.CONFIG["上架行列表"]:
				chk_on_shelf.setChecked(True)
			else:
				chk_on_shelf.setChecked(False)

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		tbe_price = settings.wnd_main.tbe_price
		cls.CONFIG["上架行列表"] = []
		pic_names = []
		goods_names = []
		for row in range(tbe_price.rowCount()):
			chk_on_shelf = tbe_price.cellWidget(row, COL_GOODS_ONSHELF)
			if chk_on_shelf.isChecked():
				name = tbe_price.item(row, COL_GOODS_NAME).text()
				if not name:
					continue
				cls.CONFIG["上架行列表"].append(row)
				goods_names.append(name)
				pic_name = settings.global_wk.match_pic(f"*{name}.bmp")
				if pic_name:
					pic_names.append(pic_name)
				else:
					pic_names.append(f"{name}.bmp")
		cls.CONFIG["上架图片名"] = "|".join(pic_names)
		cls.CONFIG["上架物品名列表"] = goods_names
		# print("上架图片名:", cls.CONFIG["上架图片名"])
		super().cfg_save(plan_name)


class TaskShouShi(TaskBase):
	TASK_NAME = "打造首饰"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		wk.record("正在打开首饰页...")
		if not cls.open_shoushi_page(wk):
			return
		item = "打造" + wk.cfg_plan_task["类型"]
		if not wk.find_str_click(*RECT_LEFT, item, COLOR_BLACK, timeout=400):
			wk.record(f"点击 {item} 失败")
			cls.close_pages(wk)
			return
		for i in range(10000):
			msleep(800)
			if not cls.da_zao(wk):
				wk.record("打造首饰失败")
				cls.close_pages(wk)
				return
		cls.close_pages(wk)

	@classmethod
	def da_zao(cls, wk: Worker):
		x, y = wk.get_str_pos(*RECT_LEFT, "首饰", COLOR_BLACK,
							  zk=ZK_BOLD_11, timeout=400)
		if x < 0:
			wk.record("定位背包页失败")
			return False
		RECT_BAG = (x + 436, y - 20, x + 661, y + 269)
		# 放主材
		wk.record("放入主材...")
		zhucai = wk.cfg_plan_task["主材"]
		zhucai_pic = wk.match_pic(f"*{zhucai}.bmp")
		for i in range(5):
			if not wk.find_pic_r_click(*RECT_BAG, zhucai_pic, timeout=400):
				wk.record(f"放主材 {zhucai} 失败, 未找到")
				return False
			msleep(600)
		# 放辅材
		wk.record("放入辅材...")
		fucai = wk.cfg_plan_task["辅材"]
		fucai_pic = wk.match_pic(f"*{fucai}*.bmp")
		for _ in range(5):
			if not wk.find_pic_r_click(*RECT_BAG, fucai_pic, timeout=400):
				wk.record(f"放辅材 {fucai} 失败, 未找到")
				return False
			msleep(600)
		# 确认打造
		wk.record("确认打造...")
		cls.click_confirm(wk)
		return True

	@classmethod
	def get_default_biz_config(cls):
		return {
			"类型": "玉佩",
			"主材": "天蚕丝",
			"辅材": "紫水晶",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_shoushi, cls.CONFIG["类型"]
		)
		settings.wnd_main.cmb_shoushi_zhucai.setCurrentText(cls.CONFIG["主材"])
		settings.wnd_main.cmb_shoushi_fucai.setCurrentText(cls.CONFIG["辅材"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["类型"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_shoushi
		)
		cls.CONFIG["主材"] = settings.wnd_main.cmb_shoushi_zhucai.currentText()
		cls.CONFIG["辅材"] = settings.wnd_main.cmb_shoushi_fucai.currentText()
		super().cfg_save(plan_name)


class TaskJiShouBuy(TaskBase):
	TASK_NAME = "寄售买物"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		thing_name = wk.cfg_plan_task["物品名"]
		thing_num = wk.cfg_plan_task["数量"]
		wk.record(f"寄售买物 {thing_name} {thing_num} 件...")
		cls.ji_shou_buy_thing(wk, thing_name, thing_num)

	@classmethod
	def get_default_biz_config(cls):
		return {
			"物品名": "[极品]养",
			"数量": 99,
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		settings.wnd_main.edt_ji_shou.setText(cls.CONFIG["物品名"])
		settings.wnd_main.spin_count_ji_shou.setValue(cls.CONFIG["数量"])

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["物品名"] = settings.wnd_main.edt_ji_shou.text()
		cls.CONFIG["数量"] = settings.wnd_main.spin_count_ji_shou.value()
		super().cfg_save(plan_name)


class TaskQianDao(TaskBase):
	TASK_NAME = "签到祥瑞"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		wk.team.signal_get_gift.leader_set()
		if cls.is_gift_enable(wk):
			cls.get_gift(wk)
		if cls.is_fest_enable(wk):
			cls.get_fest(wk)


class TaskQuitGame(TaskBase):
	TASK_NAME = "退出游戏"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.exit_game(wk)

	@classmethod
	def after_run(cls, wk: Worker):
		pass


class TaskKeep(TaskBase):
	TASK_NAME = "持续鸣笛"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		while True:
			wk.beep()
			msleep(1000)

	@classmethod
	def after_run(cls, wk: Worker):
		pass


class TaskXueDao(TaskBase):
	TASK_NAME = "金针穴道"
	HUO_POS = [
		(162, 261), (183, 276), (198, 296), (205, 322), (203, 350),
		(199, 377), (186, 405), (164, 425), (128, 432), (113, 407),
		(117, 373), (134, 342), (126, 306), (96, 327), (75, 370),
		(84, 394), (75, 422), (51, 441), (43, 471), (75, 486),
		(106, 456), (142, 468), (179, 490)
	]
	TU_POS = [
		(74, 437), (59, 413), (59, 384), (73, 357), (77, 320),
		(54, 298), (54, 272), (90, 267), (104, 300), (95, 338),
		(106, 372), (147, 368), (162, 347)
	]
	COLOR_EMPTY = "ceb78d"

	@classmethod
	def run(cls, wk: Worker):
		if not wk.find_pic_click(*RECT_FULL, "穴道2.bmp"):
			if not wk.find_pic_click(*RECT_FULL, "穴道.bmp"):
				wk.key_press_combo(VK_ALT, VK_C)
				msleep(600)
				wk.find_pic_click(*RECT_FULL, "穴道.bmp", timeout=600)
			msleep(600)
		wk.find_pic_drag_to(*RECT_FULL, "界_穴道.bmp", 135, 104)
		msleep(600)
		wk.find_pic_click(*RECT_FULL, "火脉.bmp|穴道火.bmp", timeout=600)
		msleep(600)
		if not wk.find_str(*RECT_FULL, "穴道点数", COLOR_WHITE, timeout=600):
			wk.record("进入穴道打通界面失败")
			return
		for pos in cls.HUO_POS:
			for i in range(5):
				cls.close_other_talk(wk)
				pos_color = wk.get_pos_color(*pos)
				if color_in_range(pos_color, cls.COLOR_EMPTY, 0x25):  # 说明这穴道还没点
					wk.record("点穴道...")
					wk.move_click(*pos)
					msleep(400)
					if cls.is_popup_show_info(wk, "需要消耗"):
						wk.record("金针不足")
						return
					cls.click_confirm(wk)
					msleep(400)
				else:
					break
		if not wk.find_pic_click(*RECT_FULL, "穴道土.bmp", timeout=600):
			wk.record("切换到穴道土失败")
			return
		wk.record("切换到穴道土成功")
		msleep(600)
		if not wk.find_str(*RECT_FULL, "穴道点数", COLOR_WHITE, timeout=600):
			wk.record("进入穴道打通界面失败")
			return
		for pos in cls.TU_POS:
			for i in range(5):
				cls.close_other_talk(wk)
				pos_color = wk.get_pos_color(*pos)
				if color_in_range(pos_color, cls.COLOR_EMPTY, 0x25):  # 说明这穴道还没点
					wk.record("点穴道...")
					wk.move_click(*pos)
					msleep(400)
					if cls.is_popup_show_info(wk, "需要消耗"):
						wk.record("金针不足")
						return
					cls.click_confirm(wk)
					msleep(400)
				else:
					break
		wk.record("穴道打通成功")


class TaskBaiBaoXiang(TaskBase):
	TASK_NAME = "开百宝箱"
	IS_FREE = True
	RECT_QUESTION = (228, 210, 641, 279)
	RECT_ANSWER = (228, 363, 576, 434)
	COLOR_ANSWER = "c9ff26"
	POS_CONFIRM = (610, 406)
	NEED_PLAYER_NAME = False

	QUESTION_A = "什么是麒麟儿|开当前地图|悟性的作用|与师傅组队|师门环任务会|用于升级内功|关于摆摊|武器品质|关于炼药|群体绝艺|极限挑战的|侠义值的获取|快捷炼器|晶体石升级|转生随从与|当前威望|属性重置和品质强化"
	QUESTION_B = "佩戴武器|比武和单挑|受伤的三元|穴道的贯通|接取悟性环|侠义值的作用|晶体石的获得|坐骑的产出|侠义值的用处|挑战药神堂|合成为套装|可以自创内功"
	QUESTION_C = "获得晶体石的方式|梦中大侠副本|擂台活动的最低|五行相克|影响收服|级的武功招式|关于结拜|关于押镖|关于打造|障碍技能|装备不可交易|极品麒麟|会员每天的押镖|叶子戏活动|头盔煅炼|堂可以提升随"
	QUESTION_D = "万安寺副本|坐骑强化能提升|能创建队伍|关于袭击|押镖能获得|会员每周能领|坐骑强化能|修行老者处|80级梦中|备可以进行属|备品质强化需要"

	@classmethod
	def run(cls, wk: Worker):
		pics = wk.match_pic("江湖百宝*.bmp")
		print(pics)
		for _ in range(1000):
			if cls.is_popup_show_info(wk, "剩余物品箱空间不够"):
				wk.record("背包已满")
				break
			if cls.is_popup_show_info(wk, "你的等级需要达到"):
				wk.record("等级不够")
				break
			if not cls.is_exam_page_open(wk):
				if not cls.bag_use_item(wk, pics, is_close=False, is_confirm=False):
					wk.record("背包里未找到江湖百宝箱...")
					break
			else:
				wk.record("点击答案...")
				if cls.click_bai_bao_answer(wk):
					wk.move_click(*cls.POS_CONFIRM)
					msleep(2000)
			msleep(1200)

	@classmethod
	def is_exam_page_open(cls, wk: Worker):
		return wk.find_pic(*RECT_FULL, "界_考试.bmp")

	@classmethod
	def click_bai_bao_answer(cls, wk: Worker):
		if wk.find_str(*cls.RECT_QUESTION, cls.QUESTION_D, COLOR_TASK_NAME):
			wk.record("正确选项:四")
			wk.move_click(409, 428)  # 第四个选项
			wk.move_click(409, 410)  # 第四个选项
			return True
		elif wk.find_str(*cls.RECT_QUESTION, cls.QUESTION_C, COLOR_TASK_NAME):
			wk.record("正确选项:三")
			wk.move_click(245, 428)  # 第三个选项
			wk.move_click(245, 410)  # 第三个选项
			return True
		elif wk.find_str(*cls.RECT_QUESTION, cls.QUESTION_B, COLOR_TASK_NAME):
			wk.record("正确选项:二")
			wk.move_click(409, 398)  # 第二个选项
			wk.move_click(409, 380)  # 第二个选项
			return True
		elif wk.find_str(*cls.RECT_QUESTION, cls.QUESTION_A, COLOR_TASK_NAME):
			wk.record("正确选项:一")
			wk.move_click(244, 398)  # 第一个选项
			wk.move_click(244, 380)  # 第一个选项
			return True
		wk.record("没有找到正确选项")
		return False


class TaskShangJin(TaskBase):
	TASK_NAME = "赏金任务"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.back_to_kai_feng(wk)
		for i in range(500):
			cls.talk_with_cur_map_npc(
				wk, "凤天情", ["我来关照了"], reply_first=True, click_doing=True, close_talk=False)
			if cls.is_talk_show_info(wk, "还没找齐吗"):
				wk.record("物品不够了, 自动结束")
				break
			cls.close_other_talk(wk)
			msleep(400)
		cls.close_pages(wk)


class TaskFengLu(TaskBase):
	TASK_NAME = "领取俸禄"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.go_to_wu_ling_meng(wk)
		if cls.cur_map(wk) != "武林盟":
			wk.record("进入武林盟失败")
			return
		wk.record("正在领取俸禄...")
		for i in range(50):
			if cls.is_talk_open(wk):
				if cls.is_talk_show_info(wk, "天下英豪云集"):
					wk.record("和盟主对话领取...")
					cls.talk_click_items(wk, ["俸禄领取", "领取"])
					break
				else:
					wk.record("关闭其它对话...")
					cls.close_other_talk(wk)
			if i == 0 or wk.is_stuck:
				color = COLOR_CYAN + "|" + COLOR_GREEN
				if wk.find_str_offset_click(*RECT_FIND, "盟主", color, dx=rnd(-4, 4), dy=rnd(80, 100)):
					wk.record("点击盟主")
				else:
					cls.close_big_map(wk)
					cls.big_map_click(wk, *POS_MENG_ZHU)
				msleep(600)
			msleep(600)
		wk.record("领取俸禄完成")


class TaskReturnLogin(TaskBase):
	TASK_NAME = "返回登录"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.return_login(wk)

	@classmethod
	def after_run(cls, wk: Worker):
		pass


class TaskFastChallenge(TaskBase):
	TASK_NAME = "急速挑战"
	IS_TEAM_TASK = True

	@classmethod
	def run(cls, wk: Worker):
		cls.back_to_kai_feng(wk)
		while True:
			if wk.is_fight:
				cls.fight_operation(wk)
				msleep(800)
			if cls.talk_click_specify_item(wk, "确定", timeout=600):
				continue
			if rnd(0, 1):
				cls.find_way_npc(wk, "楚狂士", ["急速挑战", "进行挑战"])
			else:
				cls.talk_with_cur_map_npc(
					wk, "楚狂士", ["急速挑战", "进行挑战"])
			for i in range(10):
				if wk.is_fight:
					break
				msleep(400)


class TaskKaiBaoHe(TaskBase):
	TASK_NAME = "开宝盒"
	IS_FREE = True
	POS_YAO_SHI = (279, 133)
	POS_CHUI_ZI = (537, 131)
	POS_LUCKY_FIRST = (195, 233)
	POS_LUCKY_AGAIN = (269, 474)
	POS_CLOSE_BAOHE = (549, 49)
	RECT_BAO_HE_NUM = (312, 426, 358, 456)

	@classmethod
	def run(cls, wk: Worker):
		pics = wk.match_pic("*月饼.bmp") + "|" + wk.match_pic("*宝盒.bmp") + "|圣诞袜.bmp|圣诞袜满.bmp"
		cls.bag_use_item_all(wk, pics)

	@classmethod
	def do_sth_before_use_thing(cls, wk: Worker):
		for _ in range(99):
			msleep(400)
			if not cls.do_sth_after_use_thing(wk):
				break

	@classmethod
	def do_sth_after_use_thing(cls, wk: Worker):
		flag = False  # 代表是否在开特殊的箱子
		# 开白银/黄金/富甲等
		if cls.is_key_page_open(wk):
			if wk.ocr(*cls.RECT_BAO_HE_NUM, COLOR_BLACK, zk=ZK_DIGIT_11) == "0":
				cls.click_confirm(wk, RECT=RECT_POPUP)
				wk.move_click(*cls.POS_CLOSE_BAOHE)
				msleep(600)
				return False
			flag = True
			if wk.cfg_plan_task["开盒工具"] == "钥匙":
				wk.record("有锁宝盒用钥匙开...")
				wk.move_click(*cls.POS_YAO_SHI)
			else:
				wk.record("有锁宝盒用锤子砸...")
				wk.move_click(*cls.POS_CHUI_ZI)
		# 开幸运
		if cls.is_lucky_page_open(wk):
			wk.record("幸运宝盒翻牌子...")
			flag = True
			x, y = cls.POS_LUCKY_FIRST
			wk.move_click(x + rnd(0, 3)*138, y + rnd(-3, 3))
			msleep(1000)
			wk.move_click(*cls.POS_LUCKY_AGAIN)  # 开启新宝盒
			msleep(400)
		if not flag:
			wk.record("直接开宝盒(各种宝盒, 月饼, 圣诞袜子...)")
		if cls.click_confirm(wk):
			cls.click_confirm(wk)
		return flag

	@classmethod
	def is_key_page_open(cls, wk: Worker):
		return wk.find_pic(*RECT_FULL, "宝盒数量.bmp")

	@classmethod
	def is_lucky_page_open(cls, wk: Worker):
		return wk.find_pic(*RECT_FULL, "免费选取.bmp")

	@classmethod
	def get_default_biz_config(cls):
		return {
			"开盒工具": "钥匙",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_kai_bao_he, cls.CONFIG["开盒工具"]
		)

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["开盒工具"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_kai_bao_he)
		super().cfg_save(plan_name)


class TaskPickupPosts(TaskBase):
	TASK_NAME = "捡帖子"
	NEED_AVOID_FIGHT = True
	IS_FREE = True
	NATIONAL_MAP_LIST = ["桃花岛", "开封", "雁门关内"]  # 国庆
	SPRING_MAP_LIST = ["有座山", "桃花林", "十字坡", "绝情谷", "王屋山", "龙门"]
	MAP_POINTS = {  # 这里记录一下每个地图左上和右下的坐标
		"桃花岛": [(834,159), (659,96), (593,226), (818,278)],
		"开封": [(199,102), (287,210), (236,236), (178,418), 
			(336,379), (442,358), (481,255), (448,130), (566,132), 
			(504,168), (812,268), (799,402), (481,324)],
		"雁门关内": [(606,297), (614,251), (694,128), (684,252), (695,319), (758,260), (854,83), (702,77)],
		"桃花林": [(521,283), (543,166), (520,114), (789,109), (823,344), (651,317)],
		"有座山": [(832,230), (683,79), (668,122), (781,220), (669,323), (779,276), (823,323)],
		"绝情谷": [(511,172), (495,102), (595,225), (656,288), (746,144), (739,302), (834,323)],
		"十字坡": [(697,229), (678,88), (831,106), (716,140), (842,171), (828,229)],
		"王屋山": [(443,353), (370,235), (266,241), (791,313), (825, 123)],
		"龙门": [(837,254), (579,103), (834,117), (823,158), (719,211), (572,252)],
	}

	@classmethod
	def run(cls, wk: Worker):
		wk.last_success_ts = settings.cur_time_stamp
		cls.go_to_specific_map(wk)
		wk.is_stuck = True
		while True:
			cls.close_pages(wk)
			cur_map_name = cls.cur_map(wk)
			wk.record(f"正在 {cur_map_name} 捡帖子...")
			points = cls.get_points(wk, cur_map_name)
			if not points:
				return
			idx = -1
			while True:
				if cls.click_treasure(wk):
					break
				if wk.is_stuck:
					idx += 1
					if idx >= len(points):
						idx = 0
					pt = points[idx]
					rnd_x, rnd_y = pt[0] + rnd(-2, 2), pt[1] + rnd(-2, 2)
					wk.record("到下一位置寻找帖子...")
					cls.big_map_click(wk, rnd_x, rnd_y)
					msleep(200)
				msleep(400)

	@classmethod
	def get_points(cls, wk: Worker, cur_map_name: str):
		points = cls.MAP_POINTS.get(cur_map_name)
		if not points:
			wk.record("地图信息未录入!")
			return []
		return points

	@classmethod
	def click_treasure(cls, wk: Worker):
		# 返回True代表换地图
		for i in range(8):
			if not wk.find_pic_relative_click(*RECT_FULL, "帖子.bmp|春节帖子.bmp|春节帖子2.bmp", delta_color="1c1c1c", timeout=100):
				if settings.cur_time_stamp - wk.last_success_ts > 60:
					wk.record("超过1分钟未发现帖子, 换地图!")
					cls.switch_map(wk)
					wk.last_success_ts = settings.cur_time_stamp
					return True
				break
			wk.last_success_ts = settings.cur_time_stamp
			wk.record("发现并点击帖子！")
			cls.click_confirm(wk)
			msleep(400)
		cls.click_cancel(wk, RECT=RECT_FULL)

	@classmethod
	def go_to_specific_map(cls, wk: Worker):
		cur_month = datetime.now().month
		map_list = cls.NATIONAL_MAP_LIST if cur_month in [9, 10] else cls.SPRING_MAP_LIST
		if cls.cur_map(wk) not in map_list:
			random_map = random.choice(map_list)
			wk.record(f"当前不在 活动地图, 正在随机前往 {random_map}...")
			cls.run_to_map(wk, random_map)

	@classmethod
	def switch_map(cls, wk: Worker):
		cur_month = datetime.now().month
		map_name = cls.cur_map(wk)
		map_list = cls.NATIONAL_MAP_LIST if cur_month in [9, 10] else cls.SPRING_MAP_LIST
		new_map_list = copy.deepcopy(map_list)
		if map_name in new_map_list:
			new_map_list.remove(map_name)
		random_map = random.choice(new_map_list)
		wk.record(f"正在前往下一地图 {random_map}...")
		cls.run_to_map(wk, random_map)


class TaskGuYongNing(TaskBase):
	TASK_NAME = "交雇佣令"
	IS_FREE = True
	IS_TEAM_TASK = True

	@classmethod
	def run(cls, wk: Worker):
		if wk.cfg_plan_task["交雇佣令"] == "威望":
			cls.go_to_wu_ling_meng(wk)
			if cls.cur_map(wk) != "武林盟":
				wk.record("进入武林盟失败")
				return
			wk.record(f"正在交雇佣令, 当前设置:威望...")
		else:
			cls.back_to_kai_feng(wk)
			wk.record(f"正在交雇佣令, 当前设置:名望...")
		for i in range(50):
			if cls.is_talk_open(wk):
				t_name = cls.get_talk_name(wk)
				if t_name in ["武林盟管事", "曹都尉"]:
					wk.record(f"和 {t_name} 对话中...")
					if cls.talk_click_items(wk, ["使用雇佣令", "使用小雇佣令"]):
						break
				else:
					wk.record("关闭其它对话...")
					cls.close_other_talk(wk)
			if i == 0 or wk.is_stuck:
				if wk.cfg_plan_task["交雇佣令"] == "威望":
					cls.find_wlm_gs(wk)
				else:
					cls.find_way_npc(wk, "曹都尉", [], close_talk=False)
			msleep(600)
		# 如果弹窗展示
		if wk.find_str(285, 133, 591, 233, "要使用完成环任务吗", COLOR_BLACK, timeout=800):
			wk.record("交雇佣令成功")
			wk.move_click(379, 251)
			msleep(400)
		else:
			wk.record("交雇佣令失败")
		cls.click_confirm(wk)
		wk.record("交雇佣令完成")

	@classmethod
	def find_wlm_gs(cls, wk: Worker):
		color = COLOR_CYAN + "|" + COLOR_GREEN
		if wk.find_str_offset_click(*RECT_FIND, "盟管事", color, dx=rnd(-4, 4), dy=rnd(80, 100)):
			wk.record("点击武林盟管事")
		else:
			cls.big_map_click(wk, *POS_MENG_ZHU)
		msleep(600)

	@classmethod
	def get_default_biz_config(cls):
		return {
			"交雇佣令": "威望",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_jgyn, cls.CONFIG["交雇佣令"]
		)

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["交雇佣令"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_jgyn)
		super().cfg_save(plan_name)


class TaskUpdateZbg(TaskBase):
	TASK_NAME = "升装备格"
	IS_FREE = True
	NEED_PLAYER_NAME = False

	@classmethod
	def run(cls, wk: Worker):
		if not cls.open_zbg_page(wk):
			wk.record("打开装备格失败")
			return
		POS_LIST = [(213, 127), (286, 125), (359, 129), (427, 129), (212, 196),
					(425, 197), (216, 265), (286, 263), (358, 267), (431, 268)]
		for i, pos in enumerate(POS_LIST):
			x, y = pos
			wk.record(f"正在升级第 {i+1} 个装备格...")
			wk.move_click(x, y)
			for _ in range(100):
				if wk.find_pic_click(*RECT_FULL, "炼化1.bmp|炼化2.bmp"):
					wk.record("点击炼化...")
				msleep(500)
				if cls.click_confirm(wk, timeout=0):
					wk.record(f"第 {i+1} 个装备格炼化终止")
					break
			msleep(600)
			cls.click_confirm(wk)
			cls.close_other_talk(wk)

	@classmethod
	def is_zbg_page_open(cls, wk: Worker, timeout=0):
		return wk.find_pic(*RECT_FULL, "界_装备格.bmp", timeout=timeout)

	@classmethod
	def open_zbg_page(cls, wk: Worker):
		if cls.is_zbg_page_open(wk):
			return True
		cls.open_bag_page(wk)
		msleep(400)
		wk.find_pic_click(*RECT_FULL, "精通1.bmp|精通2.bmp", timeout=200)
		msleep(400)
		res = cls.is_zbg_page_open(wk, timeout=400)
		return res


class TaskRunFight(TaskBase):
	TASK_NAME = "跑动遇敌"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		for i in range(10000):
			wk.record(f"第 {i} 次跑动遇敌...")
			cls.run_left_right_click_floor(wk)
			if wk.is_fight:
				cls.fight_operation(wk)
			msleep(600)


class TaskBiHaiChaoSheng(TaskBase):
	TASK_NAME = "碧海潮生"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.run_to_map(wk, "桃花岛")
		if cls.cur_map(wk) != "桃花岛":
			wk.record("进入桃花岛失败")
			return
		wk.record("和黄药师对话...")
		cls.talk_with_cur_map_npc(wk, "黄药师", ["碧海潮生", "不吝指教"], map_name="桃花岛")
		start_ts = settings.cur_time_stamp
		while settings.cur_time_stamp - start_ts < 125 * 60:
			if not cls.is_have_buff(wk, "碧海潮生"):
				wk.record("当前未在打坐, 开始打坐...")
				cls.run_to_map(wk, "桃花岛")
				rnd_x, rnd_y = 650 + rnd(0,22), 92 + rnd(0,15)
				cls.big_map_click(wk, rnd_x, rnd_y)
				time.sleep(3)
				wk.key_press_combo(VK_ALT, VK_D)
			mins = (settings.cur_time_stamp - start_ts) // 60
			wk.record(f"已打坐 {mins} 分钟, 打坐中...")
			time.sleep(60)
		wk.record("碧海潮生两小时已完成, 领取奖励...")
		for _ in range(6):  # 领20分钟奖励6次
			cls.talk_with_cur_map_npc(wk, "黄药师", ["碧海潮生"], close_talk=False, map_name="桃花岛")
			msleep(600)
			cls.talk_click_first_item(wk)
			msleep(400)
			cls.close_other_talk(wk)
		wk.record("领取奖励完成")




class TaskDuanZB(TaskBase):
	TASK_NAME = "锻炼装备"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		if not cls.open_duan_page(wk):
			return
		cls.duan_wu_qi(wk)
		cls.duan_fang_ju(wk)
		cls.duan_shou_shi(wk)

	@classmethod
	def open_duan_page(cls, wk: Worker):
		if wk.find_multi_color(*RECT_FULL, MCOLOR_IN_DUAN):
			return True
		cls.open_quick_func_page(wk)
		if not wk.find_multi_color_click(*RECT_LEFT, MCOLOR_QUICK_EQUIP, timeout=200):
			wk.record("快捷页面打开失败")
			return False
		msleep(600)
		if not wk.find_multi_color_click(*RECT_LEFT, MCOLOR_QUICK_DUAN, timeout=200):
			wk.record("点击 锻炼 失败")
			return False
		msleep(600)
		return True

	@classmethod
	def duan_wu_qi(cls, wk: Worker):
		if not wk.find_multi_color_click(*RECT_LEFT, MCOLOR_DUAN_CHUAN_WUQI):
			wk.record("切到 已穿戴武器 页失败")
			return
		if cls.is_talk_show_info(wk, "没有万溶锡", timeout=200):
			wk.record("没有万溶锡!!!")
			return
		msleep(400)
		wk.record("开始锻炼 主手武器...")
		if wk.find_multi_color_click(*RECT_FULL, MCOLOR_DUAN_WU_QI_L, timeout=600):
			cls.select_property(wk)
			wk.record("主手武器 锻炼完成")
		wk.record("开始锻炼 副手武器...")
		if wk.find_multi_color_click(*RECT_FULL, MCOLOR_DUAN_WU_QI_R, timeout=600):
			cls.select_property(wk)
			wk.record("副手武器 锻炼完成")

	@classmethod
	def duan_fang_ju(cls, wk: Worker):
		if not wk.find_multi_color_click(*RECT_LEFT, MCOLOR_DUAN_CHUAN_FANG):
			wk.record("切到 已穿戴防具 页失败")
			return
		if cls.is_talk_show_info(wk, "没有万溶锡", timeout=200):
			wk.record("没有万溶锡!!!")
			return
		msleep(400)
		lists = [
			("冠帽", MCOLOR_DUAN_MAO),
			("铠甲", MCOLOR_DUAN_KAI),
			("左护腕", MCOLOR_DUAN_HAND_L),
			("右护腕", MCOLOR_DUAN_HAND_R),
			("腰带", MCOLOR_DUAN_YAO),
			("靴子", MCOLOR_DUAN_XUE),
		]
		for name, c_info in lists:
			wk.record(f"开始锻炼 {name}...")
			if wk.find_multi_color_click(*RECT_FULL, c_info, timeout=600):
				cls.select_property(wk)
				wk.record(f"{name} 锻炼完成")

	@classmethod
	def duan_shou_shi(cls, wk: Worker):
		if not wk.find_multi_color_click(*RECT_LEFT, MCOLOR_DUAN_CHUAN_SHOU):
			wk.record("切到 已穿戴首饰 页失败")
			return
		if cls.is_talk_show_info(wk, "没有万溶锡", timeout=200):
			wk.record("没有万溶锡!!!")
			return
		msleep(400)
		lists = [
			("项链", MCOLOR_DUAN_XIANG),
			("玉佩", MCOLOR_DUAN_YU),
		]
		for name, c_info in lists:
			wk.record(f"开始锻炼 {name}...")
			if wk.find_multi_color_click(*RECT_FULL, c_info, timeout=600):
				cls.select_property(wk)
				wk.record(f"{name} 锻炼完成")

	@classmethod
	def select_property(cls, wk: Worker):
		msleep(100, 200)
		if not wk.find_multi_color_click(*RECT_FULL, MCOLOR_DUAN_SEL, timeout=400):
			wk.record("下拉属性失败")
			return
		msleep(400)
		prefer = wk.cfg_plan_task["内外"]
		wk.record(f'锻炼偏好: {prefer}')
		props = "气势|躲闪|御身"
		if prefer == "外":
			props += "|外|血量|精元"
		else:
			props += "|内|内力|气元"
		if not wk.find_str_click(*RECT_FULL, props, COLOR_WHITE, timeout=600):
			wk.record(f"选择属性失败")
			return
		msleep(400, 600)
		wk.find_multi_color_click(*RECT_FULL, MCOLOR_DUAN_CONFIRM, timeout=600)
		msleep(600)
		cls.click_cancel(wk)


	@classmethod
	def get_default_biz_config(cls):
		return {
			"内外": "外",
		}

	@classmethod
	def cfg_read(cls, plan_name: str):  # 文件到控件
		super().cfg_read(plan_name)
		set_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_duan, cls.CONFIG["内外"]
		)

	@classmethod
	def cfg_save(cls, plan_name: str):  # 控件到文件
		cls.CONFIG = cls.get_default_biz_config()
		cls.CONFIG["内外"] = get_checked_radio_text_in_groupbox(
			settings.wnd_main.groupBox_duan
		)
		super().cfg_save(plan_name)


class TaskShengDanXueRen(TaskBase):
	TASK_NAME = "圣诞雪人"

	@classmethod
	def run(cls, wk: Worker):
		wk.record("帮圣诞使者找回礼物...")
		cls.back_to_kai_feng(wk)
		cls.talk_with_cur_map_npc(wk, "圣诞使者", ["找回礼物"])
		wk.record("击败盗宝雪人...")
		msleep(1000)
		cls.task_npc_find_way(wk, "遗失的", color=COLOR_TALK_ITEM_TASK, npc_name='盗宝雪人')
		msleep(1000)
		if wk.is_fight:
			cls.fight_operation(wk)
		wk.record("回复圣诞使者...")
		cls.task_npc_find_way(wk, "归还礼物", color=COLOR_TALK_ITEM_TASK, npc_name='圣诞使者')
		wk.record("找圣诞树许愿...")
		cls.talk_with_cur_map_npc(wk, "圣诞树", ["许愿"])
		msleep(600)
		cls.click_confirm(wk)
		

class TaskShengDanShu(TaskDaNei):
	TASK_NAME = "圣诞树挑战"
	IS_DIFFICULT_TASK = True

	@classmethod
	def run(cls, wk: Worker):
		cls.back_to_kai_feng(wk)
		if settings.cur_time_fmt < "21:00":
			wk.record("活动还未开始, 自动结束")
			return
		while True:
			if settings.cur_time_fmt > "23:00":
				wk.record("活动已到结束时间")
				break
			wk.record("点击圣诞树挑战...")
			cls.talk_with_cur_map_npc(wk, "圣诞树", ["挑战雪人", "挑战"])
			msleep(600)
			if wk.is_fight:
				cls.fight_operation(wk)

	@classmethod
	def recognize_enemy(cls, wk: Worker):
		wk.should_run_away = False
		if wk.cur_round > 1:
			return
		if not wk.cfg_plan_task["不打小怪"]:  # 要打小怪
			wk.record(f"正在打 {boss_name}...")
			return
		boss_name = wk.ocr(*RECT_BOSS_NAME, COLOR_GOLD, timeout=200)
		if boss_name.endswith(("_", "一")):
			msleep(500)  # 过一会再识别
			boss_name = wk.ocr(*RECT_BOSS_NAME, COLOR_GOLD, timeout=200)
		if "宝雪人" not in boss_name:  # 说明是小怪
			wk.record(f"不打小怪 {boss_name}")
			wk.should_run_away = True
			return
		wk.record(f"遇到 {boss_name}")
		wk.beep(1000, 600)


class TaskDeliverPosts(TaskBase):
	TASK_NAME = "交帖子"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.back_to_kai_feng(wk)
		for i in range(60):
			cls.talk_with_cur_map_npc(wk, "岁岁欢", ["瑞雪丰年", "上交"])
			msleep(300)


class TaskSiHai(TaskBase):
	TASK_NAME = "四海同乐"
	IS_FREE = True

	@classmethod
	def run(cls, wk: Worker):
		cls.back_to_kai_feng(wk)
		for i in range(10):
			cls.recv_task(wk)
			wk.record(f"正在做 第 {i+1} 次拜年...")
			cls.do_task(wk)

	@classmethod
	def recv_task(cls, wk: Worker):
		wk.record("正在领取拜年任务...")
		cls.back_to_kai_feng(wk)
		cls.find_way_npc(wk, "岁岁欢", ["四海同乐", "领取拜年"])

	@classmethod
	def do_task(cls, wk: Worker):
		cls.task_npc_find_way(wk, "拜年", color=COLOR_TALK_ITEM_TASK)

	@classmethod
	def is_cur_task_desc(cls, wk: Worker):
		return cls.region_task_desc_find_str(wk, "普天同庆中国年")
	

class TaskSiHai(TaskBase):
	TASK_NAME = "四象洗练"
	IS_FREE = True
	NEED_PLAYER_NAME = False

	@classmethod
	def run(cls, wk: Worker):
		if not wk.find_multi_color(*RECT_RIGHT, MCOLOR_TI_HUAN_XI_NIAN, timeout=200):
			wk.record("请先打开要培养的四象界面!")
			wk.pause()
		for i in range(1000):
			if i % 10 == 0:
				wk.record(f"正在洗练中...")
			cls.xi_nian_or_ti_huan(wk)
			cls.click_confirm(wk, timeout=500)
			
	@classmethod
	def xi_nian_or_ti_huan(cls, wk: Worker):
		color = COLOR_GREEN + "|" + COLOR_RED
		prop_expr = wk.ocr(*RECT_XI_NIAN_NUMBER, color, zk=ZK_DIGIT_11_EX)
		print(f"prop_expr: {prop_expr}")
		value = eval(prop_expr) if prop_expr else 0
		if value > 0:
			wk.record(f"本次:{prop_expr}, 增加数值:{value}, 正在替换...")
			wk.find_multi_color_click(*RECT_RIGHT, MCOLOR_TI_HUAN_XI_NIAN)
		else:
			if prop_expr:
				wk.record(f"本次:{prop_expr}, 减少数值:{value}, 正在洗练...")
			wk.find_multi_color_click(*RECT_RIGHT, MCOLOR_XI_NIAN)
		msleep(600)