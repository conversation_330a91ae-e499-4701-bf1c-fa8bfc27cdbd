from PySide2.QtWidgets import QGroupBox, QRadioButton, QStyledItemDelegate


def get_checked_radio_text_in_groupbox(groupbox: QGroupBox):
    selected_radio = None
    for button in groupbox.findChildren(QRadioButton):
        if button.isChecked():
            selected_radio = button
            break
    return selected_radio.text() if selected_radio else None


def set_checked_radio_text_in_groupbox(groupbox: QGroupBox, text: str):
    for button in groupbox.findChildren(QRadioButton):
        if button.text() == text:
            button.setChecked(True)
            break


class AccountDelegate(QStyledItemDelegate):
    def __init__(self, parent=None, mask_len=5) -> None:
        self.mask_len = mask_len
        super().__init__(parent)

    def displayText(self, value, locale):
        if len(value) > self.mask_len:
            return "*" * self.mask_len + value[self.mask_len:]
        return value


class PasswordDelegate(QStyledItemDelegate):
    def displayText(self, value, locale):
        if not value:
            return ""
        return "************"
    

