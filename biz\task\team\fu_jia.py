from biz.constants.constants import *
from utils import *
from biz.task.__base import TaskBase
from biz.obj.worker import Worker


class TaskFuJia(TaskBase):
    TASK_NAME = "富甲天下"
    IS_TEAM_TASK = True
    NEED_ANSWER_QUESTION = True
    IS_DIFFICULT_TASK = True
    
    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        wk.team.need_answer_question = True

    @classmethod
    def run(cls, wk: Worker):
        if not cls.check_the_week(wk):
            return
        if cls.cur_map(wk) != "富甲天下":
            cls.go_to_fu_jia(wk)
        while True:
            if wk.is_fight:
                cls.fight_operation(wk)
            if cls.cur_map(wk) == "开封":
                wk.record("传回开封, 富甲结束")
                break
            cls.close_other_talk(wk)
            if wk.find_pic_r_click(*RECT_FUJIA_DAOJU, "财神卡.bmp"):
                wk.record("点击财神卡")
                msleep(300)
                cls.click_confirm(wk, RECT=RECT_POPUP)
            if wk.find_pic(*RECT_FULL, "答题.bmp"):
                wk.record("答题中...")
                wk.find_pic_click(*RECT_FULL, "答题选项.bmp")
                msleep(500)
                wk.move_click(*POS_CONFIRM)
            if wk.find_pic_click(*RECT_FULL, "骰子.bmp|骰子2.bmp|骰子3.bmp"):
                wk.record("点击骰子")
                msleep(500)
            wk.record("等待下一轮...")
            msleep(1000)
            
    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday != 6:
            wk.record(f"富甲只能在星期六, 今天是星期{weekday}")
            return False
        return True

    @classmethod
    def go_to_fu_jia(cls, wk: Worker):
        if cls.cur_map(wk) in WILD_MAP_LIST:
            cls.use_xmx(wk)
        cls.lead_team_switch_line(wk, '一')
        cls.back_to_kai_feng(wk)
        if cls.cur_map(wk) == "开封":
            cls.find_way_npc(wk, "钱老板", ["富甲天下", "我要参加活动"])
            msleep(2000)
            cls.click_confirm(wk)

    @classmethod
    def people_fight_action(cls, wk: Worker):
        if cls.is_people_action(wk) and wk.team.fight_random:
            idx = rnd(0, 9)
            x, y = POS_ENENMY_LIST[idx]
            wk.record(f"发现 探宝小兵，人物随机攻击:{idx}")
            wk.move_relative_click(x, y - rnd(50, 60))
            return
        return super().people_fight_action(wk)

    @classmethod
    def bb_fight_action(cls, wk: Worker):
        if cls.is_bb_action(wk) and wk.team.fight_random:
            idx = rnd(0, 9)
            x, y = POS_ENENMY_LIST[idx]
            wk.record(f"发现 探宝小兵，BB随机攻击:{idx}")
            wk.move_relative_click(x, y - rnd(50, 60))
            return
        return super().bb_fight_action(wk)

    @classmethod
    def recognize_enemy(cls, wk: Worker):
        super().recognize_enemy(wk)
        if wk.find_str(*RECT_FIGHT_ENEMY, "探宝小兵", COLOR_RED):
            wk.team.fight_random = True
