from datetime import datetime
from biz.task.__base import TaskBase
from biz.task.single.qing_li import TaskQingLi
from biz.constants.constants import *
from biz.task.team.fu_ben import TaskFuBen
from utils import *
import settings
from biz.obj.worker import Worker


MAP_NPC_XY = {
    "有座山": {(766, 130), (765, 130), (767, 130), (766, 129), (766, 131)},
    "绝情谷": {(648, 290), (647, 290), (649, 290), (648, 289), (648, 291)},
    "十字坡": {(844, 225), (843, 225), (845, 225), (844, 224), (844, 226)},
    "王屋山": {(381, 197), (380, 197), (382, 197), (381, 196), (381, 198)},
    "神龙岛": {(780, 140), (779, 140), (781, 140), (780, 139), (780, 141)},
    "龙门": {
        (824, 102), (823, 102), (825, 102), (824, 101), (824, 103),
        (767, 130), (766, 130), (768, 130), (767, 129), (767, 131)
    },
    "终南山": {
        (842, 183), (841, 183), (843, 183), (842, 182), (842, 184)
    },
    "白驼山": {
        (770, 196), (769, 196), (771, 196), (770, 195), (770, 197),
        (648, 97), (647, 97), (649, 97), (648, 96), (648, 98),
        (846, 65), (845, 65), (847, 65), (846, 64), (846, 66)
    },
    "碧峰峡": {
        (661, 300), (660, 300), (662, 300), (661, 299), (661, 301),
        (856, 263), (855, 263), (857, 263), (856, 262), (856, 264)
    },
    "恶人谷": {
        (628, 279), (629, 280),
        (812, 87), (811, 87),
        (778, 172), (779, 173),
        (839, 254), (840, 255)
    },
    "无量山": {
        (706, 315), (705, 315), (707, 315), (706, 314), (706, 316)
    },
    "雁门关外": {
        (627, 102), (626, 102), (628, 102), (627, 101), (627, 103)
    },
    "大草原": {
        (524, 193), (523, 193), (525, 193), (524, 192), (524, 194)
    },
    "古墓一层": {
        (676, 378), (675, 378), (677, 378), (676, 379), (676, 377)
    },
    "古墓二层": {
        (817, 380), (816, 380), (818, 380), (817, 379), (817, 381),
        (564, 142), (563, 142), (565, 142), (564, 141), (564, 143)
    },
    "古墓三层": {
        (446, 213), (445, 213), (447, 213), (446, 212), (446, 214),
        (510, 110), (509, 110), (511, 110), (510, 109), (510, 111),
        (510, 109), (509, 109), (511, 109), (510, 108), (510, 110)
    },
    "秦陵一层": {
        (634, 196), (633, 196), (635, 196), (634, 195), (634, 197),
        (305, 352), (304, 352), (306, 352), (305, 351), (305, 353)
    },
    "秦陵二层": {
        (794, 266), (793, 266), (795, 266), (794, 265), (794, 267),
        (512, 234), (511, 234), (513, 234), (512, 233), (512, 235)
    },
    "玄霜浅滩": {
        (805, 289), (804, 289), (806, 289), (805, 288), (805, 290),
        (626, 85), (625, 85), (627, 85), (626, 84), (626, 86)
    },
    "金风山道": {
        (584, 188), (583, 188), (585, 188), (584, 187), (584, 189),
        (844, 330), (843, 330), (845, 330), (844, 329), (844, 331),
        (655, 319), (654, 319), (656, 319), (655, 318), (655, 320),
        (677, 302), (676, 302), (678, 302), (677, 301), (677, 303),
    },
    "赤霞渡口": {
        (600, 255), (599, 255), (601, 255), (600, 254), (600, 256),
        (776, 97), (775, 97), (777, 97), (776, 96), (776, 98)
    },
    "青云幽谷": {
        (692, 164), (691, 164), (693, 164), (692, 163), (692, 165),
        (830, 162), (829, 162), (831, 162), (830, 161), (830, 163)
    },
    "燕子坞": {
        (708, 278), (577, 186), (692, 122), (776, 247)
    },
    "楼兰古道": {
        (741, 141), (740, 141), (742, 141), (741, 140), (741, 142)
    },
    "玉门关": {
        (683, 234), (682, 234), (684, 234), (683, 233), (683, 235),
        (772, 170), (771, 170), (773, 170), (772, 169), (772, 171),
        (571, 281), (570, 281), (572, 281), (571, 280), (571, 282),
        (571, 210), (570, 210), (572, 210), (571, 209), (571, 211),
        (840, 108), (839, 108), (841, 108), (840, 107), (840, 109)
    },
    "落日谷": {
        (627, 114), (626, 114), (628, 114), (627, 113), (627, 115),
        (755, 268), (754, 268), (756, 268), (755, 267), (755, 269)
    },
    "斡难河": {
        (802, 311), (801, 311), (803, 311), (802, 310), (802, 312),
        (747, 108), (746, 108), (748, 108), (747, 107), (747, 109),
        (670, 249), (669, 249), (671, 249), (670, 248), (670, 250),
        (846, 356), (845, 356), (847, 356), (846, 355), (846, 357),
        (738, 276), (737, 276), (739, 276), (738, 275), (738, 277)
    },
}


class TaskMingGuaiBase(TaskBase):
    IS_TEAM_TASK = True
    NEED_AVOID_FIGHT = True
    MONSTER_NAME = ""
    TALK_CONTENT = ""

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        wk.exclude_pos = MAP_NPC_XY.get(cls.cur_map(wk), set())
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=0, minute=0, second=0, microsecond=0).timestamp()
        )
        cls.NO_REFRESH_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=23, minute=59, second=59, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=23, minute=59, second=59, microsecond=0).timestamp()
        )

    @classmethod
    def run(cls, wk: Worker):
        if not cls.check_the_week(wk):
            return
        if settings.cur_time_stamp > cls.END_TS:
            wk.record("已到结束时间")
            return
        cls.on_ride(wk)
        cls.go_to_specific_map(wk)
        cls.ming_guai_proc(wk)

    @classmethod
    def ming_guai_proc(cls, wk: Worker):
        gone_another_map_before_end = False
        enter_bull_fight = False
        last_map_click_ts = 0
        for i in range(1000000):
            if settings.cur_time_stamp > cls.END_TS:
                wk.record("已到结束时间")
                break
            if wk.is_fight:
                cls.fight_operation(wk)
                if enter_bull_fight:
                    wk.done_count += 1
                    wk.record(f"已完成 {wk.done_count} 次")
                    enter_bull_fight = False
                    i = 0  # 立刻触发地图找明怪
            cls.do_fix_bb(wk)
            if cls.only_run_map(wk):
                cls.click_treasure(wk)
                cls.go_to_another_map(wk)
                continue
            if cls.is_talk_open(wk, timeout=500):
                talk_name = cls.get_talk_name(wk)
                if cls.MONSTER_NAME and talk_name and (talk_name in cls.MONSTER_NAME or cls.MONSTER_NAME in talk_name or talk_name.startswith("梦境")):
                    # 进到和明怪的对话
                    cls.talk_click_first_item(wk)
                    enter_bull_fight = True
                    if cls.wait_for_fight(wk):
                        continue
            cls.close_pages(wk)
            cls.click_treasure(wk)
            if cls.click_monster(wk):
                wk.fail_count = 0
                continue
            if i == 0 or wk.is_stuck or settings.cur_time_stamp - last_map_click_ts > rnd(6, 12):
                last_map_click_ts = settings.cur_time_stamp
                if cls.map_find_monster_click(wk):
                    wk.fail_count = 0
                    continue
                wk.fail_count += 1
            if cls.is_switch_map(wk):  # 打牛专用
                wk.record("没找到，且超过了刷新时间")
                if not gone_another_map_before_end:
                    gone_another_map_before_end = True
                    cls.go_to_another_map(wk)
                    continue
                break
            if wk.fail_count >= 1:
                wk.fail_count = 0
                cls.go_to_another_map(wk)

    @classmethod
    def is_switch_map(cls, wk: Worker):
        return wk.fail_count >= 1 and settings.cur_time_stamp > cls.NO_REFRESH_TS

    @classmethod
    def only_run_map(cls, wk: Worker):
        return False

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        pass

    @classmethod
    def go_to_another_map(cls, wk: Worker):
        pass

    @classmethod
    def map_find_monster_click(cls, wk: Worker):
        cls.open_big_map(wk)
        # 先找到自己的位置
        cx, cy = wk.get_pic_pos(*RECT_FULL, "我的位置.bmp")
        if cx > 0:  # 找到自己的位置，则选离自己最近的牛
            x, y = wk.find_pic_nearest_pos(
                *RECT_FULL, "明怪黄点.bmp", cx + 5, cy + 4)
            if x > 0 and (x, y) not in wk.exclude_pos:
                wk.record(f"找到了最近的怪:{x},{y}")
                wk.move_click(x + 2 + rnd(-5, 5), y + 3 + rnd(-5, 5))
                wk.key_press(VK_TAB)  # 关闭大地图
                return True
        # 没找到的话就全图找牛
        # [(0,333,444), (0, 444,555)]
        pos_list = wk.find_pic_ex(*RECT_FULL, "明怪黄点.bmp")
        if not pos_list:
            wk.key_press(VK_TAB)  # 关闭大地图
            return False
        # 然后排除掉任务NPC的坐标
        set1 = set([(x, y) for _, x, y in pos_list])
        set2 = wk.exclude_pos
        pos_list = list(set1 - set2)
        # 如果出了对话框
        if cls.is_talk_open(wk):
            return False
        # 然后从列表中随机取一个
        if len(pos_list):
            x, y = random.choice(pos_list)
            wk.record(f"随机选择了:{x},{y}")
            wk.move_click(x + 2 + rnd(-2, 2), y + 3 + rnd(-2, 2))
            wk.key_press(VK_TAB)  # 关闭大地图
            wk.is_stuck = False
            return True
        if rnd(0, 10) == 1:
            wk.record("地图上没找到怪...")
        cls.close_big_map(wk)
        return False

    @classmethod
    def get_dx(cls):
        return cls.name_calc_offset_x(cls.MONSTER_NAME)

    @classmethod
    def get_bull_body_pos(cls, wk: Worker):
        x, y = wk.find_str_nearest_pos(
            *RECT_FIND, cls.MONSTER_NAME, COLOR_GOLD + "|" + COLOR_CYAN, cx=PEOPLE_NAME_X, cy=PEOPLE_NAME_Y, sim=0.9
        )
        if x < 0:
            return -1, -1
        return x + cls.get_dx(), y + rnd(60, 100)

    @classmethod
    def click_monster(cls, wk: Worker):
        cls.close_big_map(wk)
        for _ in range(20):
            if wk.is_fight or cls.is_talk_open(wk):
                return True
            x, y = cls.get_bull_body_pos(wk)
            if x == -1:
                break
            wk.move_relative_click(x, y, re_move=False,
                                   limit_border=True, limit_y=Y_LIMIT_CLICK)
            msleep(50)
        if rnd(0, 5) == 1:
            wk.record("当前位置没找到怪...")
        return False


class TaskDaNiu(TaskMingGuaiBase):
    TASK_NAME = "带队打牛"
    IS_TEAM_TASK = True
    NEED_AVOID_FIGHT = True
    MONSTER_NAME = "破天牛|撼天牛|邪教长老|邪教元老|马贼"
    TALK_CONTENT = "岂能由你胡来|进入战斗"

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        daniu_map = wk.cfg_plan_task["主地图"]
        wk.exclude_pos = MAP_NPC_XY.get(daniu_map, set())
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=14, minute=0, second=0, microsecond=0).timestamp()
        )
        cls.NO_REFRESH_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=15, minute=5, second=0, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=15, minute=15, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday not in [1, 3, 5]:
            wk.record(f"打牛只能在星期一三五, 今天是星期{weekday}")
            return False
        return True

    @classmethod
    def after_run(cls, wk: Worker):
        if wk.cfg_plan_task["结束丢垃圾"]:
            wk.record("打牛结束, 正在丢垃圾...")
            wk.team.signal_drop_garbage.leader_set()
            TaskQingLi.set_task_config(wk, cls.TASK_NAME)
            TaskQingLi.throw_things(wk)
            cls.set_task_config(wk)  # 还原
        super().after_run(wk)

    @classmethod
    def go_to_another_map(cls, wk: Worker):
        main_map = wk.cfg_plan_task["主地图"]
        backup_map = wk.cfg_plan_task["备用地图"]
        cur_map_name = cls.cur_map(wk)
        if cur_map_name == main_map:
            wk.record(f"在主地图 {main_map} 没找到牛, 前往备用地图 {backup_map}")
            cls.run_to_map(wk, backup_map)
            wk.exclude_pos = MAP_NPC_XY.get(cls.cur_map(wk), set())
        elif cur_map_name == backup_map:
            wk.record(f"在备用地图 {backup_map} 没找到牛, 前往主地图 {main_map}")
            cls.run_to_map(wk, main_map)
            wk.exclude_pos = MAP_NPC_XY.get(cls.cur_map(wk), set())
        else:
            wk.record(f"前往主地图 {main_map}")
            cls.run_to_map(wk, main_map)
            wk.exclude_pos = MAP_NPC_XY.get(cls.cur_map(wk), set())

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        if wk.cfg_plan["领双带队打牛"]:
            db_time = wk.cfg_plan["领双时间带队打牛"]
            wk.record(f"打牛前领双:{db_time}")
            cls.get_double(wk, db_time)
        main_map = wk.cfg_plan_task["主地图"]
        if cls.cur_map(wk) != main_map:
            if not cls.is_avoid_fight(wk):
                cls.bag_use_item(wk, "熊猫香.bmp")
            cls.run_to_map(wk, main_map)
        cls.close_pages(wk)

    @classmethod
    def click_treasure(cls, wk: Worker):
        main_map = wk.cfg_plan_task["主地图"]
        backup_map = wk.cfg_plan_task["备用地图"]
        cur_map_name = cls.cur_map(wk)
        if cur_map_name and cur_map_name not in [main_map, backup_map]:
            wk.record(f"发现不在打牛地图, 正在前往...")
            cls.go_to_another_map(wk)

    @classmethod
    def get_dx(cls):
        return rnd(16, 28)

    @classmethod
    def get_default_biz_config(cls):
        return {
            "主地图": "大草原",
            "备用地图": "雁门关外",
            "结束丢垃圾": True,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_daniu_new_map_main.setCurrentText(
            cls.CONFIG["主地图"])
        settings.wnd_main.cmb_daniu_new_map_backup.setCurrentText(
            cls.CONFIG["备用地图"])
        settings.wnd_main.chk_daniu_end_drop.setChecked(cls.CONFIG["结束丢垃圾"])


    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件 -> 文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["主地图"] = settings.wnd_main.cmb_daniu_new_map_main.currentText()
        cls.CONFIG["备用地图"] = settings.wnd_main.cmb_daniu_new_map_backup.currentText()
        cls.CONFIG["结束丢垃圾"] = settings.wnd_main.chk_daniu_end_drop.isChecked()
        super().cfg_save(plan_name)


class TaskXieJiao(TaskDaNiu):
    TASK_NAME = "国庆邪教"

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        daniu_map = wk.cfg_plan_task["主地图"]
        wk.exclude_pos = MAP_NPC_XY.get(daniu_map, set())
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=14, minute=0, second=0, microsecond=0).timestamp()
        )
        cls.NO_REFRESH_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=20, minute=0, second=0, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=20, minute=0, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def check_the_week(cls, wk: Worker):
        return True


class TaskWuDou(TaskMingGuaiBase):
    TASK_NAME = "武斗大会"
    NEED_AVOID_FIGHT = False
    MONSTER_NAME = "五毒教徒|神龙教弟子|星宿派弟子"
    TALK_CONTENT = "接招吧"
    IS_ACTIVITY = True

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=19, minute=30, second=0, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=20, minute=00, second=0, microsecond=0).timestamp()
        )
        wk.team.need_auth_colors = True

    @classmethod
    def run(cls, wk: Worker):
        if not cls.check_the_week(wk):
            return
        map_name = cls.cur_map(wk)
        if "斗场" not in map_name and settings.cur_time_stamp > cls.END_TS:
            wk.record("已到结束时间")
            return
        if map_name and "斗场" not in map_name:
            cls.go_to_specific_map(wk)
        enter_bull_fight = False
        for i in range(100000):
            if wk.is_fight:
                cls.fight_operation(wk)
                if enter_bull_fight:
                    wk.done_count += 1
                    wk.record(f"已完成 {wk.done_count} 次")
                    enter_bull_fight = False
                    i = 0  # 立刻触发地图找牛
            cls.click_confirm(wk, timeout=0)
            if wk.find_str_click(*RECT_TALK, cls.TALK_CONTENT, COLOR_TALK_ITEM, timeout=200):
                msleep(500)
                enter_bull_fight = True
            else:  # 对话的不是牛，优先在当前位置直接点牛
                cls.close_pages(wk)
                if not cls.click_monster(wk):
                    if wk.is_stuck or i == 0:
                        cls.map_find_monster_click(wk)
            if cls.cur_map(wk) == "开封":
                wk.record("已返回开封, 武斗已结束")
                break
            msleep(200)
        # 领奖
        cls.talk_with_cur_map_npc(wk, "武举考官", ["领取奖励"])
        
            
    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday != 5:
            wk.record(f"武斗大会只能在星期五, 今天是星期{weekday}")
            return False
        return True

    @classmethod
    def get_dx(cls):
        return rnd(28, 35)

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        cls.lead_team_switch_line(wk, '四')
        wk.record("不在场内, 正在前往武斗场...")
        cls.back_to_kai_feng(wk)
        for i in range(100):
            if cls.talk_click_specify_item(wk, "是的"):
                msleep(600)
                cls.talk_click_specify_item(wk, "确定")
                wk.record("正在进入武斗场...")
                msleep(600)
            if i == 0 or wk.is_stuck:
                cls.click_system_task_name(wk)
            msleep(600)
            map_name = cls.cur_map(wk)
            if map_name and map_name.startswith("武斗场"):
                wk.record("已进入武斗场, 准备战斗...")
                break

    @classmethod
    def ready_overlap_auth(cls, wk: Worker):
        return TaskFuBen.ready_overlap_auth(wk)


class TaskTouZongXiaoZei(TaskMingGuaiBase):
    TASK_NAME = "偷粽小贼"
    IS_TASK_FIX_EQUIP_BB_ENABLE = True
    IS_DIFFICULT_TASK = True
    NEED_AVOID_FIGHT = True
    MONSTER_NAME = "偷棕小贼"
    TALK_CONTENT = "找打"
    MAP_NAME = "恶人谷"

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        wk.exclude_pos = MAP_NPC_XY.get(cls.MAP_NAME, set())
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=23, minute=00, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        cls.run_to_map(wk, cls.MAP_NAME)
        
    @classmethod
    def do_fix_bb(cls, wk: Worker, force=False):
        super().do_fix_bb(wk, force)
        cls.go_to_specific_map(wk)


class TaskQiXiQingRen(TaskMingGuaiBase):
    TASK_NAME = "七夕情人"
    IS_DIFFICULT_TASK = True
    NEED_AVOID_FIGHT = True
    MONSTER_NAME = "捕捉喜鹊的恶人|马贼|恶人"
    TALK_CONTENT = "岂能让你们|进入战斗"
    MAP_NAME = "碧峰峡"

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        wk.exclude_pos = MAP_NPC_XY.get(cls.MAP_NAME, set())
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=23, minute=00, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        cls.run_to_map(wk, cls.MAP_NAME)

    @classmethod
    def get_dx(cls):
        return rnd(13, 46)


class TaskZhongQiuShanDaWang(TaskMingGuaiBase):
    TASK_NAME = "中秋山大王"
    IS_DIFFICULT_TASK = True
    NEED_AVOID_FIGHT = True
    MONSTER_NAME = "山大王"
    TALK_CONTENT = "看打"

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=23, minute=00, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        map_list = ["十字坡", "有座山", "龙门"]
        if cls.cur_map(wk) not in map_list:
            # 从 map_list 中 随机选一个
            random_map = random.choice(map_list)
            wk.record(f"当前不在 活动地图, 正在随机前往 {random_map}...")
            cls.run_to_map(wk, random_map)
            

class TaskWuXingYiShou(TaskMingGuaiBase):
    TASK_NAME = "五行异兽"
    IS_DIFFICULT_TASK = True
    IS_TASK_FIX_EQUIP_BB_ENABLE = True
    NEED_AVOID_FIGHT = False
    MONSTER_NAME = "金行异兽|木行异兽|土行异兽|水行异兽|火行异兽"
    TALK_CONTENT = "看打"
    PEOPLE_WU_XING_ORDER = ["金", "木", "土", "水", "火"]
    BOSS_WU_XING_ORDER = ["木", "土", "水", "火", "金"]

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        wk.team.is_yi_shou_wu_xing = True
        wk.exclude_pos = set()

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        cls.back_to_kai_feng(wk)

    @classmethod
    def get_boss_wu_xing_idx(cls, wk: Worker):
        pos_list = wk.find_str_ex(*RECT_FIGHT_ENEMY, "木|土|水|火|金", COLOR_GOLD)
        if not pos_list:
            return -1
        idx, _, _ = pos_list.pop()
        return idx

    @classmethod
    def people_fight_action(cls, wk: Worker):
        if wk.boss_wu_xing_idx == -1:
            wk.boss_wu_xing_idx = cls.get_boss_wu_xing_idx(wk)
            if wk.boss_wu_xing_idx != -1:
                boss_wu_xing = cls.BOSS_WU_XING_ORDER[wk.boss_wu_xing_idx]
                player_wu_xing = cls.PEOPLE_WU_XING_ORDER[wk.boss_wu_xing_idx]
                wk.record(f"异兽使用:{boss_wu_xing}招, 大侠使用:{player_wu_xing}招克制")
                wk.key_press(VK_F1 + wk.boss_wu_xing_idx)
        wk.right_click()
        wk.key_press_combo(VK_ALT, VK_A)

    @classmethod
    def get_dx(cls):
        return rnd(14, 35)

class TaskLeiTai(TaskMingGuaiBase):
    TASK_NAME = "个人擂台"
    NEED_AVOID_FIGHT = False
    IS_TEAM_TASK = False
    IS_DIFFICULT_TASK = True
    IS_ACTIVITY = True

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=19, minute=0, second=0, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=20, minute=31, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday not in [2,3,5]:
            wk.record(f"个人擂台只能在星期二三五, 今天是星期{weekday}")
            return False
        return True

    @classmethod
    def run(cls, wk: Worker):
        if not cls.check_the_week(wk):
            return
        if settings.cur_time_stamp > cls.END_TS:
            wk.record("已到结束时间")
            return
        
        for i in range(100000):
            if wk.is_fight:
                cls.fight_operation(wk)
            map_name = cls.cur_map(wk)
            if map_name and not map_name.startswith("比武擂台"):
                cls.go_to_specific_map(wk)
            if cls.click_confirm(wk):
                wk.record("已应战...")
            if settings.cur_time_stamp > cls.END_TS:
                wk.record("已到结束时间")
                break
            msleep(600)

    @classmethod
    def handle_switch_line(cls, wk: Worker):
        cls.switch_line(wk, '四')

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        wk.record("不在场内, 正在前往比武擂台...")
        cls.back_to_kai_feng(wk)
        cls.switch_line(wk, "四")
        for i in range(100):
            if settings.cur_time_stamp > cls.END_TS:
                wk.record("已到结束时间")
                return
            if cls.is_talk_open(wk):
                if wk.find_str_click(*RECT_TALK, "擂台报名", COLOR_TALK_ITEM):
                    msleep(600)
                    wk.find_str_click(*RECT_TALK, "单人报名", COLOR_TALK_ITEM)
                    wk.record("正在进入比武擂台...")
                    msleep(600)
                    break
                cls.close_other_talk(wk)
            if i == 0 or wk.is_stuck:
                cls.click_system_task_name(wk)
            msleep(600)
        return True

class TaskLeiTaiTeam(TaskLeiTai):
    TASK_NAME = "团体擂台"
    IS_TEAM_TASK = True
    NEED_AVOID_FIGHT = False
    IS_ACTIVITY = True

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        cls.lead_team_switch_line(wk, '四')
        wk.record("不在场内, 正在前往比武擂台...")
        cls.back_to_kai_feng(wk)
        for i in range(100):
            if settings.cur_time_stamp > cls.END_TS:
                wk.record("已到结束时间")
                return
            if cls.is_talk_open(wk):
                if wk.find_str_click(*RECT_TALK, "擂台报名", COLOR_TALK_ITEM):
                    msleep(600)
                    wk.find_str_click(*RECT_TALK, "战队报名", COLOR_TALK_ITEM)
                    wk.record("正在进入比武擂台...")
                    msleep(600)
                    if cls.is_talk_show_info(wk, "你的战队已有队伍报名"):
                        wk.record("你的战队已有队伍报名")
                        cls.close_pages(wk)
                        return False
                cls.close_other_talk(wk)
            if i == 0 or wk.is_stuck:
                cls.click_system_task_name(wk)
            msleep(600)
            if cls.cur_map(wk) == "比武擂台":
                wk.record("已进入比武擂台, 准备战斗...")
                break
        return True
    
    @classmethod
    def handle_switch_line(cls, wk: Worker):
        cls.lead_team_switch_line(wk, '四')
    
    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday not in [1, 4, 6, 7]:
            wk.record(f"团体擂台只能在星期一四六七, 今天是星期{weekday}")
            return False
        return True
    
    



class TaskShouWei(TaskMingGuaiBase):
    TASK_NAME = "守卫师门"
    NEED_AVOID_FIGHT = False
    MONSTER_NAME = "蒙军|武林败类|叛变长老|蒙军先锋|蒙军统领|变节武林人|蒙军亲兵|大亲王|东征统帅"
    TALK_CONTENT = "接招吧"

    @classmethod
    def get_dx(cls):
        return rnd(14, 35)


class TaskChanCaiSanGuan(TaskMingGuaiBase):
    TASK_NAME = "善财三官"
    IS_DIFFICULT_TASK = True
    NEED_AVOID_FIGHT = True
    MONSTER_NAME = "善财童子|天官|地官|水官|马贼"
    TALK_CONTENT = "看打|进入战斗|接受|接招吧"
    '''
    天降福泽是三官, 每天20只
    真假财神是打善财童子
    '''

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=23, minute=30, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        map_list = ["十字坡", "有座山", "龙门", "桃花林"]
        if cls.cur_map(wk) not in map_list:
            # 从 map_list 中 随机选一个
            random_map = random.choice(map_list)
            wk.record(f"当前不在 活动地图, 正在随机前往 {random_map}...")
            wk.exclude_pos = MAP_NPC_XY.get(random_map, set())
            cls.run_to_map(wk, random_map)

    @classmethod
    def go_to_another_map(cls, wk):
        map_list = ["十字坡", "有座山", "龙门", "桃花林"]
        cur_map_name = cls.cur_map(wk)
        if cur_map_name in map_list:
            map_list.remove(cur_map_name)
        random_map = random.choice(map_list)
        wk.record(f"没怪了, 当前在 {cur_map_name}, 正在随机前往 {random_map}...")
        wk.exclude_pos = MAP_NPC_XY.get(random_map, set())
        cls.run_to_map(wk, random_map)
        
    @classmethod
    def get_dx(cls):
        return rnd(14, 35)