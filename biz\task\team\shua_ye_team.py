from PySide2.QtCore import QTime

from biz.task._shua_ye_base import TaskShuaYeBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker
import settings


class TaskShuaYeTeam(TaskShuaYeBase):
    TASK_NAME = "带队刷野"
    IS_TASK_FIX_EQUIP_BB_ENABLE = True
    IS_TEAM_TASK = True
    
    @classmethod
    def run(cls, wk: Worker):
        start_ts = settings.cur_time_stamp
        cls.go_to_shuaye_map(wk)
        while True:
            if wk.is_fight:
                cls.fight_operation(wk)
                if cls.do_fix_bb(wk):
                    cls.go_to_shuaye_map(wk)
                wk.done_count += 1
                wk.record(f"已完成 {wk.done_count} 次")
            if wk.is_stuck:
                cls.auto_meet_enemy_by_shortcut(wk)
            msleep(800)
            total_seconds = wk.cfg_plan_task["刷野时间"] * 60
            if cls.is_reach_time(wk, start_ts, total_seconds):
                wk.record("时间到, 自动结束")
                break
        cls.back_to_kai_feng(wk)

    @classmethod
    def is_reach_time(cls, wk: Worker, start_ts: int, total_seconds: int):
        if settings.cur_time_stamp - start_ts > total_seconds:
            wk.record("超出设定时长，自动结束")
            return True
        if wk.cfg_plan_task["定时结束"]:
            if wk.cur_task_start_time_fmt > wk.cfg_plan_task["定时结束时间"]:
                wk.record("任务开始时间超过定时结束时间, 忽略定时结束")
                wk.cfg_plan_task["定时结束"] = False
                return False
            end_time_fmt = wk.cfg_plan_task["定时结束时间"]
            if end_time_fmt == "19:30" and get_week_day() == 2:
                end_time_fmt = "20:30"
            if settings.cur_time_fmt[:5] >= end_time_fmt:
                wk.record("定时结束时间到")
                return True
        return False
    
    @classmethod
    def fight_yan_nan_tian(cls, wk: Worker):
        if wk.find_str_click(*RECT_TALK, "那晚辈就得罪了", COLOR_TALK_ITEM):
            wk.fight_yan = True
            return True
        return False
            
    @classmethod
    def go_to_shuaye_map(cls, wk: Worker):
        if cls.cur_map(wk) != wk.shuaye_map:
            wk.record(f"前往刷野地图: {wk.shuaye_map}")
            cls.run_to_map(wk, wk.shuaye_map)
        cls.use_hls(wk)
        cls.close_pages(wk)
            
    @classmethod
    def recognize_enemy(cls, wk: Worker):
        if wk.find_str(*RECT_FIGHT_ENEMY, "天枫十四郎", COLOR_RED):
            super().recognize_enemy(wk)
            wk.team.fight_random = True

    @classmethod
    def people_fight_action(cls, wk: Worker):
        if wk.team.fight_random and cls.is_people_action(wk):
            if wk.team.true_enemy_pos != (-1, -1):
                wk.record("攻击 真身")
                wk.move_relative_click(*wk.team.true_enemy_pos)
                return
            idx = rnd(0, 9)
            x, y = POS_ENENMY_LIST[idx]
            wk.record(f"发现 天枫十四郎，人物随机攻击:{idx}")
            wk.move_relative_click(x, y - rnd(50, 60))
            return
        return super().people_fight_action(wk)
    
    @classmethod
    def bb_fight_action(cls, wk: Worker):
        if wk.team.fight_random and cls.is_bb_action(wk):
            if wk.team.true_enemy_pos != (-1, -1):
                wk.record("攻击 真身")
                wk.move_relative_click(*wk.team.true_enemy_pos)
                return
            idx = rnd(0, 9)
            x, y = POS_ENENMY_LIST[idx]
            wk.record(f"发现 天枫十四郎，BB随机攻击:{idx}")
            wk.move_relative_click(x, y - rnd(50, 60))
            return
        return super().bb_fight_action(wk)
    
    @classmethod
    def fight_close_talk(cls, wk: Worker):
        if wk.cur_round == 1:
            if cls.talk_click_specify_item(wk, "得罪了", timeout=0):
                wk.record("偶遇 燕南天, 开打")
            cls.close_other_talk(wk)

    @classmethod
    def guard_do_something(cls, wk: Worker):
        if not wk.is_fight:
            return
        if not wk.team.fight_random:
            return
        if wk.team.true_enemy_pos != (-1, -1):
            return
        x, y = wk.get_str_pos(*RECT_FIGHT_ENEMY, "真身在此", COLOR_WHITE)
        if x > 0:
            wk.record(f"发现 真身, 坐标是 {x}, {y}")
            wk.team.true_enemy_pos = (x+29, y+116)
            
    @classmethod
    def enable_task_fix_equip_bb(cls, wk: Worker):
        return wk.cfg_plan_task["修理忠诚"]

    @classmethod
    def get_default_biz_config(cls):
        return {
            "刷野地图": "玄霜浅滩",
            "刷野时间": 1000,
            "修理忠诚": True,
            "定时结束": True,
            "定时结束时间": "20:33",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_shuaye_team_map.setCurrentText(cls.CONFIG["刷野地图"])
        settings.wnd_main.edt_shuaye_team_time.setText(str(cls.CONFIG["刷野时间"]))
        settings.wnd_main.chk_shuaye_team_fix_bb.setChecked(cls.CONFIG["修理忠诚"])
        settings.wnd_main.groupBox_time_stop_shua_ye_team.setChecked(cls.CONFIG["定时结束"])
        settings.wnd_main.tedt_timer_shua_ye_team.setTime(QTime.fromString(cls.CONFIG["定时结束时间"], "HH:mm"))
        
    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["刷野地图"] = settings.wnd_main.cmb_shuaye_team_map.currentText()
        cls.CONFIG["刷野时间"] = int(settings.wnd_main.edt_shuaye_team_time.text())
        cls.CONFIG["修理忠诚"] = settings.wnd_main.chk_shuaye_team_fix_bb.isChecked()
        cls.CONFIG["定时结束"] = settings.wnd_main.groupBox_time_stop_shua_ye_team.isChecked()
        cls.CONFIG["定时结束时间"] = settings.wnd_main.tedt_timer_shua_ye_team.time().toString("HH:mm")
        super().cfg_save(plan_name)
    