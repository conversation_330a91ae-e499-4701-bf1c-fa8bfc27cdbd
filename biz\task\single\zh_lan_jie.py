from utils import *
from biz.task.__base import TaskBase
from biz.constants.constants import *
from biz.obj.worker import Worker


class TaskLanJie(TaskBase):
    TASK_NAME = "拦截商旅"

    @classmethod
    def before_run(cls, wk: Worker):
        # 重置任务类型和完成次数
        cls.set_task_config(wk)
        wk.done_count = 0

    @classmethod
    def run(cls, wk: Worker):
        cls.open_shang_lv_page(wk)
        msleep(600)
        for i in range(10000):
            if cls.click_confirm(wk, timeout=0):
                msleep(500)
                break
            if wk.find_pic_click(*RECT_FULL, "继续.bmp|继续2.bmp"):
                msleep(1000)
            wk.find_pic_click(*RECT_FULL, "结果.bmp", timeout=300)
            msleep(500)
        
    @classmethod
    def after_run(cls, wk: Worker):
        wk.record("正在关闭界面...")
        wk.move_click(*POS_CLOSE_SHENGLI, re_move=False)
        msleep(800)
        cls.close_pages(wk)
        super().after_run(wk)

    @classmethod
    def open_shang_lv_page(cls, wk: Worker):
        if wk.find_pic(*RECT_FULL, "结果.bmp"):
            wk.record("已在商旅界面")
            return
        wk.record("正在打开商旅界面...")
        cls.close_pages(wk)
        cls.open_activity_page(wk)
        if not cls.is_activity_page_open(wk):
            wk.record("打开活动界面失败")
            return
        wk.move_click(*POS_SHANGLV, re_move=False)
        msleep(600)
        wk.move_click(*POS_LIJICANJIA)
        msleep(600)
        wk.move_click(*POS_LANJIE)
        msleep(600)
        cls.select_difficulty(wk)
        msleep(600)
        wk.move_click(*POS_FAQILANJIE)
        msleep(600)
        cls.click_confirm(wk, timeout=400)
        
    @classmethod
    def select_difficulty(cls, wk: Worker):
        difficulty = wk.cfg_plan_task["商旅难度"]
        wk.record(f"用户设定难度为: {difficulty}")
        if difficulty != "极度危险":
            color = "|".join([COLOR_WHITE, COLOR_QUALITY_BLUE, COLOR_QUALITY_GREEN, COLOR_GOLD])
            if wk.find_str_click(*RECT_DIFFICULTY, difficulty, color, timeout=400):
                wk.record(f"选择难度 {difficulty} 成功")
                return
        color = COLOR_RED
        wk.move_click(*POS_SHANG_LV_DOWN, click_count=8)
        if wk.find_str_click(*RECT_DIFFICULTY, difficulty, color, timeout=400):
            wk.record(f"选择难度 {difficulty} 成功")
            return
        wk.move_click(*POS_YIBAN)
        wk.record(f"选择难度 {difficulty} 失败, 自动选择一般难度")

    @classmethod
    def get_default_biz_config(cls):
        return {
            "商旅难度": "有风险",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_shang_lv.setCurrentText(cls.CONFIG["商旅难度"])
        
    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["商旅难度"] = settings.wnd_main.cmb_shang_lv.currentText()
        super().cfg_save(plan_name)
