from PySide2.QtCore import QTime

from biz.task._shua_ye_base import TaskShuaYeBase
from biz.task.single.qing_li import TaskQingLi
from biz.task.single.jiao_jie_huo import TaskJiaoHuo
from biz.task.single.zh_lan_jie import Task<PERSON>an<PERSON>ie
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker
import settings


class TaskShuaYeSingle(TaskShuaYeBase):
    TASK_NAME = "单人刷野"

    @classmethod
    def run(cls, wk: Worker):
        cls.leave_team(wk)
        start_ts = settings.cur_time_stamp
        if wk.cfg_plan_task["清理背包"] and cls.is_bag_full(wk):
            wk.record("背包满了")
            wk.need_clear_bag = True
        if wk.need_clear_bag:
            # 优先原地丢东西
            cls.throw_baggage(wk)
            if not TaskQingLi.clean_temp_bag(wk):
                wk.record("清理临时背包失败, 正在回城清包...")
                cls.clear_bag_and_back_map(wk)
            else:
                cls.go_to_shuaye_map(wk)
        else:
            cls.go_to_shuaye_map(wk)
        last_check_gift_ts = 0
        last_check_hu_song_ts = 0
        while True:
            if wk.is_fight:
                cls.fight_operation(wk)
                wk.done_count += 1
                wk.record(f"已完成 {wk.done_count} 次")
                # 交罚款
                if wk.need_pay_fine:
                    cls.pay_fine(wk)
                    if not wk.need_fix_equip_bb and not wk.need_clear_bag:
                        cls.go_to_shuaye_map(wk)
                # 满足条件回城
                cls.clear_bag_and_back_map(wk)
            if wk.is_die:
                wk.record("死亡看桃花了...")
                if wk.cfg_plan_task["死亡继续"]:
                    wk.record("死亡继续返回刷野地图")
                    cls.go_to_shuaye_map(wk)
                    wk.is_die = False
                else:
                    msleep(10*60*1000)  # 10分钟提示一次
                    continue
            if wk.is_stuck:
                if wk.cfg_plan_task["签到祥瑞"]:
                    if settings.cur_time_stamp - last_check_gift_ts > 60:
                        if cls.is_gift_enable(wk):
                            cls.get_gift(wk)
                        if cls.is_fest_enable(wk):
                            cls.get_fest(wk)
                        last_check_gift_ts = settings.cur_time_stamp
                if wk.cfg_plan_task["商旅护送"]:
                    if settings.cur_time_stamp - last_check_hu_song_ts > 15*60:
                        cls.hu_song(wk)
                        last_check_hu_song_ts = settings.cur_time_stamp
                cls.walk_to_run(wk)
                if rnd(0, 10) == 1 and wk.cfg_plan_task["死亡继续"] and cls.cur_map(wk) != wk.shuaye_map:
                    wk.record("检测到不在刷野地图, 正在返回...")
                    cls.go_to_shuaye_map(wk)
                    continue
                cls.auto_meet_enemy_by_shortcut(wk)
            msleep(800)
            total_seconds = wk.cfg_plan_task["刷野时间"] * 60
            if cls.is_reach_time(wk, start_ts, total_seconds):
                wk.record("时间到, 自动结束")
                break

    @classmethod
    def hu_song(cls, wk: Worker):
        wk.record("触发 商旅护送")
        cls.stop_auto_find_way(wk)
        cls.open_activity_page(wk)
        if not cls.is_activity_page_open(wk):
            return
        msleep(400)
        wk.move_click(733, 141)  # 护送
        msleep(800)
        wk.move_click(637, 522)  # 立即参加
        msleep(400)
        if wk.find_pic_click(*RECT_FULL, "领取奖励.bmp", timeout=400):
            wk.record("商旅护送 领取奖励")
            wk.move_click(441, 391)  # 领取奖励的确定
            msleep(400)
        # 护送设置难度
        TaskLanJie.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
        TaskLanJie.select_difficulty(wk)
        cls.set_task_config(wk)  # 还原
        msleep(400)
        wk.record("商旅护送 开始护送")
        wk.move_click(639, 474)  # 开始护送
        msleep(400)
        cls.click_confirm(wk)  # 开始护送的确定
        msleep(400)
        if cls.is_popup_show_info(wk, "护送失败"):
            wk.record("护送次数已经消耗完了, 不再触发护送")
            cls.click_confirm(wk)
            wk.cfg_plan_task["商旅护送"] = False
        wk.key_press(VK_ESC)
        msleep(400)
        cls.close_pages(wk)

    @classmethod
    def go_to_shuaye_map(cls, wk: Worker):
        wk.re_move()  # 把地图刷新一下
        msleep(600)
        wk.record(f"前往刷野地图: {wk.shuaye_map}")
        cls.run_to_map_run_away(wk, wk.shuaye_map)
        cls.use_hls(wk)
        cls.close_pages(wk)

    @classmethod
    def fight_operation(cls, wk: Worker):
        super().fight_operation(wk)
        if wk.cfg_plan_task["刷燕南天"] and wk.fight_yan:
            wk.fight_yan_count += 1
            wk.record(f"燕南天个数统计:{wk.fight_yan_count}")
        # 战斗后
        if wk.cfg_plan_task["精炼BB"] and wk.fight_meet_bb:
            wk.record("开始精炼BB...")
            cls.stop_auto_find_way(wk, force=True)
            cls.terse_bb(wk)
            wk.is_stuck = True
        if wk.cfg_plan_task["清理背包"] and cls.is_bag_full(wk):
            wk.record("背包满了")
            wk.need_clear_bag = True

    @classmethod
    def clear_bag_and_back_map(cls, wk: Worker):
        # 如果还是需要清理才回城
        if wk.need_fix_equip_bb or (wk.need_clear_bag and wk.cfg_plan_task["清理背包"]):
            if wk.need_clear_bag:
                # 优先丢东西
                cls.throw_baggage(wk)
                if TaskQingLi.clean_temp_bag(wk):
                    wk.need_clear_bag = False
                    wk.record("原地清理背包成功, 暂不回城")
                    if not wk.need_fix_equip_bb:
                        return
            wk.record("触发回城条件")
            cls.stop_auto_find_way(wk)
            cls.back_to_kai_feng(wk)
            if wk.cfg_plan_task["优先交货"]:
                TaskJiaoHuo.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
                TaskJiaoHuo.run(wk)
                cls.set_task_config(wk)  # 还原
            if wk.cfg_plan_task["清理背包"]:
                TaskQingLi.set_task_config(wk, cls.TASK_NAME)  # 要读这个功能的配置
                TaskQingLi.run(wk)
                cls.set_task_config(wk)  # 还原
            if wk.cfg_plan_task["修理忠诚"]:
                cls.fix_equip(wk)
                cls.fix_bb(wk)
            if wk.cfg_plan_task["精炼BB"]:
                cls.terse_bb(wk)
            if wk.cfg_plan_task["编驯马绳"]:
                cls.bian_xun_ma(wk)
            # 回到刷野地图
            wk.need_fix_equip_bb, wk.need_clear_bag = False, False
            cls.go_to_shuaye_map(wk)

    @classmethod
    def people_catch_bb(cls, wk: Worker):
        if not cls.is_people_action(wk):
            return
        if wk.cfg_plan_task["收服麒麟"]:
            color = "|".join([COLOR_QI_LING1, COLOR_QI_LING2, COLOR_QI_LING3])
            if cls.do_catch(wk, "麒麟", color):
                wk.fight_meet_bb = True
                return
        if wk.cfg_plan_task["抓野怪"]:
            name = wk.cfg_plan_task["野怪名称"]
            if not name:
                wk.record("勾了抓野怪, 却没填野怪名称")
                return
            color = "|".join([COLOR_QI_LING1, COLOR_RED])
            if cls.do_catch(wk, name, color):
                wk.fight_meet_name = True
                return

    @classmethod
    def do_catch(cls, wk: Worker, name: str, color: str):
        x, y = wk.get_str_pos(*RECT_FIGHT_ENEMY, name, color, timeout=200)
        if x < 0:
            return False
        wk.record(f"遇到 {name}，正在收服...")
        wk.find_pic_click(*RECT_FULL, "收服.bmp|收服2.bmp")
        wk.record(f"正在收服 {name}...")
        if name == "麒麟":
            x = x + rnd(-5, 5)
        else:
            x = x + rnd(30, 40)
        wk.move_relative_click(x, y + rnd(60, 90))
        msleep(400)
        if cls.is_system_broad_show(wk, "你的随从太多了"):
            wk.record("你的随从太多了")
        return True

    @classmethod
    def fight_close_talk(cls, wk: Worker):
        cls.close_other_talk(wk)

    @classmethod
    def enable_task_fix_equip_bb(cls, wk: Worker):
        return cls.IS_TASK_FIX_EQUIP_BB_ENABLE

    @classmethod
    def fight_do_something(cls, wk: Worker):
        random_number = rnd(0, 6)
        if random_number == 0:
            if settings.cur_time_stamp - wk.fight_start_ts > 180 and (wk.find_color(178, 173, 294, 229, COLOR_RED) or wk.find_pic(406, 222, 445, 276, "0.bmp")):
                wk.record("检测到卡战斗, 退出游戏重登")
                cls.return_login(wk)
                msleep(1000)
                return
        elif random_number == 1:
            if wk.cfg_plan_task.get("修理忠诚") and cls.is_system_show_fix_equib_bb(wk):
                wk.record("需要修理忠诚")
                wk.need_fix_equip_bb = True
        elif random_number == 2:  # 隔一段时间检测下是否死亡
            if wk.find_pic_click(*RECT_FIGHT_DIE, "死亡回桃花岛.bmp"):
                wk.record("人物死亡回桃花岛")
                wk.is_die = True

        if wk.need_pay_fine:
            return
        if random_number == 3 and cls.is_system_broad_show(wk, "范歪瓜"):
            wk.record("需要交纳罚款")
            wk.need_pay_fine = True

    @classmethod
    def fight_yan_nan_tian(cls, wk: Worker):
        if not wk.cfg_plan_task.get("刷燕南天"):  # 这个地方可能被before_run调用, 还没读到配置
            return False
        if wk.find_str_click(*RECT_TALK, "那晚辈就得罪了", COLOR_TALK_ITEM):
            wk.fight_yan = True
            return True
        return False

    @classmethod
    def people_run_away(cls, wk: Worker):
        """
        返回False表示不要逃跑
        要逃跑的场景: 
        勾了刷燕南天却没有燕南天 或者 
        没勾打恶人榜却遇到恶人榜  或者
        勾了收服麒麟却没有麒麟 或者
        勾了抓野怪却没有野怪
        """
        if wk.should_run_away:
            wk.record("未到刷野地图, 逃跑")
            cls.click_run_away(wk)
            return
        if wk.find_str(*RECT_FIGHT_ENEMY, "随从", COLOR_GOLD):  # 没勾打恶人, 有则逃跑
            wk.record("遇到恶人榜...")
            wk.fight_e_ren = True
            if not wk.cfg_plan_task["打恶人榜"] and cls.click_run_away(wk):
                wk.record("遇到恶人榜, 撤退...")
                cls.click_run_away(wk)
                return
        if not wk.cfg_plan_task["无则撤退"]:
            return
        if wk.cfg_plan_task["刷燕南天"]:
            if wk.cfg_plan_task["打恶人榜"] and wk.fight_e_ren:  # 如果勾了打恶人就打, 不用逃跑
                return False
            if not wk.fight_yan and not wk.find_str(*RECT_FIGHT_ENEMY, "名人|杜傻", COLOR_GOLD) and cls.click_run_away(wk):
                wk.record("没有燕南天, 撤退...")
        if wk.cfg_plan_task["抓野怪"]:
            if wk.fight_yan or wk.fight_e_ren:
                return False
            if not wk.fight_meet_name and not wk.fight_meet_bb and cls.click_run_away(wk):
                wk.record("没有遇到指定野怪, 撤退...")
                return
        if wk.cfg_plan_task["收服麒麟"]:
            if wk.fight_yan or wk.fight_e_ren:
                return False
            if not wk.fight_meet_bb and cls.click_run_away(wk):
                wk.record("没有麒麟, 撤退...")
        return False
    
    @classmethod
    def guard_do_something(cls, wk: Worker):
        if wk.find_pic(464, 276, 513, 327, "场景加载中.bmp"):
            wk.fail_count += 1
            if wk.fail_count > 30:
                wk.record("游戏卡点场景读取, 正在离线重登...")
                cls.return_login(wk)
                msleep(1000)
                return
        else:
            wk.fail_count = 0

    @classmethod
    def get_default_biz_config(cls):
        return {
            "刷野地图": "恶人谷",
            "刷野时间": 99999,
            "优先交货": False,
            "修理忠诚": True,
            "精炼BB": False,
            "清理背包": True,
            "编驯马绳": True,
            "收服麒麟": True,
            "打恶人榜": False,
            "刷燕南天": False,
            "无则撤退": False,
            "死亡继续": False,
            "定时结束": False,
            "商旅护送": False,
            "签到祥瑞": False,
            "定时结束时间": "14:04",
            "抓野怪": False,
            "野怪名称": "蒙军千户那颜|内陵百将俑",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_shuaye_map.setCurrentText(cls.CONFIG["刷野地图"])
        settings.wnd_main.edt_shuaye_time.setText(str(cls.CONFIG["刷野时间"]))
        settings.wnd_main.chk_shuaye_deliver.setChecked(cls.CONFIG["优先交货"])
        settings.wnd_main.chk_shuaye_fix_bb.setChecked(cls.CONFIG["修理忠诚"])
        settings.wnd_main.chk_shuaye_terse_bb.setChecked(cls.CONFIG["精炼BB"])
        settings.wnd_main.chk_shuaye_qingli.setChecked(cls.CONFIG["清理背包"])
        settings.wnd_main.chk_shuaye_xun_ma.setChecked(cls.CONFIG["编驯马绳"])
        settings.wnd_main.chk_shuaye_catch_bb.setChecked(cls.CONFIG["收服麒麟"])
        settings.wnd_main.chk_shuaye_fight_e_ren.setChecked(cls.CONFIG["打恶人榜"])
        settings.wnd_main.chk_shuaye_fight_yan.setChecked(cls.CONFIG["刷燕南天"])
        settings.wnd_main.chk_shuaye_che_tui.setChecked(cls.CONFIG["无则撤退"])
        settings.wnd_main.chk_shuaye_die_continue.setChecked(
            cls.CONFIG["死亡继续"])
        settings.wnd_main.groupBox_time_stop_shua_ye.setChecked(
            cls.CONFIG["定时结束"])
        settings.wnd_main.chk_shuaye_qiandao.setChecked(cls.CONFIG["签到祥瑞"])
        settings.wnd_main.chk_shuaye_hu_song.setChecked(cls.CONFIG["商旅护送"])
        settings.wnd_main.tedt_timer_shua_ye.setTime(
            QTime.fromString(cls.CONFIG["定时结束时间"], "HH:mm"))
        settings.wnd_main.chk_shuaye_catch_name.setChecked(cls.CONFIG["抓野怪"])
        settings.wnd_main.edt_shuaye_name.setText(cls.CONFIG["野怪名称"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["刷野地图"] = settings.wnd_main.cmb_shuaye_map.currentText()
        cls.CONFIG["刷野时间"] = int(settings.wnd_main.edt_shuaye_time.text())
        cls.CONFIG["优先交货"] = settings.wnd_main.chk_shuaye_deliver.isChecked()
        cls.CONFIG["修理忠诚"] = settings.wnd_main.chk_shuaye_fix_bb.isChecked()
        cls.CONFIG["精炼BB"] = settings.wnd_main.chk_shuaye_terse_bb.isChecked()
        cls.CONFIG["清理背包"] = settings.wnd_main.chk_shuaye_qingli.isChecked()
        cls.CONFIG["编驯马绳"] = settings.wnd_main.chk_shuaye_xun_ma.isChecked()
        cls.CONFIG["收服麒麟"] = settings.wnd_main.chk_shuaye_catch_bb.isChecked()
        cls.CONFIG["打恶人榜"] = settings.wnd_main.chk_shuaye_fight_e_ren.isChecked()
        cls.CONFIG["刷燕南天"] = settings.wnd_main.chk_shuaye_fight_yan.isChecked()
        cls.CONFIG["无则撤退"] = settings.wnd_main.chk_shuaye_che_tui.isChecked()
        cls.CONFIG["死亡继续"] = settings.wnd_main.chk_shuaye_die_continue.isChecked()
        cls.CONFIG["定时结束"] = settings.wnd_main.groupBox_time_stop_shua_ye.isChecked()
        cls.CONFIG["签到祥瑞"] = settings.wnd_main.chk_shuaye_qiandao.isChecked()
        cls.CONFIG["商旅护送"] = settings.wnd_main.chk_shuaye_hu_song.isChecked()
        cls.CONFIG["定时结束时间"] = settings.wnd_main.tedt_timer_shua_ye.time().toString(
            "HH:mm")
        cls.CONFIG["抓野怪"] = settings.wnd_main.chk_shuaye_catch_name.isChecked()
        cls.CONFIG["野怪名称"] = settings.wnd_main.edt_shuaye_name.text()
        super().cfg_save(plan_name)
