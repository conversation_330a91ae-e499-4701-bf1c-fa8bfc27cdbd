import copy
from PySide2.QtCore import QObject, QThread, Signal
import pythoncom
import requests
import win32gui
import grpc
import traceback

from api.netauth.v1 import card_pb2, card_pb2_grpc
from biz.constants.constants import *
from biz.task.__base import TaskBase
from biz.task.other import TaskQuitGame
from const import const
from utils import *
from biz.obj.worker import Worker
import settings


class ThreadExec(QThread):
    def __init__(self, wk: Worker):
        super().__init__()
        self.wk = wk
        self.thread_guard = ThreadGuard(wk)

    def run(self):
        wk = self.wk
        pythoncom.CoInitialize()
        activate_wnd(wk.hwnd)
        wk.init_flag()

        wk.is_run, wk.is_pause, wk.is_end = True, False, False
        self.thread_guard.start()  # 启动检测线程

        cmb_plan = settings.cmb_plan_list[wk.row]
        cmb_plan.setEnabled(False)
        wk.plan_name = cmb_plan.currentText()
        # 把方案配置保存到worker中
        if wk.plan_name == "":  # 没选方案, 读取当前控件的配置
            settings.cur_cfg_plan = copy.deepcopy(settings.default_cfg_plan)
            settings.cur_cfg_plan.update(
                settings.wnd_main.get_basic_plan_setting_dict())
            settings.wnd_main.save_biz_task_setting_dict_from_control("")
            # 这里再拷贝一份, 不然可能会随下一次settings.cur_cfg_plan改变而改变
            wk.cfg_plan = copy.deepcopy(settings.cur_cfg_plan)
        else:  # 选了方案
            wk.cfg_plan = copy.deepcopy(settings.default_cfg_plan)
            wk.cfg_plan.update(settings.cfg_plan_dict.get(wk.plan_name, {}))

        task_list = wk.cfg_plan["执行列表"]
        check_list = wk.cfg_plan["执行列表选中"]
        taskCls = None
        is_first_task = True
        flag = False # 清空窗口行的标志位
        for idx, task_name in enumerate(task_list):
            if task_name == "":
                wk.record("此方案未添加任务!")
                break
            wk.cur_task_idx = idx
            taskCls = settings.task_dict.get(task_name)
            if taskCls:
                if len(check_list) > idx and not check_list[idx]:  # 如果没勾就会
                    continue
                if not taskCls.check_run(wk):
                    continue
                if is_first_task and taskCls.NEED_PLAYER_NAME:
                    wk.player_name = TaskBase.ocr_player_name(wk)
                    TaskBase.close_pages(wk)
                    TaskBase.minimize_talk_region(wk)
                    if wk.player_name:
                        settings.wnd_main.sig_cell.emit(
                            wk.row, COL_NAME, wk.player_name)
                    else:
                        wk.player_name = settings.wnd_main.tbe_console.item(
                            wk.row, COL_NAME).text()
                is_first_task = False
                try:
                    taskCls.before_run(wk)
                    taskCls.run(wk)
                    taskCls.after_run(wk)
                except ZeroDivisionError as e:
                    wk.record(f"{task_name} 除零异常: {e}\n{traceback.format_exc()}")
                except Exception as e:
                    wk.record(f"{task_name} 执行异常: {e}\n{traceback.format_exc()}")
                if taskCls is TaskQuitGame:
                    flag = True
                    break
            else:
                wk.record(f"{task_name} 不存在, 请检查该功能是否已改名")
            msleep(600)

        wk.cur_task = ""
        if task_list:
            wk.record("任务执行完毕")
        else:
            wk.record("未配置方案执行列表!")
        wk.is_run, wk.is_pause, wk.is_end = False, False, True
        wk.show_in_tbe_console(const.COL_END, const.SELECTED)
        if taskCls is TaskQuitGame and flag:
            settings.wnd_main.sig_remove.emit(wk)

    def pause(self):
        wk = self.wk
        if not wk.is_fight:
            TaskBase.stop_auto_find_way(wk)
        wk.mutex.lock()
        wk.record("任务暂停执行")
        wk.is_run, wk.is_pause, wk.is_end = False, True, False

    def resume(self):
        wk = self.wk
        wk.mutex.unlock()
        wk.record("任务恢复执行")
        wk.is_run, wk.is_pause, wk.is_end = True, False, False
        wk.is_stuck = True
        wk.mate_check_leave = True  # 每一次恢复运行完都要检查是不是离队了

    def end(self):
        wk = self.wk
        is_pause = wk.is_pause
        if not is_pause:
            if not wk.is_fight:
                TaskBase.stop_auto_find_way(wk)
            if not wk.mutex.tryLock(timeout=1000):
                return False
        wk.record("任务即将终止...")
        self.terminate()
        self.wait()
        wk.record("任务终止执行")
        wk.is_run, wk.is_pause, wk.is_end = False, False, True
        wk.show_in_tbe_console(const.COL_END, const.SELECTED)
        wk.mutex.unlock()
        return True


class ThreadGuard(QThread):
    def __init__(self, wk: Worker):
        super().__init__()
        self.wk = wk
        self.last_check_stuck_ts = int(time.time())
        self.before_color1 = COLOR_BLACK
        self.before_color2 = COLOR_BLACK
        self.before_color3 = COLOR_BLACK
        self.before_x = -1
        self.before_y = -1

    def run(self):
        wk = self.wk
        pythoncom.CoInitialize()
        sleep_gap = 600
        while not wk.is_end:
            # 自动登录
            TaskBase.input_account_password_login(wk)
            # 是否在战斗中
            wk.is_fight = TaskBase.is_fight(wk)
            if wk.is_fight:
                if wk.cur_task_cls and not wk.cur_task_cls.fight_check_boss_wu_xing(wk):
                    msleep(100)
                    continue
                # 战斗中自动投点
                wk.find_pic_click(*RECT_FULL, "投点.bmp")
                msleep(sleep_gap)  # 战斗中投点不急，可以再慢点
            if not wk.is_fight or (wk.is_fight and rnd(0, 20) == 1):
                if wk.cur_task_cls:
                    wk.cur_task_cls.pass_overlap_auth(wk)
                else:
                    TaskBase.pass_overlap_auth(wk)
            if wk.cur_task_cls:
                wk.cur_task_cls.guard_do_something(wk)
            # 检测是否卡点，卡点则置寻路标志位
            self.check_stuck(wk)
            msleep(sleep_gap)

    def check_stuck(self, wk: Worker):
        if settings.cur_time_stamp - self.last_check_stuck_ts < 2:  # 2秒检测一次
            return
        if wk.get_thread_stack_addr() == "-1":
            self.stuck_method_pic(wk)
        else:
            self.stuck_method_mem(wk)

    def stuck_method_pic(self, wk: Worker):
        self.last_check_stuck_ts = settings.cur_time_stamp
        color1 = wk.get_pos_color(*POS_CHECK_STUCK1)
        color2 = wk.get_pos_color(*POS_CHECK_STUCK2)
        color3 = wk.get_pos_color(*POS_CHECK_STUCK3)
        # 三个全黑, 说明现在还在地图加载中，不算卡点
        if color1 == COLOR_BLACK and color2 == COLOR_BLACK and color3 == COLOR_BLACK:
            self.before_color1, self.before_color2, self.before_color3 = (
                color1,
                color2,
                color3,
            )
            wk.is_stuck = False
            return
        # 只要三个点中有一个点颜色没变
        if color1 == self.before_color1 or color2 == self.before_color2 or color3 == self.before_color3:
            # 且有至少一个点不为黑色, 说明现在卡点了
            if color1 != COLOR_BLACK or color2 != COLOR_BLACK or color3 != COLOR_BLACK:
                self.before_color1, self.before_color2, self.before_color3 = (
                    color1,
                    color2,
                    color3,
                )
                self.handle_stuck(wk)
                return
        self.before_color1, self.before_color2, self.before_color3 = (
            color1,
            color2,
            color3,
        )
        wk.is_stuck = False

    def stuck_method_mem(self, wk: Worker):
        x = wk.get_x()
        y = wk.get_y()
        if x == -1 or y == -1:
            return self.stuck_method_pic(wk)
        self.last_check_stuck_ts = settings.cur_time_stamp
        if x == self.before_x and y == self.before_y:
            self.before_x = x
            self.before_y = y
            self.handle_stuck(wk)
            return
        self.before_x = x
        self.before_y = y
        wk.is_stuck = False

    def handle_stuck(self, wk: Worker):
        wk.is_stuck = True
        if rnd(0, 5) == 1 and wk.is_display_dead(*RECT_CAPTURE, 1):
            if wk.is_end:
                return
            wk.record("窗口卡屏了, 正在恢复...")
            wk.set_window_state(wk.hwnd, "恢复")
            wk.focus_window(wk.hwnd)  # 自动设置焦点，避免卡屏
            wk.record("窗口卡屏恢复成功!")


class ThreadLogin(QThread):
    def __init__(self, game_path: str, start_row=0, end_row=TBE_CONSOLE_ROW):
        super().__init__()
        self.start_row = start_row
        self.end_row = end_row
        self.game_path = game_path

    def run(self):
        pythoncom.CoInitialize()
        for row in range(self.start_row, self.end_row):
            # 判断该窗口是否还存在，不存在的话清除掉
            hwnd = settings.wnd_main.tbe_console.item(row, COL_HWND).text()
            if hwnd:
                if win32gui.IsWindow(int(hwnd)):  # 存在有效的窗口句柄，则跳过
                    continue
                else:  # 有窗口句柄但无效，先清除掉再登录
                    wk = settings.worker_list[row]
                    wk.record("窗口已失效，自动清除")
                    settings.wnd_main.sig_remove.emit(wk)
            # 没填账号信息，自动跳过
            if not settings.wnd_main.tbe_console.item(row, COL_ACCOUNT).text():
                continue
            # 创建游戏窗口进程, 并创建对应的wk
            wk = self.create_new_game_window_and_create_wk(row)
            if not wk:
                continue
            log.info("正在登录...")
            msleep(1000)
            Thread(target=TaskBase.input_account_password_login,
                   args=(wk,), daemon=True).start()

        settings.wnd_main.sig_arrange.emit()
        settings.wnd_main.btn_start_stop_login.setText("开始登录")

    def create_new_game_window_and_create_wk(self, row):
        # 创建游戏进程
        hwnd = settings.wnd_main.create_game_process()
        if not hwnd:  # 说明创建失败了
            return
        # 获取窗口状态
        state = settings.global_wk.get_window_state(hwnd)
        log.info(f'state:{state} hwnd:{hwnd}')
        # if state == 1:  # 无响应
        #     terminate_wnd(hwnd)
        #     self.create_new_game_window_and_create_wk(row)
        #     return
        row = settings.wnd_main.locate_hwnd(hwnd, row)
        if row == -1:  # 说明该窗口已经添加到控制台了
            return
        plan_idx = settings.cfg_common["获取窗口后设置方案"]
        # 这里直接同步调用了, 因为要等到wk创建才能绑定窗口做操作
        settings.wnd_main.thd_create_wk_for_hwnd(hwnd, row, plan_idx)
        # wk创建完毕了, 用它来绑定窗口
        wk: Worker = settings.worker_list[row]
        if wk is None:
            return
        if not wk.bind_window():
            return
        return wk


class ThreadSendRequstBase(QThread):
    def __init__(self, url, body, headers={}, req_ts="0", timeout=5):
        super().__init__()
        # 请求
        self.url = url
        self.body = body
        self.headers = headers
        self.timeout = timeout
        self.req_ts = req_ts
        # 响应
        self.status_code = 500
        self.resp = {}

    def run(self):
        try:
            path = self.url.split("/")[-1]
            log.info(f"SendRequestStart: {path}")
            resp = requests.post(
                url=self.url,
                json=self.body,
                headers=self.headers,
                timeout=self.timeout,
                proxies={  # 忽略系统代理
                    'http': None,
                    'https': None
                }
            )
            self.status_code = resp.status_code
            self.resp = resp.json() or {}
            log.info(f"SendRequestSuccess: {path}")
            return True
        except Exception as e:
            log.warn(f"SendRequestFailed: {e}")
            return False


class ThreadSendRequestHeartBeat(ThreadSendRequstBase):
    # req_ts, status_code, resp, need_verify
    sig_resp_heart_done = Signal(str, int, dict, bool)

    rights_convert = {
        1: "One",
        5: "Five",
        10: "Ten",
        20: "Twenty",
        30: "Thirty",
        60: "Sixty"
    }

    def run(self):
        need_verify = True
        if not super().run():  # 有可能服务器被打崩了
            rights_info = settings.cfg_common["MD5"]
            if rights_info and is_network_connect():  # 客户端联网正常
                # 说明确实是服务端有问题, 然后我们挨个权益的计算哈希
                card_number = settings.wnd_main.edt_card_key.text()
                # print(f"rights_info:{rights_info}, card_number:{card_number}, machine_code:{settings.machine_code}")
                for card_rights in [1, 5, 10, 20, 30, 60]:
                    aaa = get_check_sum(
                        str(card_rights), card_number + settings.machine_code)
                    # print(f"aaa:{aaa}")
                    if aaa == rights_info:
                        self.status_code = 200
                        self.resp = {
                            "card_rights": self.rights_convert.get(card_rights),
                        }
                        need_verify = False
                        break
        # print(f"status_code:{self.status_code}, resp:{self.resp}, need_verify:{need_verify}")
        self.sig_resp_heart_done.emit(
            self.req_ts, self.status_code, self.resp, need_verify)


class ThreadSendRequestUnBind(ThreadSendRequstBase):
    # status_code, resp
    sig_resp_unbind_done = Signal(int, dict)

    def run(self):
        super().run()
        self.sig_resp_unbind_done.emit(self.status_code, self.resp)


class ThreadCheckUpdate(ThreadSendRequstBase):
    sig_get_download_info_finish = Signal(dict)  # 定义信号在线程类中

    def run(self):
        super().run()
        self.sig_get_download_info_finish.emit(self.resp)  # 收到更新数据后 通知 更新窗口


class ThreadSendRequestHeartBeatV2(QThread):
    # req_ts, status_code, resp, need_verify
    sig_resp_heart_done = Signal(str, int, object, bool)

    rights_convert = {
        1: "One",
        5: "Five",
        10: "Ten",
        20: "Twenty",
        30: "Thirty",
        60: "Sixty"
    }

    def __init__(self, card_number, machine_code, user_info, req_ts, client_version, x_check_sum):
        super().__init__()
        self.card_number = card_number
        self.machine_code = machine_code
        self.user_info = user_info
        self.req_ts = req_ts
        self.client_version = client_version
        self.x_check_sum = x_check_sum
        self.resp = None

    def run(self):
        try:
            with grpc.insecure_channel(settings.grpc_server_host_name) as channel:
                get_card_info_req = card_pb2.GetCardInfoRequestV2(
                    card_number=self.card_number,
                    machine_code=self.machine_code,
                    card_extra_rights=settings.extra_rights,
                    user_info=self.user_info,
                    req_ts=self.req_ts,
                    client_version=self.client_version,
                    x_check_sum=self.x_check_sum,
                )
                # print(f'get_card_info_req:{get_card_info_req}')
                stub = card_pb2_grpc.CardStub(channel)
                self.resp = stub.GetCardInfoV2(get_card_info_req)
                print(f'resp:{self.resp}')
                self.sig_resp_heart_done.emit(
                    self.req_ts, 200, self.resp, True)
        except grpc.RpcError as e:  # 连不上服务器, 或者服务器报错都走这里
            status_code = e.code()
            details = e.details()
            print(f"status_code:{status_code}, details:{details}")
            if status_code in [grpc.StatusCode.UNAVAILABLE, grpc.StatusCode.INTERNAL]:  # 连不上服务器
                # print('Server Unavailable')
                due_ts_encrypted = settings.cfg_common["HMAC"]
                rights_info = settings.cfg_common["MD5"]
                if rights_info and due_ts_encrypted and is_network_connect():  # 客户端联网正常
                    # 说明确实是服务端有问题
                    due_ts = self.get_due_ts_safe(due_ts_encrypted)
                    if due_ts > 0 and settings.cur_time_stamp < due_ts:
                        # 有过期信息, 然后我们挨个权益的计算哈希
                        card_number = settings.wnd_main.edt_card_key.text()
                        # print(f"rights_info:{rights_info}, card_number:{card_number}, machine_code:{settings.machine_code}")
                        for card_rights in [1, 5, 10, 20, 30, 60]:
                            aaa = get_check_sum(
                                str(card_rights), card_number + settings.machine_code)
                            if aaa == rights_info:
                                self.resp = card_pb2.GetCardInfoReply(
                                    card_rights=self.rights_convert.get(
                                        card_rights),
                                )
                                self.sig_resp_heart_done.emit(
                                    self.req_ts, 200, self.resp, False)
                                return
                self.sig_resp_heart_done.emit(
                    self.req_ts, 500, self.resp, False)
            else:  # 服务器报错
                self.sig_resp_heart_done.emit(self.req_ts, 400, details, False)
        except Exception as e:
            print(f"发送心跳失败: {e}")

    def get_due_ts_safe(self, due_ts_encrypted):
        try:
            due_ts_str = decrypt(due_ts_encrypted)
            due_ts = int(due_ts_str)
            return due_ts
        except Exception as e:
            print(f"解密失败: {e}")
            return 0


class ThreadSendRequestUnBindV2(QThread):
    # status_code, resp
    sig_resp_unbind_done = Signal(int, object)

    def __init__(self, card_number, machine_code):
        super().__init__()
        self.card_number = card_number
        self.machine_code = machine_code
        self.resp = None

    def run(self):
        try:
            with grpc.insecure_channel(settings.grpc_server_host_name) as channel:
                unbind_req = card_pb2.UnBindCardRequest(
                    card_number=self.card_number,
                    machine_code=self.machine_code,
                )
                stub = card_pb2_grpc.CardStub(channel)
                self.resp = stub.UnBindCard(unbind_req)
                self.sig_resp_unbind_done.emit(200, self.resp)
        except grpc.RpcError as e:  # 连不上服务器, 或者服务器报错都走这里
            status_code = e.code()
            details = e.details()
            if status_code in [grpc.StatusCode.UNAVAILABLE, grpc.StatusCode.INTERNAL]:
                self.sig_resp_unbind_done.emit(500, self.resp)
            else:
                self.sig_resp_unbind_done.emit(400, details)
        except Exception as e:
            print(f"发送解绑失败: {e}")
            settings.wnd_main.sig_info.emit("发送解绑失败")


class ThreadCheckUpdateV2(QThread):
    sig_get_download_info_finish = Signal(object)  # 定义信号在线程类中

    def __init__(self, card_number, machine_code, client_version):
        super().__init__()
        self.card_number = card_number
        self.machine_code = machine_code
        self.client_version = client_version
        self.resp = None

    def run(self):
        try:
            with grpc.insecure_channel(settings.grpc_server_host_name) as channel:
                get_update_info_req = card_pb2.GetUpdateInfoRequest(
                    card_number=self.card_number,
                    machine_code=self.machine_code,
                    client_version=self.client_version,
                )
                stub = card_pb2_grpc.CardStub(channel)
                self.resp = stub.GetUpdateInfo(get_update_info_req)
                resp_dict = {
                    'force_update': self.resp.force_update,
                    'latest_version': self.resp.latest_version,
                    'patcher_download_url': self.resp.patcher_download_url,
                    'installer_download_url': self.resp.installer_download_url,
                    'update_info': self.resp.update_info,
                    'md5': self.resp.md5,
                }
                self.sig_get_download_info_finish.emit(resp_dict)
        except Exception as e:
            print(f"检查更新失败: {e}")
            settings.wnd_main.sig_info.emit("检查更新失败")
            self.sig_get_download_info_finish.emit({})


class ThreadGetCustomInfo(QThread):
    sig_get_custom_info_finish = Signal(dict)  # 定义信号在线程类中

    def __init__(self, card_number, machine_code, client_version):
        super().__init__()
        self.card_number = card_number
        self.machine_code = machine_code
        self.client_version = client_version
        self.resp = None

    def run(self):
        try:
            with grpc.insecure_channel(settings.grpc_server_host_name) as channel:
                get_custom_info_req = card_pb2.GetCustomInfoRequest(
                    card_number=self.card_number,
                    machine_code=self.machine_code,
                    client_version=self.client_version,
                )
                stub = card_pb2_grpc.CardStub(channel)
                self.resp = stub.GetCustomInfo(get_custom_info_req)
                custom_info = self.resp.custom_info
                resp_dict = json.loads(custom_info)
                self.sig_get_custom_info_finish.emit(resp_dict)
        except Exception as e:
            print(f"获取自定义信息失败: {e}")
            self.sig_get_custom_info_finish.emit({})
