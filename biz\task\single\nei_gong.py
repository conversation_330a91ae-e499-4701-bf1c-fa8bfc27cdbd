from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskNeiGong(TaskBase):
    TASK_NAME = "内功闭关"
    DONE_TIP = "内气充溢"

    @classmethod
    def run(cls, wk: Worker):
        cls.leave_team(wk)
        if not cls.is_in_bi_guan(wk):
            cls.back_to_school(wk)
            if not cls.back_to_school(wk):
                wk.record("回门派失败，自动结束")
                return
        try:
            cls.open_bi_guan_page(wk)
            for i in range(10000):
                msleep(2000)
                if settings.cur_time_fmt > "23:50":
                    wk.record("闭关结束时间到, 已被传出")
                    break
                if i == 0 or rnd(0, 10) == 1:
                    wk.record("内功修炼中...")
                if cls.is_biguan_full(wk):
                    wk.record("闭关完成，结束闭关")
                    wk.find_pic_click(*RECT_FULL, "结束闭关.bmp")
                    if cls.should_clear_limit(wk):
                        msleep(600)
                        cls.open_bi_guan_page(wk)
                        continue
                    break
                if not cls.is_in_bi_guan(wk):
                    cls.open_bi_guan_page(wk)
                if wk.find_str(*RECT_INFO, "内功已经修满", COLOR_GOLD):
                    wk.record("内功已经修满，结束闭关")
                    wk.find_pic_click(*RECT_FULL, "结束闭关.bmp")
                    msleep(500)
                    break
        except Exception as e:
            print("Exception:", e)
            pass
        finally:
            wk.find_pic_click(*RECT_FULL, "结束闭关.bmp")  # 有时候点一次会没点到

    @classmethod
    def is_in_bi_guan(cls, wk: Worker):
        res = wk.find_pic(*RECT_BI_GUAN, "结束闭关.bmp") or wk.find_str(
            *RECT_BI_GUAN, "正在闭关中", COLOR_BLACK
        )
        return res

    @classmethod
    def check_fight_condition(cls, wk: Worker):
        if wk.is_fight:
            if not cls.is_in_bi_guan(wk):
                return True
        return False

    @classmethod
    def is_biguan_full(cls, wk: Worker):
        if cls.is_system_broad_show(wk, cls.DONE_TIP, COLOR_GOLD):
            return True
        cur_total = wk.ocr(*RECT_BI_GUAN_PROC, COLOR_BLACK, zk=ZK_DIGIT_11)
        len_cur_total = len(cur_total)
        if len_cur_total % 2 != 0 or len_cur_total < 4:
            return False
        if len_cur_total == 4 and cur_total < "6000":
            return False
        mid = len_cur_total // 2
        cur = cur_total[:mid]
        total = cur_total[mid:]
        return cur == total

    @classmethod
    def open_bi_guan_page(cls, wk: Worker, item="修炼内功"):
        if cls.is_in_bi_guan(wk):
            return True
        cls.off_ride(wk)
        for i in range(1000):
            if wk.find_str_click(*RECT_FULL, "闭关", COLOR_TALK_ITEM):
                wk.record(f"准备 {item}...")
                msleep(300)
                if wk.find_str_click(*RECT_TALK, item, COLOR_TALK_ITEM, timeout=300):
                    msleep(300)
                    wk.find_str_click(*RECT_TALK, "确定",
                                      COLOR_TALK_ITEM, timeout=300)
                    msleep(1000)
                    if wk.find_str(*RECT_TALK, "全部修炼完成", COLOR_BLACK):
                        raise Exception("任务已完成")
                    if wk.find_str(*RECT_TALK, "今天的贯通已完成|走火入魔", COLOR_BLACK):
                        if cls.should_clear_limit(wk):
                            cls.clear_limit(wk)
                            continue
                        raise Exception("任务已完成")
                    if wk.find_str(*RECT_POPUP, "请先下马", COLOR_BLACK):
                        wk.record("正在下马...")
                        cls.click_confirm(wk)
                        msleep(500)
                        cls.close_other_talk(wk)
                        cls.off_ride(wk)
                        continue
                    if cls.is_talk_show_info(wk, "选择要修炼的内功", COLOR_BLACK):
                        wk.record("正在选择要修炼的内功...")
                        if cls.select_nei_gong(wk):
                            continue
                    break
            if i == 0 or wk.is_stuck:
                wk.record("寻路到闭关处...")
                cls.open_system_npc_click(wk, "闭关", enable_white=True)
            msleep(500)

        if cls.is_in_bi_guan(wk):
            wk.record("进入闭关成功")
            return True
        wk.record("进入闭关失败")
        return False

    @classmethod
    def select_nei_gong(cls, wk: Worker):
        cls.close_pages(wk)
        wk.key_press_combo(VK_ALT, VK_Z)
        msleep(1000)
        x, y = wk.get_pic_pos(*RECT_FULL, "内功.bmp")
        if x < 0:
            wk.record("未找到内功选择")
            return False
        wk.record("选择内功成功")
        wk.move_click(x+94, y+7)
        msleep(400)
        if cls.click_confirm(wk):
            wk.record("选择内功失败")
            msleep(400)
            cls.close_pages(wk)
            return False
        wk.move_click(x-70, y+25)
        cls.close_pages(wk)
        return True

    @classmethod
    def should_clear_limit(cls, wk: Worker):
        return cls.TASK_NAME == "内功闭关" and wk.cfg_plan_task["清除限制"]

    @classmethod
    def clear_limit(cls, wk: Worker):
        wk.record("正在清除限制...")
        for i in range(100):
            if cls.is_talk_open(wk):
                if cls.talk_click_items(wk, ["闭关", "清除每日", "我要清除"], close_talk=False):
                    msleep(200)
                    if cls.is_popup_show_info(wk, "今日已清除次数", timeout=500):
                        count = int(wk.ocr(404, 261, 434, 283,
                                    COLOR_BLACK, zk=ZK_DIGIT_11) or 0)
                        wk.record(f"今日已清除次数:{count}")
                        if count < 4:
                            wk.record("清除限制成功")
                            cls.click_confirm(wk)
                        else:
                            wk.record("已清除4次, 取消清除")
                            cls.click_cancel(wk)
                            break
                    msleep(600)
                    if cls.is_popup_show_info(wk, "你的金券不足", timeout=400):
                        wk.record("金券不足, 取消清除")
                        cls.click_confirm(wk)
                        raise Exception("清除限制已完成")
                    else:
                        wk.record("清除限制已完成")
                    return
                cls.close_other_talk(wk)
            if i == 0 or wk.is_stuck:
                wk.record("寻路到闭关处...")
                cls.open_system_npc_click(wk, "闭关", enable_white=True)
            msleep(600)
        raise Exception("清除限制已完成")

    @classmethod
    def get_default_biz_config(cls):
        return {
            "清除限制": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_bi_guan_qing_chu.setChecked(cls.CONFIG["清除限制"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["清除限制"] = settings.wnd_main.chk_bi_guan_qing_chu.isChecked()
        super().cfg_save(plan_name)
