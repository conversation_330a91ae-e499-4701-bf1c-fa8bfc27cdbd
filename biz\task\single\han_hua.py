from biz.constants.constants import *
from biz.task.__base import TaskBase
from utils import *
from biz.obj.worker import Worker
import settings


class TaskHanHua(TaskBase):
    TASK_NAME = "自动喊话"
    IS_FREE = True

    @classmethod
    def run(cls, wk: Worker):
        last_ts = 0
        while True:
            if settings.cur_time_stamp - last_ts >= wk.cfg_plan_task["喊话频率"]*60:
                cls.close_pages(wk)
                wk.record("正在喊话...")
                wk.send_string(wk.cfg_plan_task["喊话内容"])
                msleep(500)
                wk.key_press(VK_ENTER)
                wk.record("喊话结束, 等待中...")
                last_ts = settings.cur_time_stamp
            msleep(1000)

    @classmethod
    def get_default_biz_config(cls):
        return {
            "喊话频率": 5,
            "喊话内容": ""
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_shout_frequency.setValue(cls.CONFIG["喊话频率"])
        settings.wnd_main.edt_shout.setPlainText(cls.CONFIG["喊话内容"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["喊话频率"] = settings.wnd_main.spin_count_shout_frequency.value()
        cls.CONFIG["喊话内容"] = settings.wnd_main.edt_shout.toPlainText()
        super().cfg_save(plan_name)
