# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/netauth/v1/card.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.netauth.v1 import enums_pb2 as api_dot_netauth_dot_v1_dot_enums__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='api/netauth/v1/card.proto',
  package='api.netauth.v1',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19\x61pi/netauth/v1/card.proto\x12\x0e\x61pi.netauth.v1\x1a\x1a\x61pi/netauth/v1/enums.proto\"\xc7\x01\n\x14GetCardInfoRequestV2\x12\x13\n\x0b\x63\x61rd_number\x18\x01 \x01(\t\x12\x14\n\x0cmachine_code\x18\x02 \x01(\t\x12\x1e\n\x11\x63\x61rd_extra_rights\x18\x03 \x01(\rH\x00\x88\x01\x01\x12\x11\n\tuser_info\x18\x04 \x01(\t\x12\x0e\n\x06req_ts\x18\x05 \x01(\t\x12\x16\n\x0e\x63lient_version\x18\x06 \x01(\t\x12\x13\n\x0bx_check_sum\x18\x07 \x01(\tB\x14\n\x12_card_extra_rights\"\xb0\x01\n\x12GetCardInfoReplyV2\x12+\n\tcard_type\x18\x01 \x01(\x0e\x32\x18.api.netauth.v1.CardType\x12/\n\x0b\x63\x61rd_rights\x18\x02 \x01(\x0e\x32\x1a.api.netauth.v1.CardRights\x12\x19\n\x11\x63\x61rd_extra_rights\x18\x03 \x01(\r\x12\x0e\n\x06\x64ue_ts\x18\x04 \x01(\t\x12\x11\n\tcheck_sum\x18\x05 \x01(\t\">\n\x11UnBindCardRequest\x12\x13\n\x0b\x63\x61rd_number\x18\x01 \x01(\t\x12\x14\n\x0cmachine_code\x18\x02 \x01(\t\"\x11\n\x0fUnBindCardReply\"Y\n\x14GetUpdateInfoRequest\x12\x13\n\x0b\x63\x61rd_number\x18\x01 \x01(\t\x12\x14\n\x0cmachine_code\x18\x02 \x01(\t\x12\x16\n\x0e\x63lient_version\x18\x03 \x01(\t\"\xa2\x01\n\x12GetUpdateInfoReply\x12\x14\n\x0c\x66orce_update\x18\x01 \x01(\x08\x12\x16\n\x0elatest_version\x18\x02 \x01(\t\x12\x1c\n\x14patcher_download_url\x18\x03 \x01(\t\x12\x1e\n\x16installer_download_url\x18\x04 \x01(\t\x12\x13\n\x0bupdate_info\x18\x05 \x01(\t\x12\x0b\n\x03md5\x18\x06 \x01(\t\"Y\n\x14GetCustomInfoRequest\x12\x13\n\x0b\x63\x61rd_number\x18\x01 \x01(\t\x12\x14\n\x0cmachine_code\x18\x02 \x01(\t\x12\x16\n\x0e\x63lient_version\x18\x03 \x01(\t\")\n\x12GetCustomInfoReply\x12\x13\n\x0b\x63ustom_info\x18\x01 \x01(\t\"w\n\x1bSetLatestVersionInfoRequest\x12\x16\n\x0elatest_version\x18\x01 \x01(\t\x12\x1c\n\x14latest_installer_url\x18\x02 \x01(\t\x12\x13\n\x0bupdate_info\x18\x03 \x01(\t\x12\r\n\x05token\x18\x04 \x01(\t\"\x1b\n\x19SetLatestVersionInfoReply2\xe3\x03\n\x04\x43\x61rd\x12[\n\rGetCardInfoV2\x12$.api.netauth.v1.GetCardInfoRequestV2\x1a\".api.netauth.v1.GetCardInfoReplyV2\"\x00\x12R\n\nUnBindCard\x12!.api.netauth.v1.UnBindCardRequest\x1a\x1f.api.netauth.v1.UnBindCardReply\"\x00\x12[\n\rGetUpdateInfo\x12$.api.netauth.v1.GetUpdateInfoRequest\x1a\".api.netauth.v1.GetUpdateInfoReply\"\x00\x12p\n\x14SetLatestVersionInfo\x12+.api.netauth.v1.SetLatestVersionInfoRequest\x1a).api.netauth.v1.SetLatestVersionInfoReply\"\x00\x12[\n\rGetCustomInfo\x12$.api.netauth.v1.GetCustomInfoRequest\x1a\".api.netauth.v1.GetCustomInfoReply\"\x00\x62\x06proto3'
  ,
  dependencies=[api_dot_netauth_dot_v1_dot_enums__pb2.DESCRIPTOR,])




_GETCARDINFOREQUESTV2 = _descriptor.Descriptor(
  name='GetCardInfoRequestV2',
  full_name='api.netauth.v1.GetCardInfoRequestV2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='card_number', full_name='api.netauth.v1.GetCardInfoRequestV2.card_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='machine_code', full_name='api.netauth.v1.GetCardInfoRequestV2.machine_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='card_extra_rights', full_name='api.netauth.v1.GetCardInfoRequestV2.card_extra_rights', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_info', full_name='api.netauth.v1.GetCardInfoRequestV2.user_info', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='req_ts', full_name='api.netauth.v1.GetCardInfoRequestV2.req_ts', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='client_version', full_name='api.netauth.v1.GetCardInfoRequestV2.client_version', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_check_sum', full_name='api.netauth.v1.GetCardInfoRequestV2.x_check_sum', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_card_extra_rights', full_name='api.netauth.v1.GetCardInfoRequestV2._card_extra_rights',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=74,
  serialized_end=273,
)


_GETCARDINFOREPLYV2 = _descriptor.Descriptor(
  name='GetCardInfoReplyV2',
  full_name='api.netauth.v1.GetCardInfoReplyV2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='card_type', full_name='api.netauth.v1.GetCardInfoReplyV2.card_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='card_rights', full_name='api.netauth.v1.GetCardInfoReplyV2.card_rights', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='card_extra_rights', full_name='api.netauth.v1.GetCardInfoReplyV2.card_extra_rights', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='due_ts', full_name='api.netauth.v1.GetCardInfoReplyV2.due_ts', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='check_sum', full_name='api.netauth.v1.GetCardInfoReplyV2.check_sum', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=276,
  serialized_end=452,
)


_UNBINDCARDREQUEST = _descriptor.Descriptor(
  name='UnBindCardRequest',
  full_name='api.netauth.v1.UnBindCardRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='card_number', full_name='api.netauth.v1.UnBindCardRequest.card_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='machine_code', full_name='api.netauth.v1.UnBindCardRequest.machine_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=454,
  serialized_end=516,
)


_UNBINDCARDREPLY = _descriptor.Descriptor(
  name='UnBindCardReply',
  full_name='api.netauth.v1.UnBindCardReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=518,
  serialized_end=535,
)


_GETUPDATEINFOREQUEST = _descriptor.Descriptor(
  name='GetUpdateInfoRequest',
  full_name='api.netauth.v1.GetUpdateInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='card_number', full_name='api.netauth.v1.GetUpdateInfoRequest.card_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='machine_code', full_name='api.netauth.v1.GetUpdateInfoRequest.machine_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='client_version', full_name='api.netauth.v1.GetUpdateInfoRequest.client_version', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=537,
  serialized_end=626,
)


_GETUPDATEINFOREPLY = _descriptor.Descriptor(
  name='GetUpdateInfoReply',
  full_name='api.netauth.v1.GetUpdateInfoReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='force_update', full_name='api.netauth.v1.GetUpdateInfoReply.force_update', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latest_version', full_name='api.netauth.v1.GetUpdateInfoReply.latest_version', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='patcher_download_url', full_name='api.netauth.v1.GetUpdateInfoReply.patcher_download_url', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='installer_download_url', full_name='api.netauth.v1.GetUpdateInfoReply.installer_download_url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='update_info', full_name='api.netauth.v1.GetUpdateInfoReply.update_info', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='md5', full_name='api.netauth.v1.GetUpdateInfoReply.md5', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=629,
  serialized_end=791,
)


_GETCUSTOMINFOREQUEST = _descriptor.Descriptor(
  name='GetCustomInfoRequest',
  full_name='api.netauth.v1.GetCustomInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='card_number', full_name='api.netauth.v1.GetCustomInfoRequest.card_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='machine_code', full_name='api.netauth.v1.GetCustomInfoRequest.machine_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='client_version', full_name='api.netauth.v1.GetCustomInfoRequest.client_version', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=793,
  serialized_end=882,
)


_GETCUSTOMINFOREPLY = _descriptor.Descriptor(
  name='GetCustomInfoReply',
  full_name='api.netauth.v1.GetCustomInfoReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='custom_info', full_name='api.netauth.v1.GetCustomInfoReply.custom_info', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=884,
  serialized_end=925,
)


_SETLATESTVERSIONINFOREQUEST = _descriptor.Descriptor(
  name='SetLatestVersionInfoRequest',
  full_name='api.netauth.v1.SetLatestVersionInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='latest_version', full_name='api.netauth.v1.SetLatestVersionInfoRequest.latest_version', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latest_installer_url', full_name='api.netauth.v1.SetLatestVersionInfoRequest.latest_installer_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='update_info', full_name='api.netauth.v1.SetLatestVersionInfoRequest.update_info', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='token', full_name='api.netauth.v1.SetLatestVersionInfoRequest.token', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=927,
  serialized_end=1046,
)


_SETLATESTVERSIONINFOREPLY = _descriptor.Descriptor(
  name='SetLatestVersionInfoReply',
  full_name='api.netauth.v1.SetLatestVersionInfoReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1048,
  serialized_end=1075,
)

_GETCARDINFOREQUESTV2.oneofs_by_name['_card_extra_rights'].fields.append(
  _GETCARDINFOREQUESTV2.fields_by_name['card_extra_rights'])
_GETCARDINFOREQUESTV2.fields_by_name['card_extra_rights'].containing_oneof = _GETCARDINFOREQUESTV2.oneofs_by_name['_card_extra_rights']
_GETCARDINFOREPLYV2.fields_by_name['card_type'].enum_type = api_dot_netauth_dot_v1_dot_enums__pb2._CARDTYPE
_GETCARDINFOREPLYV2.fields_by_name['card_rights'].enum_type = api_dot_netauth_dot_v1_dot_enums__pb2._CARDRIGHTS
DESCRIPTOR.message_types_by_name['GetCardInfoRequestV2'] = _GETCARDINFOREQUESTV2
DESCRIPTOR.message_types_by_name['GetCardInfoReplyV2'] = _GETCARDINFOREPLYV2
DESCRIPTOR.message_types_by_name['UnBindCardRequest'] = _UNBINDCARDREQUEST
DESCRIPTOR.message_types_by_name['UnBindCardReply'] = _UNBINDCARDREPLY
DESCRIPTOR.message_types_by_name['GetUpdateInfoRequest'] = _GETUPDATEINFOREQUEST
DESCRIPTOR.message_types_by_name['GetUpdateInfoReply'] = _GETUPDATEINFOREPLY
DESCRIPTOR.message_types_by_name['GetCustomInfoRequest'] = _GETCUSTOMINFOREQUEST
DESCRIPTOR.message_types_by_name['GetCustomInfoReply'] = _GETCUSTOMINFOREPLY
DESCRIPTOR.message_types_by_name['SetLatestVersionInfoRequest'] = _SETLATESTVERSIONINFOREQUEST
DESCRIPTOR.message_types_by_name['SetLatestVersionInfoReply'] = _SETLATESTVERSIONINFOREPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetCardInfoRequestV2 = _reflection.GeneratedProtocolMessageType('GetCardInfoRequestV2', (_message.Message,), {
  'DESCRIPTOR' : _GETCARDINFOREQUESTV2,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.GetCardInfoRequestV2)
  })
_sym_db.RegisterMessage(GetCardInfoRequestV2)

GetCardInfoReplyV2 = _reflection.GeneratedProtocolMessageType('GetCardInfoReplyV2', (_message.Message,), {
  'DESCRIPTOR' : _GETCARDINFOREPLYV2,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.GetCardInfoReplyV2)
  })
_sym_db.RegisterMessage(GetCardInfoReplyV2)

UnBindCardRequest = _reflection.GeneratedProtocolMessageType('UnBindCardRequest', (_message.Message,), {
  'DESCRIPTOR' : _UNBINDCARDREQUEST,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.UnBindCardRequest)
  })
_sym_db.RegisterMessage(UnBindCardRequest)

UnBindCardReply = _reflection.GeneratedProtocolMessageType('UnBindCardReply', (_message.Message,), {
  'DESCRIPTOR' : _UNBINDCARDREPLY,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.UnBindCardReply)
  })
_sym_db.RegisterMessage(UnBindCardReply)

GetUpdateInfoRequest = _reflection.GeneratedProtocolMessageType('GetUpdateInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETUPDATEINFOREQUEST,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.GetUpdateInfoRequest)
  })
_sym_db.RegisterMessage(GetUpdateInfoRequest)

GetUpdateInfoReply = _reflection.GeneratedProtocolMessageType('GetUpdateInfoReply', (_message.Message,), {
  'DESCRIPTOR' : _GETUPDATEINFOREPLY,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.GetUpdateInfoReply)
  })
_sym_db.RegisterMessage(GetUpdateInfoReply)

GetCustomInfoRequest = _reflection.GeneratedProtocolMessageType('GetCustomInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCUSTOMINFOREQUEST,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.GetCustomInfoRequest)
  })
_sym_db.RegisterMessage(GetCustomInfoRequest)

GetCustomInfoReply = _reflection.GeneratedProtocolMessageType('GetCustomInfoReply', (_message.Message,), {
  'DESCRIPTOR' : _GETCUSTOMINFOREPLY,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.GetCustomInfoReply)
  })
_sym_db.RegisterMessage(GetCustomInfoReply)

SetLatestVersionInfoRequest = _reflection.GeneratedProtocolMessageType('SetLatestVersionInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETLATESTVERSIONINFOREQUEST,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.SetLatestVersionInfoRequest)
  })
_sym_db.RegisterMessage(SetLatestVersionInfoRequest)

SetLatestVersionInfoReply = _reflection.GeneratedProtocolMessageType('SetLatestVersionInfoReply', (_message.Message,), {
  'DESCRIPTOR' : _SETLATESTVERSIONINFOREPLY,
  '__module__' : 'api.netauth.v1.card_pb2'
  # @@protoc_insertion_point(class_scope:api.netauth.v1.SetLatestVersionInfoReply)
  })
_sym_db.RegisterMessage(SetLatestVersionInfoReply)



_CARD = _descriptor.ServiceDescriptor(
  name='Card',
  full_name='api.netauth.v1.Card',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1078,
  serialized_end=1561,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetCardInfoV2',
    full_name='api.netauth.v1.Card.GetCardInfoV2',
    index=0,
    containing_service=None,
    input_type=_GETCARDINFOREQUESTV2,
    output_type=_GETCARDINFOREPLYV2,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UnBindCard',
    full_name='api.netauth.v1.Card.UnBindCard',
    index=1,
    containing_service=None,
    input_type=_UNBINDCARDREQUEST,
    output_type=_UNBINDCARDREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetUpdateInfo',
    full_name='api.netauth.v1.Card.GetUpdateInfo',
    index=2,
    containing_service=None,
    input_type=_GETUPDATEINFOREQUEST,
    output_type=_GETUPDATEINFOREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetLatestVersionInfo',
    full_name='api.netauth.v1.Card.SetLatestVersionInfo',
    index=3,
    containing_service=None,
    input_type=_SETLATESTVERSIONINFOREQUEST,
    output_type=_SETLATESTVERSIONINFOREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCustomInfo',
    full_name='api.netauth.v1.Card.GetCustomInfo',
    index=4,
    containing_service=None,
    input_type=_GETCUSTOMINFOREQUEST,
    output_type=_GETCUSTOMINFOREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CARD)

DESCRIPTOR.services_by_name['Card'] = _CARD

# @@protoc_insertion_point(module_scope)
