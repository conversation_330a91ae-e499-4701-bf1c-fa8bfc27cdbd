from biz.constants.constants import *
from biz.task.team.ming_bu import TaskBoss
from utils import *
from biz.obj.worker import Worker


class TaskChuangGuan(TaskBoss):
    TASK_NAME = "闯关"
    NEED_AVOID_FIGHT = False
    IS_CALL_BB_ENABLE = False

    @classmethod
    def run(cls, wk: Worker):
        if cls.cur_map(wk) in OUTSIDE_MAP_LIST:
            wk.record("不在闯关地图内, 正在前往闯关地图...")
            cls.back_to_kai_feng(wk)
            cls.cross_map_by_talk(wk, "关卡人", npc_name="闯关传送使", talk_items=["我要闯关"])
        wk.record(f"请选好箱子关后, 按下鼠标右键后启动...")
        wk.wait_key(VK_MOUSE_RIGHT)
        wk.record("已启动, 正在找关卡...")
            
        color = COLOR_GREEN + "|" + COLOR_CYAN
        count = 0
        while True:
            if wk.is_fight:
                cls.fight_operation(wk)
                count += 1
                wk.record(f"已完成{count}/3")
                if count >= 3:
                    break
            chuang_guan_level = wk.cfg_plan_task["关卡等级"]
            if wk.find_str_offset_click(*RECT_FIND, f"第{chuang_guan_level}关", color, zk=ZK_NAME_11, dx=rnd(16,26), dy=rnd(90, 120), timeout=300, use_fast=False, limit_border=True, limit_y=Y_LIMIT_CLICK):
                wk.record(f"找到并点击了 第{chuang_guan_level}关...")
                msleep(600)
            else:
                if rnd(0, 5) == 1:
                    wk.record(f"未在附近找到{chuang_guan_level}关, 请走到箱子关附近...")
            if cls.is_talk_open(wk):
                mode = wk.cfg_plan_task["模式"]
                if wk.find_str_click(*RECT_TALK, mode, COLOR_TALK_ITEM):
                    wk.record(f"已选择{mode}")
                    msleep(1000)
                else:
                    msleep(1000) # 给用户点击其它对话的时间
                    cls.close_other_talk(wk)
            msleep(800)
        wk.record("闯关完成, 正在返回开封...")
        cls.find_way_npc(wk, "闯关管理员", ["返回开封"])
            
    @classmethod
    def is_meet_call_bb_condition(cls, wk: Worker):  # 闯关不用唤出BB
        return False
    
    @classmethod
    def after_fight_rest_call_bb(cls, wk: Worker):
        pass

    @classmethod
    def get_default_biz_config(cls):
        res = {
            "关卡等级": "100",
            "模式": "普通模式",
        }
        res.update(super().get_default_biz_config())
        return res

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.edt_chuang_guan_level.setText(cls.CONFIG["关卡等级"])
        set_checked_radio_text_in_groupbox(settings.wnd_main.groupBox_chuang_guan, cls.CONFIG["模式"])
        
    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["关卡等级"] = settings.wnd_main.edt_chuang_guan_level.text()
        cls.CONFIG["模式"] = get_checked_radio_text_in_groupbox(settings.wnd_main.groupBox_chuang_guan)
        super().cfg_save(plan_name)
