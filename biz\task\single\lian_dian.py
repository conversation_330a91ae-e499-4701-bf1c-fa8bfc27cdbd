from utils import *
from biz.task.__base import TaskBase
from biz.constants.constants import *
from biz.obj.worker import Worker


class TaskLianDian(TaskBase):
    TASK_NAME = "鼠标连点"
    IS_FREE = True
    NEED_PLAYER_NAME = False

    @classmethod
    def before_run(cls, wk: Worker):
        # 重置任务类型和完成次数
        wk.cur_task, wk.sub_task = "", ""
        wk.done_count = 0
        wk.cur_task = cls.TASK_NAME
        wk.record(f"开始执行")

    @classmethod
    def run(cls, wk: Worker):
        while True:
            # wk.record(f"请按鼠标右键启动...")
            # wk.wait_key(VK_MOUSE_RIGHT)
            # if settings.com_obj.get_mouse_point_window() != wk.hwnd:
            #     continue
            x, y = settings.com_obj.get_cursor_pos()
            client_x, client_y = wk.screen_to_client(wk.hwnd, x, y)
            wk.record(f"开始连点...(按中键停止)，坐标：{x},{y}")
            for _ in range(1000000000):
                wk.move_to(client_x, client_y, is_delay=False)
                wk.left_click(re_move=False, is_delay=False)
                msleep(10)
                
    @classmethod
    def after_run(cls, wk: Worker):
        wk.record("执行完成")


class TaskZhenFa(TaskBase):
    TASK_NAME = "阵法升级"
    IS_FREE = True
    NEED_PLAYER_NAME = False

    @classmethod
    def before_run(cls, wk: Worker):
        # 重置任务类型和完成次数
        wk.cur_task, wk.sub_task = "", ""
        wk.done_count = 0
        wk.cur_task = cls.TASK_NAME
        wk.record(f"开始执行")

    @classmethod
    def run(cls, wk: Worker):
        while True:
            wk.record(f"开始升级...(按中键停止)")
            for _ in range(100000000):
                wk.move_click(565, 319, re_move=False)  # 升级
                wk.move_click(339, 331, re_move=False)  # 确认
                msleep(300)
                
    @classmethod
    def after_run(cls, wk: Worker):
        msleep(500)
        wk.record("执行完成")


class TaskSuiCongShengJi(TaskBase):
    TASK_NAME = "随从升级"
    IS_FREE = True
    NEED_PLAYER_NAME = False

    @classmethod
    def before_run(cls, wk: Worker):
        # 重置任务类型和完成次数
        wk.cur_task, wk.sub_task = "", ""
        wk.done_count = 0
        wk.cur_task = cls.TASK_NAME
        wk.record(f"开始执行")

    @classmethod
    def run(cls, wk: Worker):
        while True:
            wk.record(f"开始升级...(按中键停止)")
            for _ in range(100000000):
                wk.move_click(539, 353, re_move=False)  # 升级
                wk.move_click(399, 344, re_move=False)  # 确认
                msleep(300)