name: software_release

on:
#   push: # 代码推送到main分支自动触发工作流
#     branches:
#       - main
  workflow_dispatch: # 加上这个也支持手动触发工作流

permissions: write-all # 给所有工作写权限

jobs:
  gen_version:
    name: Build version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.create_version.outputs.tag_name }} # 版本号
      body: ${{ steps.create_version.outputs.body }} # 版本变更内容
    steps:
      - uses: release-drafter/release-drafter@v5
        id: create_version
        with:
          config-name: release-drafter.yml # 配置文件在 .github/release-drafter.yml
          disable-autolabeler: true # 禁止自动标签
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 查看变量
        run: |
          echo ${{ format('version={0}', steps.create_version.outputs.tag_name ) }}

  build_launcher_patcher:
    name: Build Launcher Patcher
    needs: gen_version # 等待 gen_version 任务完成才执行
    runs-on: windows-latest
    env:
      version: ${{ needs.gen_version.outputs.version }}
      body: ${{ needs.gen_version.outputs.body }}
    steps:
      - uses: actions/checkout@v3  # 切分支到 执行流水线时指定的分支
        with:
          submodules: recursive

      - name: 读入环境信息
        run: |
          echo ${{ format('version {0}', env.version ) }} # 版本号

      - name: 编译环境设置 Python 3.8
        uses: actions/setup-python@v4
        with:
          python-version: "3.8"
          architecture: "x86"
          cache: 'pip'

      - name: 下载依赖文件
        run: pip install -r requirements.txt

      - name: 读入构建好的版本号, 写入到version变量
        run: |
          python qtAutoUpdateApp/run_write_version.py

      - name: 构建main程序  # 改成 opengl32ws.dll 迷惑破解者
        run: |  
          pyinstaller main.spec --clean -y
          mv ./dist/main/main.exe ./dist/main/opengl32ws.dll

      - name: 打补丁包
        run: |
          mkdir -p ./dist/patcher
          cp ./dist/main/opengl32ws.dll ./dist/patcher/
          cp -r ./dist/main/biz/ ./dist/patcher/biz/
          Compress-Archive -Path ./dist/patcher/* -DestinationPath ./dist/patcher.zip

      - name: 上传patcher压缩包
        uses: actions/upload-artifact@v3
        with:
          name: windows
          path: ./dist/patcher.zip

      - name: 构建launcher程序
        run: |  
          pyinstaller launcher.spec --clean -y

      - name: 将launcher拷贝到main目录
        run: |
          cp ./dist/launcher/launcher.exe ./dist/main/

      - name: InnoSetup打安装包  # ISCC.exe installer.iss
        uses: zhuzichu520/inno-setup-action@v1.0.1
        with: 
          filepath: './installer.iss'

      - name: 给launcher添加版本号
        run: |
          mv ./dist/installer.exe ./dist/installer-${{ env.version }}.exe

      - name: 上传installer安装包
        uses: actions/upload-artifact@v3
        with:
          name: windows
          path: ./dist/installer-${{ env.version }}.exe
      
  release:
    name: Release Version
    needs: [gen_version, build_launcher_patcher]
    runs-on: ubuntu-latest
    env:
      version: ${{ needs.gen_version.outputs.version }}
      body: ${{ needs.gen_version.outputs.body }}
    steps:
      - name: 下载产物
        id: download
        uses: actions/download-artifact@v3
        with:
          path: ./

      - name: 读入环境信息
        run: |
          echo ${{ format('version {0}', env.version ) }}
          echo ${{steps.download.outputs.download-path}}
          ls -R

      - name: 发布文件
        uses: ncipollo/release-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          allowUpdates: true # 覆盖文件
          #draft: true # 草稿 自己可见 版本号会保持一样 默认是自动发布 latest
          #prerelease: true # 预发布 别人可以看到 版本号会继续加
          tag: ${{ env.version }} # 版本号 v0.1.0
          body: ${{ env.body }} # 输出的内容
          artifacts: "windows/patcher.zip, windows/installer-${{ env.version }}.exe"

      - name: 上传文件到服务器
        run: |
            curl -X POST -F "file=@windows/patcher.zip" -F "version=${{ env.version }}" \
            -F "update_info=${{ env.body }}" \
            -H "Authorization: ${{ secrets.UPLOAD_API_TOKEN }}" ${{ secrets.UPLOAD_API_URL }}

