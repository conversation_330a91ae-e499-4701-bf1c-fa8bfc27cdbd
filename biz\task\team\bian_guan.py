from datetime import datetime
from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker


class TaskBianGuan(TaskBase):
    TASK_NAME = "边关清剿"
    IS_TEAM_TASK = True
    
    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=19, minute=30, second=0, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=20, minute=30, second=30, microsecond=0).timestamp()
        )
        
    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday not in [1, 4]:
            wk.record(f"边关清剿只能在星期一/四, 今天是星期{weekday}")
            return False
        return True

    @classmethod
    def run(cls, wk: Worker):
        if not cls.check_the_week(wk):
            return
        if settings.cur_time_stamp < cls.START_TS:
            wk.record("未到开始时间")
            return
        if settings.cur_time_stamp > cls.END_TS:
            wk.record("已到结束时间")
            return
        if wk.cfg_plan["领双边关清剿"]:
            db_time = wk.cfg_plan["领双时间边关清剿"]
            wk.record(f"边关清剿前领双:{db_time}")
            cls.get_double(wk, db_time)
        
        for task_name in ["边关告急", "清剿残寇"]:
            cur_map_name = cls.cur_map(wk, timeout=400)
            if cur_map_name:
                if cur_map_name in OUTSIDE_MAP_LIST:
                    cls.back_to_kai_feng(wk)
                    cls.find_way_npc(wk, "朝廷信使", [task_name, "我要参加", "送我去杀敌"])
                    msleep(1500)
                else:
                    wk.record("已进入边关清剿杀敌地图")
                    break
        if cls.cur_map(wk) == "开封":
            wk.record("进入边关清剿杀敌地图失败")
            return
        cls.use_hls(wk)
        while True:
            if wk.is_fight:  # 战斗中
                cls.fight_operation(wk)
                wk.done_count += 1
                wk.record(f"已完成 {wk.done_count} 次")
            cls.close_other_talk(wk)
            cls.run_left_right_click_floor(wk)
            if settings.cur_time_stamp > cls.END_TS:
                wk.record("已到结束时间")
                return

    @classmethod
    def fight_do_something(cls, wk: Worker):
        for i in range(4):
            wk.move_click(343, 283, 4, is_sleep=False)