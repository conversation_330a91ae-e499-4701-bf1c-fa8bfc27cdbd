from PySide2.QtWidgets import (
    QDialog, QWidget, QListView
)
from PySide2.QtCore import Qt
from typing import List
from ui.wnd_plan_confirm import Ui_WndPlanConfirm


class WndPlanConfirm(QDialog, Ui_WndPlanConfirm):
    def __init__(self,  plan_list: List[str], parent: QWidget):
        super().__init__(parent=parent)
        # 安装界面
        self.setupUi(self)
        # 移除问号按钮（帮助按钮）
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        # 设置 btn_confirm 为默认按钮
        self.btn_confirm.setDefault(True)
        self.btn_confirm.setFocus()  # 确保焦点在按钮上
        self.btn_confirm.setStyleSheet("""
            QPushButton:default {
                border: 1px solid #0078D7;
                background-color: #E5F1FB;
            }
        """)
        self.cmb_plan_list.setView(QListView())
        self.cmb_plan_list.setStyleSheet("QComboBox QAbstractItemView::item{height:18px;}")

        self.last_click_btn = None
        self.cmb_plan_list.clear()
        self.cmb_plan_list.addItems(plan_list)
        self.btn_confirm.clicked.connect(self.on_btn_confirm_clicked)
        self.btn_cancel.clicked.connect(self.on_btn_cancel_clicked)
        
    def on_btn_confirm_clicked(self):
        self.last_click_btn = self.btn_confirm
        self.close()

    def on_btn_cancel_clicked(self):
        self.last_click_btn = self.btn_cancel
        self.close()

    def clickedButton(self):
        return self.last_click_btn
