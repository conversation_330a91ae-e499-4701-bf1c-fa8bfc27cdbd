

from datetime import datetime
from biz.constants.constants import *
from biz.obj.worker import Worker
from biz.task.team.ming_guai import TaskMingGuaiBase
from utils.utils import *


class TaskMengJin(TaskMingGuaiBase):
    TASK_NAME = "梦境寻宝"
    IS_TEAM_TASK = False
    # IS_DIFFICULT_TASK = True
    NEED_AVOID_FIGHT = False
    MONSTER_NAME = "狂徒|梦境|无情|铁手|追命|冷血|诸葛正我|韦一笑|谢逊|殷天正|紫衫龙王|张无忌|黄药师|欧阳锋|段智兴|王重阳|洪七|花满楼|司空摘星|叶孤城|西门吹雪|陆小凤|苏星河|丁春秋|李秋水|天山童姥"
    TALK_CONTENT = "不知道是谁灭了谁|前辈请赐教"

    @classmethod
    def before_run(cls, wk: Worker):
        super().before_run(wk)
        cls.START_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=19, minute=30, second=0, microsecond=0).timestamp()
        )
        cls.NO_REFRESH_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=21, minute=30, second=0, microsecond=0).timestamp()
        )
        cls.END_TS = int(
            datetime.now(settings.china_tz).replace(
                hour=21, minute=30, second=0, microsecond=0).timestamp()
        )

    @classmethod
    def after_run(cls, wk: Worker):
        weekday = get_week_day()
        time_fmt = settings.cur_time_fmt[:5]
        if weekday != 6 or time_fmt < "21:29" or time_fmt > "21:33":
            return
        msleep(200)
        if cls.cur_map(wk, timeout=400) == "开封":
            wk.record("梦境结束, 正在找金鹰领奖...")
            cls.talk_with_cur_map_npc(wk, "金鹰", ["我要领奖", "我要领取"])
            wk.record("领奖完毕")
            
    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday != 6:
            wk.record(f"梦境只能在星期六, 今天是星期{weekday}")
            return False
        return True

    @classmethod
    def only_run_map(cls, wk: Worker):
        return wk.cfg_plan_task["只捡箱子"]

    @classmethod
    def go_to_specific_map(cls, wk: Worker):
        if cls.cur_map(wk) == "梦境桃园":
            wk.record("正在从梦境桃园到下一地图...")
            cls.cross_map_by_way(wk, "梦境桃园")
        elif cls.cur_map(wk).startswith("梦境"):
            pass
        else:
            if not cls.switch_line(wk, "二"):
                msleep(2000)
            cls.back_to_kai_feng(wk)
            wk.record("正在前往开封金鹰处入场...")
            cls.talk_with_cur_map_npc(wk, "金鹰", ["我想探险"], close_talk=False)
            msleep(1500)
            if cls.is_talk_show_info(wk, "只有在活动期间", timeout=200):
                cls.close_other_talk(wk)
                raise Exception("梦境寻宝未开启")
            cls.go_to_specific_map(wk)

    @classmethod
    def go_to_another_map(cls, wk: Worker):
        cur_map_name = cls.cur_map(wk)
        if cur_map_name in ["开封"]:
            return
        wk.record(f"正在从 {cur_map_name} 前往下一地图...")
        cls.cross_map_by_way(wk, cur_map_name)

    @classmethod
    def click_treasure(cls, wk: Worker):  # 开宝箱
        for i in range(8):
            if not wk.find_pic_relative_click(*RECT_FULL, "梦境宝箱.bmp|梦境宝箱2.bmp", delta_color="1c1c1c"):
                break
            wk.record("发现并点击梦境宝箱！")
            cls.click_confirm(wk)
            msleep(2500)
            # 博弈
            if wk.find_pic_click(*RECT_FULL, "博弈小.bmp"):
                wk.record("正在博弈... 点击小！")
                msleep(1000)
                if wk.find_pic_click(*RECT_FULL, "确定6.bmp", timeout=400):
                    wk.record("骰子确定点击小")
                else:
                    wk.record("骰子没点到确定")
                msleep(400)
                cls.click_confirm(wk)
                msleep(400)
            if cls.click_confirm(wk):
                wk.record("正在转转盘...")
                msleep(5000)
                cls.click_cancel(wk, RECT=RECT_FULL, timeout=800)
            if wk.find_pic(*RECT_FULL, "梦境答题.bmp"):
                wk.record("答题中...")
                wk.move_click(216, 263+40*rnd(0, 3))
                msleep(500)
                wk.move_click(582, 383)  # 确定
            msleep(1500)
            return True
        cls.click_cancel(wk, timeout=0, RECT=RECT_FULL)
        return False

    @classmethod
    def get_dx(cls):
        return rnd(14, 32)

    @classmethod
    def ready_overlap_auth(cls, wk: Worker):
        # 梦境打不开组队平台, 偏色要大一点
        delta_color = "202020"
        if cls.open_clan_page(wk):  # 尝试打开战队
            delta_color = "101010"
        return delta_color

    @classmethod
    def adjust_color(cls, wk: Worker, pic_list_length: int, delta_color: str):
        if delta_color == "101010":
            return delta_color
        if pic_list_length > 1:
            wk.record("---")
            return cls.sub_colors(delta_color)
        if pic_list_length < 1:
            wk.record("+++")
            return cls.add_colors(delta_color)
        return delta_color

    @classmethod
    def get_default_biz_config(cls):
        return {
            "只捡箱子": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_meng_jing_only_box.setChecked(
            cls.CONFIG["只捡箱子"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["只捡箱子"] = settings.wnd_main.chk_meng_jing_only_box.isChecked()
        super().cfg_save(plan_name)
