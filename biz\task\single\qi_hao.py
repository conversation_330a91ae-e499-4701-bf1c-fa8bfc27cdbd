import copy
from utils import *
from biz.constants.constants import *
from biz.task.__base import TaskBase
from biz.obj.worker import Worker


class TaskQiHao(TaskBase):
    TASK_NAME = "起号任务"
    NPC_TALK_ITEM_DICT = {
        "老顽童": ["打开快捷键说明面板", "隐藏其他玩家", "打开好友面板"],
        "江湖万事通": ["打听任务消息"]
    }

    @classmethod
    def run(cls, wk: Worker):
        task_condition_func_dict = {
            "捕捉小猪仔进行中": cls._catch_pig,
            "教训猴子进行中": cls._fight_monkey,
            "回复黄药师进行中": cls._call_huang,
        }
        while True:
            if wk.is_fight:
                cls.fight_operation(wk)
            if wk.cfg_plan_task["十级结束"] and cls.cur_map(wk) == "开封":
                cls.join_school(wk)
                return
            if cls.is_talk_open(wk):
                cls.talk_click_first_item(wk)
            if wk.is_stuck:
                wk.record("任务NPC寻路中...")
                cls.open_task_npc_click(
                    wk, condition_func_dict=task_condition_func_dict
                )
            msleep(500)

    @classmethod
    def talk_click_first_item(cls, wk: Worker):
        cur_talk_need_click_items = []
        talk_name = cls.get_talk_name(wk)
        for npc_name, talk_items in cls.NPC_TALK_ITEM_DICT.items():
            if talk_name == npc_name:
                wk.record(f"正在与{npc_name}对话...")
                cur_talk_need_click_items = copy.deepcopy(talk_items)
                break
        wk.record("对话中...")
        for i in range(12):
            if not cls.is_talk_open(wk, timeout=400):
                break
            msleep(100)
            # 优先点任务项
            if wk.find_color_click(*RECT_TALK, COLOR_TALK_ITEM_TASK):
                continue
            # 没有再点指定项
            if cur_talk_need_click_items and wk.find_str_click(
                *RECT_TALK, cur_talk_need_click_items[0], COLOR_TALK_ITEM, timeout=500
            ):
                wk.record(f"点击 {cur_talk_need_click_items[0]} 成功")
                cur_talk_need_click_items.pop(0)
                continue
            # 如果是江湖万事通 和 银湘玉
            if talk_name in ["江湖万事通", "银湘玉"]:
                if wk.find_str(*RECT_TALK, "江湖隐侠|查看包裹和仓库", COLOR_TALK_ITEM):
                    wk.find_str_click(*RECT_TALK, "退出|离开", COLOR_TALK_ITEM)
                    continue
            # 然后点第一项
            if wk.find_color_click(*RECT_TALK, COLOR_TALK_ITEM):
                msleep(500)
                continue
            # 都没有就点空白区域
            cls.talk_click_space(wk)

    @classmethod
    def join_school(cls, wk: Worker):
        wk.record("正在加入明教...")
        cls.talk_with_cur_map_npc(wk, "明教接引使者", ["加入明教"])
        msleep(1000)
        cls.close_pages(wk)
        wk.key_press(VK_F8)
        wk.record("加入门派完成")

    @classmethod
    def _catch_pig(cls, wk: Worker):
        wk.record("开始捕捉小猪仔")
        cls.close_pages(wk)
        cls.run_to_map(wk, "桃花林")
        if cls.cur_map(wk) != "桃花林":
            wk.record("跑图失败")
            return
        catch_success = False
        for _ in range(120):
            while wk.is_fight:
                if cls.is_people_action(wk):
                    if not catch_success:
                        x, y = wk.get_str_pos(*RECT_FULL, "小猪仔", COLOR_RED)
                        if x > 0:
                            wk.record("找到小猪仔, 开始捕捉")
                            wk.find_pic_click(*RECT_FULL, "收服.bmp")
                            wk.move_click(x + 24, y + 85)
                            catch_success = True
                            msleep(500)
                            continue
                    wk.key_press_combo(VK_ALT, VK_A)
                msleep(1000)
            if catch_success:
                cls.stop_auto_find_way(wk)
                break
            if wk.is_stuck:
                wk.record("自动遇怪中...")
                wk.key_press_combo(VK_ALT, VK_O)  # 自动遇怪
                msleep(500)
            msleep(1000)
        if not catch_success:
            wk.record("捕捉小猪仔超时")
            return
        wk.record("捕捉小猪仔完成, 回去交任务")
        # 交任务
        for i in range(60):
            if wk.is_fight:
                cls.fight_operation(wk)
            if wk.is_stuck:
                cls.open_task_npc_click(wk)
            msleep(1000)
            if cls.is_talk_open(wk):
                if wk.find_str(*RECT_FULL, "随从教员", COLOR_NPC_NAME):
                    cls.talk_click_first_item(wk)
                    wk.record("交任务成功")
                    cls.close_other_talk(wk)
                    msleep(500)
                    # BB出战
                    wk.key_press_combo(VK_ALT, VK_P)
                    msleep(1000)
                    wk.move_click(*POS_PET_FIGHT)
                    wk.record("BB出战成功")
                    return
                cls.close_other_talk(wk)

    @classmethod
    def _fight_monkey(cls, wk: Worker):
        wk.record("开始教训猴子...")
        cls.close_pages(wk)
        for _ in range(120):
            if wk.is_fight:
                cls.fight_operation(wk)
                cls.stop_auto_find_way(wk)
                break
            if wk.is_stuck:
                wk.key_press_combo(VK_ALT, VK_O)  # 自动遇怪
                msleep(500)
            msleep(1000)
        wk.record("教训猴子结束...")

    @classmethod
    def _call_huang(cls, wk: Worker):
        if cls.cur_map(wk) != "桃花岛":
            wk.record("回复黄药师...")
            wk.key_press(VK_F8)
        cls.open_task_npc_click(wk)
        wk.is_stuck = False

    @classmethod
    def is_cur_task_desc(cls, wk: Worker):
        return True
    
    @classmethod
    def get_default_biz_config(cls):
        return {
            "十级结束": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_qi_hao_end.setChecked(cls.CONFIG["十级结束"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["十级结束"] = settings.wnd_main.chk_qi_hao_end.isChecked()
        super().cfg_save(plan_name)
