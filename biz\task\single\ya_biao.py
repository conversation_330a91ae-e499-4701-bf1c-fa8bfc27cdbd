from biz.constants.constants import *
from biz.exception import TaskFinalStatusException
from biz.task.__base import TaskBase
from utils import *
from biz.obj.worker import Worker


class TaskYaBiao(TaskBase):
    TASK_NAME = "押镖"
    IS_TASK_FIX_EQUIP_BB_ENABLE = True

    @classmethod
    def run(cls, wk: Worker):
        cls.leave_team(wk)
        skip_recv = False
        cls.refresh_task_list(wk)
        if cls.is_cur_task_desc(wk):
            skip_recv = True
        for _ in range(100):
            msleep(800)
            try:
                if skip_recv or cls.recv_task(wk):
                    cls.do_task(wk)
                    skip_recv = False
            except TaskFinalStatusException:
                wk.record("切回任务失败, 重新接任务")
                continue
            except Exception as e:
                wk.record(f'押镖异常了: {e}')
                break

    @classmethod
    def get_task_publisher_name(cls) -> str:
        return "震关西"

    @classmethod
    def recv_task(cls, wk: Worker):
        wk.record("正在接任务...")
        cls.back_to_kai_feng(wk)
        cls.do_fix_bb(wk)
        for i in range(rate(1000)):
            if cls.is_talk_open(wk):
                cls.reply_task(wk)
                if cls.talk_recv_task(wk):
                    wk.record("接任务成功")
                    return True
                cls.close_other_talk(wk)
            if i % 50 == 0 or wk.is_stuck:
                cls.click_system_task_name(wk)
            msleep(600)
        cls.close_other_talk(wk)
        return False

    @classmethod
    def do_task(cls, wk: Worker):
        npc_name = ""
        cls.open_task_page(wk)
        if cls.region_task_desc_find_str(wk, "及时回到开封", timeout=600):
            npc_name = cls.region_task_status_get_npc_name_pro(wk, timeout=600)
            print(f"接{npc_name}的押镖任务")
        wk.record(f"开始运镖, 目标人:{npc_name}...")
        for i in range(2500):
            if wk.is_fight:
                cls.fight_operation(wk)
            if i == 0 or wk.is_stuck:
                if cls.task_npc_find_way(wk, "级押镖任务", COLOR_TALK_ITEM_TASK, npc_name=npc_name):
                    break
            time.sleep(0.6)
        cls.close_other_talk(wk)

    @classmethod
    def check_confirm_popup(cls, wk: Worker):
        if cls.is_popup_show_info(wk, "在防外挂答题中回答错误"):
            cls.click_confirm(wk)
            return False
        return True

    @classmethod
    def is_cur_task_desc(cls, wk: Worker):
        return cls.region_task_desc_find_str(wk, "否则任务失败")

    @classmethod
    def change_to_origin_task(cls, wk: Worker):
        if cls.region_task_list_find_str_click(wk, "级押镖任务"):
            wk.record("已切回 原任务")
            msleep(400)
            return True

    @classmethod
    def reply_task(cls, wk: Worker):
        if wk.find_str_click(*RECT_TALK, "级押镖任务", COLOR_TALK_ITEM_TASK):
            wk.record("回复任务")
            msleep(500)
            cls.close_other_talk(wk)
            if cls.is_popup_show_info(wk, "处于反挂机惩罚"):
                cls.click_confirm(wk)
                cls.pay_fine(wk)
                return False
            return True
        return False

    @classmethod
    def talk_recv_task(cls, wk: Worker) -> bool:
        if wk.find_str_click(*RECT_TALK, "押镖", COLOR_TALK_ITEM):
            wk.record("接任务")
            msleep(500)
            if wk.cfg_plan_task["押镖方式"] == "游戏币":
                wk.record("押镖方式: 游戏币")
                wk.find_str_click(*RECT_TALK, "游戏币", COLOR_TALK_ITEM)
            else:
                wk.record("押镖方式: 通宝")
                wk.find_str_click(*RECT_TALK, "通宝", COLOR_TALK_ITEM)
            msleep(500)
            if cls.is_talk_show_info(wk, "在防外挂答题中回答错误"):
                wk.record("在防外挂答题中回答错误, 等待1分钟后再试...")
                cls.close_other_talk(wk)
                msleep(60000)
                return False
            max_x, max_y = cls.get_max_level_pos(wk)
            if max_x:
                wk.move_click(max_x, max_y)
                msleep(500)
                wk.find_str_click(*RECT_TALK, "接受", COLOR_TALK_ITEM)
                msleep(500)
            if cls.is_talk_show_info(wk, "你明日再来吧"):
                wk.record("押镖任务已经做完了")
                raise Exception("押镖任务已经做完了")
            if cls.is_popup_show_info(wk, "包裹已满"):
                wk.record("包裹已满, 正在丢弃垃圾...")
                cls.click_confirm(wk)
                cls.throw_baggage(wk)
                return False
            return True
        return False

    @classmethod
    def get_max_level_pos(cls, wk: Worker):
        res_list = wk.find_str_ex(
            *RECT_TALK, "级押镖任务", COLOR_TALK_ITEM, timeout=600)
        max_x, max_y = -1, -1
        max_level = 0
        # wk.record(f"找到的 级押镖任务: {res_list}")
        for _, x, y in res_list:
            level = wk.ocr(x-20, y-2, x, y+15, COLOR_TALK_ITEM, zk=ZK_DIGIT_11)
            # wk.record(f"{level}级押镖任务")
            if level and int(level) > max_level:
                max_level = int(level)
                max_x = x
                max_y = y
        if max_level == 0:
            wk.record("识别押镖任务等级失败")
        else:
            wk.record(f"接{max_level}级押镖任务")
        return max_x, max_y

    @classmethod
    def is_time_to_click_answer(cls, wk: Worker, i: int):
        res = i > 2
        if not res:  # 因为押镖有时候一显示就展示了错误的答案, 要先移过去让它消失
            dx = rnd(10, 20) if rnd(0, 1) else rnd(-20, -10)
            dy = rnd(10, 20) if rnd(0, 1) else rnd(-20, -10)
            if wk.find_pic_and_move(*RECT_FULL,
                                    "选择按钮.bmp|选择按钮2.bmp|选择按钮3.bmp",
                                    dx=dx, dy=dy):
                wk.move_relative(rnd(-2, 2), rnd(-2, 2))
                wk.re_move()
        return res  # 押镖要等一会再点

    @classmethod
    def get_default_biz_config(cls):
        return {
            "押镖方式": "游戏币",
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        set_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_ya_biao, cls.CONFIG["押镖方式"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["押镖方式"] = get_checked_radio_text_in_groupbox(
            settings.wnd_main.groupBox_ya_biao)
        super().cfg_save(plan_name)
