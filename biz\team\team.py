import settings
import threading

# 这个文件不要做业务逻辑，只做数据结构的定义
class Team:
    def __init__(self, idx) -> None:
        self.team_idx = idx  # 是第几个队伍，从0开始
        self.reset_all_signal()

    # 重置所有信号
    def reset_all_signal(self):
        self.task_name = ""  # 任务名
        self.sub_task = ""  # 子任务名
        self.fuben_name = ""  # 当前在执行的副本名
        self.is_difficult_task = False  # 队伍任务难度是否需要使用技能
        self.IS_TASK_FIX_EQUIP_BB_ENABLE = False  # 队伍任务是否支持修理忠诚
        self.is_call_bb_enable = False  # 队伍任务是否支持召唤BB
        self.is_super_chanllenge = False  # 队伍任务是否为超级挑战
        self.is_switch_primary_bb_enable = False  # 队伍任务是否切换首发宠
        self.need_answer_question = False  # 队伍是否需要答题
        self.need_auth_colors = False  # 验证是否需要调整偏色
        self.need_fix_equip_bb = False  # 队伍是否需要修理忠诚, 为True的话队长回城后触发修理
        self.fight_random = False  # 随机打怪
        self.true_enemy_pos = (-1, -1)  # 真身位置
        self.seal_main_first = True  # 优先封主怪
        self.is_yi_shou_wu_xing = False  # 异兽五行
        # 下面这些信号都有效期, 1分钟清空
        self.signal_leave_team = TeamSignal()  # 离开队伍
        self.signal_switch_line = TeamSignal()  # 换线
        self.signal_off_ride = TeamSignal()  # 卸下坐骑
        self.signal_on_ride = TeamSignal()  # 装备坐骑
        self.signal_drop_garbage = TeamSignal()  # 扔垃圾
        self.signal_get_gift = TeamSignal()  # 签到祥瑞
        self.signal_rest_bb = TeamSignal()  # 休息BB

class TeamSignal:
    def __init__(self) -> None:
        self.last_set_ts = 0
        self.mate_resp_arr = [None] * 5

    def leader_set(self, value=True):
        def reset_mate_resp():
            self.mate_resp_arr = [None] * 5
            
        if settings.cur_time_stamp - self.last_set_ts > 2: # 距离上次设置时间超过2秒才能设置下次
            self.last_set_ts = settings.cur_time_stamp
            self.mate_resp_arr = [value] * 5
            # 开启一个线程, 60秒后自动全部置为None
            threading.Timer(60, reset_mate_resp).start()

    def mate_query(self, row):
        return self.mate_resp_arr[row % 5]

    def mate_resp(self, row):
        self.mate_resp_arr[row % 5] = None