from threading import Thread

from biz.constants.constants import *
from biz.team.team import Team
from utils.plugins import PLUGIN_SDK
from const.const import *
import settings
import utils
from utils.utils import Mutex<PERSON>ocker, is_win11
from utils.wnd import send_key
from memwin import XMemory


class Worker(PLUGIN_SDK):
    def __init__(self, hwnd: int, obj, row: int = -1, team: Team = None):
        # 初始化Com类
        super().__init__(obj)
        # ---------------- 工人状态 ---------------
        self.is_run = False  # 是否在运行(在干活)
        self.is_pause = False  # 是否在暂停(在休息)
        self.is_end = True  # 是否已结束(干完活 或 没活干)
        # 设置图片和字库
        self.set_pic_pwd(PWD_PIC)
        self.set_dict_pwd(PWD_ZK)
        self.set_path(DIR_RES)
        self.set_dict(ZK_ALL_11, "zk0.txt")  # 宋11
        self.set_dict(ZK_DIGIT_11, "zk1.txt")  # 宋11
        self.set_dict(ZK_ALL_9, "zk2.txt")  # 宋9
        self.set_dict(ZK_DIGIT_9, "zk3.txt")  # 宋9
        self.set_dict(ZK_NAME_11, "zk4.txt")  # 宋11
        self.set_dict(ZK_BOLD_11, "zk5.txt")  # 宋11
        self.set_dict(ZK_DIGIT_11_EX, "zk6.txt")  # 宋11
        self.set_mouse_delay(30)
        self.set_keypad_delay(30)
        if os.getenv("LOCAL_YT_DEBUG") == "1":
            self.show_error_msg(False)
        else:
            self.show_error_msg(False)
        # ---------------- 窗口属性 ---------------
        self.hwnd = hwnd  # 窗口句柄
        self.pid = utils.get_pid_by_hwnd(hwnd)  # 进程ID
        self.row = row  # 窗口在中控表格的第几行,从0开始,-1表示全局对象
        self.is_lock = False  # 窗口是否锁定
        self.thread = None  # 执行线程对象
        self.h_thread = 0  # 执行线程的线程句柄
        self.x = 0  # 窗口左上角坐标x
        self.y = 0  # 窗口左上角坐标y
        self.log_lines = 0  # 日志行数
        # ---------------- 内存读写 ---------------
        self.xm = XMemory(hwnd)
        self.thread_stack_addr = 0  # 线程栈地址
        # ---------------- 业务相关 ---------------
        self.player_name = ""  # 当前玩家名
        self.player_idx = -1  # 自己在队伍中是几号队员
        self.plan_name = ""  # 当前方案名
        self.cur_task = ""  # 当前执行任务
        self.sub_task = ""  # 当前任务的子任务
        self.cur_task_cls = None  # 当前执行任务类
        self.cur_task_idx = -1  # 当前执行任务在列表中的下标
        self.cur_task_start_time_fmt = 0  # 当前任务开始时间
        self.teach_first = True  # 优先教化
        self.fail_count = 0  # 任务失败次数
        self.check_ls = True  # 检查是否要领双
        self.last_success_ts = 0  # 最后一次成功时间
        self.cfg_plan = settings.default_cfg_plan  # 配置方案
        self.cfg_plan_task = {}  # 配置方案["业务任务名"]
        self.team = team  # 队伍对象
        
        self.people_save_people_idx_x = [
            (0, 635),
            (1, 568),
            (2, 702),
            (3, 501),
            (4, 769),
        ]  # 给人物在队伍中的排序， 人物补血内的阈值x
        self.people_save_bb_idx_x = [
            (0, 555), 
            (1, 488), 
            (2, 622), 
            (3, 421), 
            (4, 689)
        ]
        self.after_fight_people_add_x = -1
        self.after_fight_bb_add_x = -1
        self.attack_bb_save_people_idx_x = self.people_save_people_idx_x
        self.attack_bb_save_bb_idx_x = self.people_save_bb_idx_x
        self.defend_bb_save_people_idx_x = self.people_save_people_idx_x
        self.defend_bb_save_bb_idx_x = self.people_save_bb_idx_x
        # 每次运行要重置的flag
        self.init_flag()
    
    def init_flag(self):
        self.have_school_ticket = True  # 是否有门派票
        self.have_ride = True  # 是否有坐骑
        self.is_stuck = True  # 是否卡住了
        self.is_fight = False  # 是否在战斗中
        self.fight_start_ts = 0  # 战斗开始时间戳
        self.cur_round = 0  # 当前战斗回合数
        self.hj_round = 0  # 打厚积的回合数
        self.jzz_round = 0  # 打金钟罩的回合数
        self.tbs_round = 0  # 打铁布衫的回合数
        self.pf_round = 0  # 打破釜沉舟的回合数
        self.hx_round = 0  # 打护心的回合数
        self.check_pf_round = False  # 是否检查更新破釜回合数 
        self.done_count = 0  # 当前任务完成次数
        self.setting_count = 0  # 当前任务设定次数
        self.is_defend_bb = False  # 是否为防守类BB
        self.fight_yan = False  # 是否在打燕南天
        self.fight_e_ren = False  # 是否在打恶人榜
        self.fight_meet_bb = False  # 是否遇到BB
        self.fight_meet_name = False  # 是否遇到指定野怪
        self.need_fix_equip_bb = False  # 是否需要修理装备和忠诚
        self.need_fix_equip = False  # 是否需要修理装备
        self.need_clear_bag = False  # 是否需要清理背包
        self.need_pay_fine = False  # 是否需要缴纳挂机罚款
        self.survive_enemy_pos_list = []  # 存活的敌人位置列表
        self.enemy_ci_xue_hited = False  # 敌人被刺穴命中
        self.boss_wu_xing_idx = -1  # boss技能的五行 -1表示无 0表示木 1表示土 2表示水 3表示火 4表示金 
        self.boss_recognize_round = 0  # 五行识别的回合数
        self.should_run_away = False  # 是否应逃跑
        self.fight_yan_count = 0  # 打燕南天次数
        self.is_called_bb = False  # 是否已经唤出过BB
        self.not_hit_ci_xue_pos = (-1, -1)  # 还没命中刺穴的怪物位置
        self.mate_check_leave = True  # 队员是否应检查暂离
        self.ma_zei_map_set = set()  # 马贼地图集合
        self.last_ma_zei_map = ""  # 马贼地图
        self.exclude_pos = set()  # 排除的坐标集合
        self.is_die = False  # 是否死亡
        self.lack_xmx = False  # 缺少熊猫香
        self.last_use_xmx_ts = 0  # 上次使用熊猫香的时间戳

    def show_in_tbe_console(self, col: int, info: str):
        if self.row == -1:
            print(info)
            return
        settings.wnd_main.sig_cell.emit(self.row, col, info)

    def pause(self):
        if self.row == -1:
            return
        settings.wnd_main.sig_cell.emit(self.row, COL_PAUSE, SELECTED)

    def record(self, info):
        info = f"{settings.cur_time_fmt}|{self.cur_task}|{info}"
        self.show_in_tbe_console(COL_LOG, info)
        # self.foobar_print_text(info, FOOBAR_TEXT_COLOR)
        row_num = self.row + 1
        log_path = f"{PATH_SOFTWARE_LOG}\\wnd_{row_num}.txt"
        utils.file_append_content(log_path, f"{info}\n")
        self.log_lines += 1
        if self.log_lines > 3000:  # 超过3000行, 移除前1000行
            wk = self
            self.reduce_log(wk, log_path)

    @MutexLocker
    def reduce_log(self, wk, log_path):
        utils.remove_first_1000_lines_inplace(log_path)
        self.log_lines = 2000

    # 隐藏窗口
    def hide_wnd(self, prev_x: int, prev_y: int):
        self.x, self.y = prev_x, prev_y  # 记忆之前坐标
        hide_x, hide_y = HIDE_X, utils.rnd(0, 200)
        utils.set_wnd_pos(self.hwnd, hide_x, hide_y)
        utils.activate_wnd(self.hwnd)  

    # 显示窗口
    def show_wnd(self):
        utils.set_wnd_pos(self.hwnd, self.x, self.y)
        utils.activate_wnd(self.hwnd)
        
    # 聚焦窗口
    def focus_wnd(self, hwnd: int):
        send_key()
        self.set_window_state(hwnd, "焦点")

    # 模拟真实鼠标
    def simulate_real_mouse(self):
        self.enable_real_mouse(True)

    # 绑定窗口
    def bind_window(self) -> bool:
        ret = super().bind_window(
            self.hwnd, MODE_DISPLAY, MODE_MOUSE, MODE_KEYPAD, MODE_BACK
        )
        if ret == 1:  # 若绑定成功
            return True
        # 若绑定失败, 则把"运行"列重置为空
        self.record(f"窗口绑定失败, 错误码: {ret}")
        if ret == -18:
            settings.wnd_main.sig_remove.emit(self)
        self.is_run, self.is_pause, self.is_end = False, False, True
        self.show_in_tbe_console(COL_RUN, "")
        return False
    
    def get_thread_stack_addr(self):
        if self.thread_stack_addr:
            return self.thread_stack_addr
        base_thread_init_thunk_addr = self.xm.get_module_func_addr("KERNEL32.dll", "BaseThreadInitThunk")
        stack_top_addr = self.xm.get_thread_stack_top_addr()
        for i in range(1000):
            thread_stack = stack_top_addr - 4096 + i * 4
            res = self.xm.read_int(thread_stack)
            if res == base_thread_init_thunk_addr:
                if is_win11():
                    thread_stack -= 4
                self.thread_stack_addr = hex(thread_stack)
                return self.thread_stack_addr
        self.thread_stack_addr = "-1"
        return "-1"
    
    def get_x(self):
        try:
            threadstack0_addr = self.get_thread_stack_addr()
            res = self.xm.get_value_from_addr_expr(f"{threadstack0_addr}-0xA30+0x1F8+0x88+0xC+0x78+0x52C")
            # print(f"x坐标: {res}")
            return res
        except:
            return -1
    
    def get_y(self):
        try:
            threadstack0_addr = self.get_thread_stack_addr()
            res = self.xm.get_value_from_addr_expr(f"{threadstack0_addr}-0xA30+0x1F8+0x88+0xC+0x78+0x530")
            # print(f"y坐标: {res}")
            return res
        except:
            return -1