from biz.task.__base import TaskBase
from biz.constants.constants import *
from utils import *
from biz.obj.worker import Worker

class TaskUseThingFight(TaskBase):
    IS_TEAM_TASK = True
    IS_TASK_FIX_EQUIP_BB_ENABLE = True
    THING_NAME = ""
    THING_PIC_NAME = ""
    SYSTEM_ERROR = ""
    END_LOG_INFO = ""
    
    @classmethod
    def run(cls, wk: Worker):
        if not cls.check_the_week(wk):
            return
        if cls.end_condition(wk):
            return
        map_name= wk.cfg_plan_task["地图"]
        wk.record(f"正在前往 {map_name}...")
        cls.run_to_map(wk, map_name)
        while True:
            if wk.is_fight:
                cls.fight_operation(wk)
                wk.done_count += 1
                wk.record(f"已完成 {wk.done_count} 次")
            if wk.cfg_plan_task['修理忠诚'] and wk.team.need_fix_equip_bb or wk.need_fix_equip_bb:
                wk.record("正在前往修理忠诚...")
                cls.run_to_map(wk)
                cls.fix_equip(wk)
                cls.fix_bb(wk)
                wk.team.need_fix_equip_bb = False
                wk.record("修理忠诚完成, 正在返回...")
                cls.run_to_map(wk, wk.cfg_plan_task["地图"])
            if cls.end_condition(wk):
                break
            if not cls.bag_use_item(wk, cls.THING_PIC_NAME, check_twice=True, is_close=False, bag_open_fail_true=True):
                break
            wk.record(f"使用 {cls.THING_NAME} 成功")
            cls.wait_for_fight(wk)
            
    @classmethod
    def end_condition(cls, wk: Worker):
        if wk.find_str(
            *RECT_INFO, cls.SYSTEM_ERROR, COLOR_RED
        ):
            wk.record(cls.END_LOG_INFO)
            return True
        return False
            
    @classmethod
    def get_default_biz_config(cls):
        return {
            "地图": "十字坡",
            "修理忠诚": False,
        }

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.cmb_hu_jiu_map.setCurrentText(cls.CONFIG["地图"])
        settings.wnd_main.chk_hu_jiu_fix_bb.setChecked(cls.CONFIG["修理忠诚"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["地图"] = settings.wnd_main.cmb_hu_jiu_map.currentText()
        cls.CONFIG["修理忠诚"] = settings.wnd_main.chk_hu_jiu_fix_bb.isChecked()
        super().cfg_save(plan_name)


class TaskDaHu(TaskUseThingFight):
    TASK_NAME = "带队打虎"
    THING_NAME = "礼花"
    THING_PIC_NAME = "庆年礼花.bmp|贺岁礼花.bmp"
    SYSTEM_ERROR = "此道具只在指定时间和指定地图才能使用"
    END_LOG_INFO = "不在活动时间, 自动结束"
    
    @classmethod
    def end_condition(cls, wk: Worker):
        # 不在活动时间, 直接结束
        print(f'settings.cur_time_fmt:{settings.cur_time_fmt}')
        if settings.cur_time_fmt > "21:33:00" or settings.cur_time_fmt < "20:30:00":
            return True
        # 这期间才刚刚开始 不要结束
        if settings.cur_time_fmt < "20:33:05":
            return False
        return super().end_condition(wk)
    
    @classmethod
    def check_the_week(cls, wk: Worker):
        weekday = get_week_day()
        if weekday not in [1, 2, 3, 4, 5]:
            wk.record(f"打虎只能在星期一二三四五, 今天是星期{weekday}")
            return False
        return True



class TaskDaJiu(TaskUseThingFight):
    TASK_NAME = "带队打酒"
    THING_NAME = "百草蜜酿"
    THING_PIC_NAME = "百草蜜酿.bmp"
    SYSTEM_ERROR = "不能继续使用"
    END_LOG_INFO = "已完成所有次数, 自动结束"

    @classmethod
    def end_condition(cls, wk: Worker):
        if wk.done_count >= 30:
            return True
        return super().end_condition(wk)