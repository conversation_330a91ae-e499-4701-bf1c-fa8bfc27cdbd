from utils import *
from biz.task.__base import TaskBase
from biz.constants.constants import *
from biz.obj.worker import Worker


class TaskBoss(TaskBase):
    TASK_NAME = "超级挑战"
    IS_TEAM_TASK = True
    NEED_AVOID_FIGHT = True
    IS_DIFFICULT_TASK = True
    IS_CALL_BB_ENABLE = True
    SEAL_MAIN_FIRST = False  # 随机封
    
    @classmethod
    def before_run(cls, wk: Worker):
        wk.team.is_super_chanllenge = True
        return super().before_run(wk)

    @classmethod
    def run(cls, wk: Worker):
        cls.on_ride(wk)
        if wk.cfg_plan_task["启动时休息当前BB"]:
            wk.team.signal_rest_bb.leader_set()
            cls.do_rest_bb(wk)
        last_fix_ts = 0
        for boss_name, (map_name, boss_x, boss_y) in cls.NPC_POS.items():
            count = wk.cfg_plan_task[boss_name]
            if count == -1:
                wk.record(f"{boss_name} 挑战强度为-1, 跳过")
                continue
            cls.wait_mate_return_team(wk)
            if wk.cfg_plan_task.get("换BOSS前修理BB") and settings.cur_time_stamp - last_fix_ts > 2 * 60:
                wk.record(f"即将挑战{boss_name}, 开始修理忠诚...")
                cls.fix_equip(wk)
                cls.fix_bb(wk)
                cls.go_to_ke_zhan_supply(wk, fight_run_away=False)
                last_fix_ts = settings.cur_time_stamp
            wk.record(f"正在前往{map_name}挑战{boss_name}...")
            wk.done_count = -1  # -1为基础强度
            flag = False
            dx = cls.name_calc_offset_x(boss_name)
            color = COLOR_GOLD + "|" + COLOR_CYAN
            for _ in range(300):
                count = wk.cfg_plan_task[boss_name]
                if wk.done_count >= count:
                    break  # 跳出循环打下一个boss
                msleep(500)
                if wk.is_fight:
                    cls.fight_operation(wk)
                    if flag:
                        wk.done_count += 1
                        wk.record(f"已完成{wk.done_count}/{count}")
                        continue
                elif cls.cur_map(wk) not in ["", map_name]:  # 空的话可能是战斗中打开了东西挡住了战斗标志位
                    cls.run_to_map(wk, map_name)
                    continue
                if (
                    not wk.find_str_offset_click(*RECT_FIND, 
                                                 boss_name, 
                                                 color, 
                                                 dx=dx,
                                                 dy=rnd(80,110),
                                                 sim=0.9, limit_border=True, limit_y=Y_LIMIT_CLICK)
                    and wk.is_stuck
                ):
                    if rnd(0, 5) == 1:
                        wk.record(f"未找到 {boss_name}")
                    cls.open_big_map(wk)
                    if cls.is_big_map_open(wk):
                        wk.move_click(boss_x, boss_y)
                        wk.key_press(VK_TAB)  # 关闭大地图
                        wk.is_stuck = False
                msleep(500)
                if not cls.is_talk_open(wk):
                    continue
                if wk.find_str_click(*RECT_TALK, "开始挑战", COLOR_TALK_ITEM):
                    flag = True
                    wk.record("点击 开始挑战")
                    msleep(1000)
                    x, y = wk.get_str_pos(
                        *RECT_POPUP, "强度为", COLOR_BLACK, timeout=800
                    )
                    if x > 0:
                        qd = wk.ocr(
                            x + 41, y - 2, x + 52, y + 14, COLOR_BLACK, zk=ZK_DIGIT_11
                        )
                        wk.record(f"接下来的挑战强度为：{qd}")
                        if qd:
                            wk.done_count = int(qd) - 1
                            wk.record(f"已完成{wk.done_count}/{count}")
                        if wk.done_count >= count:
                            wk.record("已完成指定强度挑战")
                            cls.click_cancel(wk)
                            msleep(1000)
                            if wk.find_str(
                                *RECT_POPUP, "不愿意挑战", COLOR_BLACK, timeout=800
                            ):
                                cls.click_confirm(wk, RECT=RECT_POPUP)
                        else:
                            cls.click_confirm(wk, RECT=RECT_POPUP)
                            cls.wait_for_fight(wk)
                    continue
                cls.close_other_talk(wk)

    @classmethod
    def is_meet_call_bb_condition(cls, wk: Worker):
        if wk.is_called_bb:  # 本次战斗已经唤出过BB了, 就直接返回
            return False
        if not wk.cfg_plan_task["剩怪唤出BB"]:
            return False
        if not cls.is_people_action(wk):
            return False
        setting_left_count = wk.cfg_plan_task["剩怪数量"]
        cur_left_count = len(wk.survive_enemy_pos_list)
        if cur_left_count >= setting_left_count:
            # print(f"当前剩余怪物数量{cur_left_count} >= 设定剩余怪物数量{setting_left_count}, 不唤出BB")
            return False
        if wk.cur_round == 1:
            wk.record("首回合避免卡顿干扰, 无论如何都不会唤出BB")
            return False
        wk.record(f"当前剩余怪物数量{cur_left_count} < 设定剩余怪物数量{setting_left_count}, 唤出BB!")
        return True
        
    @classmethod
    def call_bb(cls, wk: Worker):  # 唤出BB
        if not cls.is_meet_call_bb_condition(wk):
            return
        bb_name = wk.cfg_plan_task["唤出BB名字"].strip()
        cls.do_call_bb(wk, bb_name)
        
    @classmethod
    def after_fight_rest_call_bb(cls, wk: Worker):
        if not wk.cfg_plan_task["剩怪唤出BB"]:
            return
        cls.do_rest_bb(wk)
        
    @classmethod
    def recognize_enemy(cls, wk: Worker):
        if wk.cur_round > 1:
            super().recognize_enemy(wk)
            return 
        # 回合数为1的话要多识别一会儿
        for i in range(3):
            if cls.get_cur_enemy_count(wk) >= 10:
                break
            if i < 2:
                msleep(1000)

    @classmethod
    def get_default_biz_config(cls):
        return {
            "剩怪唤出BB": False,
            "剩怪数量": 4,
            "唤出BB名字": "白龙",
            "换BOSS前修理BB": True,
            "启动时休息当前BB": False,
        }
        
    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_super_challenge_left_monster_count.setChecked(
            cls.CONFIG["剩怪唤出BB"]
        )
        settings.wnd_main.spin_count_super_left_enemy_count.setValue(cls.CONFIG["剩怪数量"])
        settings.wnd_main.edt_super_left_enemy_call_bb_name.setText(cls.CONFIG["唤出BB名字"])
        settings.wnd_main.chk_super_chanllenge_fix_bb.setChecked(
            cls.CONFIG["换BOSS前修理BB"]
        )
        settings.wnd_main.chk_super_challenge_rest_cur_bb.setChecked(
            cls.CONFIG["启动时休息当前BB"]
        )
        
    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        # cls.CONFIG = cls.get_default_biz_config()  这里要避免再读默认, 因为子类已经读过控件了, 不能再被默认覆盖
        cls.CONFIG["剩怪唤出BB"] = settings.wnd_main.chk_super_challenge_left_monster_count.isChecked()
        cls.CONFIG["剩怪数量"] = settings.wnd_main.spin_count_super_left_enemy_count.value()
        cls.CONFIG["唤出BB名字"] = settings.wnd_main.edt_super_left_enemy_call_bb_name.text()
        cls.CONFIG["换BOSS前修理BB"] = settings.wnd_main.chk_super_chanllenge_fix_bb.isChecked()
        cls.CONFIG["启动时休息当前BB"] = settings.wnd_main.chk_super_challenge_rest_cur_bb.isChecked()
        super().cfg_save(plan_name)

class TaskMingBu(TaskBoss):
    TASK_NAME = "四大名捕"
    NPC_POS = {
        "无情": ("龙门", 805,100),
        "铁手": ("终南山", 830,178),
        "追命": ("白驼山", 789,196),
        "冷血": ("碧峰峡", 672,304),
    }

    @classmethod
    def get_default_biz_config(cls):
        res = {
            "无情": 0,
            "铁手": 0,
            "追命": 0,
            "冷血": 0,
        }
        res.update(super().get_default_biz_config())
        return res

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_mingbu_wuqing.setValue(cls.CONFIG["无情"])
        settings.wnd_main.spin_count_mingbu_tieshou.setValue(cls.CONFIG["铁手"])
        settings.wnd_main.spin_count_mingbu_zhuiming.setValue(cls.CONFIG["追命"])
        settings.wnd_main.spin_count_mingbu_lengxue.setValue(cls.CONFIG["冷血"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["无情"] = settings.wnd_main.spin_count_mingbu_wuqing.value()
        cls.CONFIG["铁手"] = settings.wnd_main.spin_count_mingbu_tieshou.value()
        cls.CONFIG["追命"] = settings.wnd_main.spin_count_mingbu_zhuiming.value()
        cls.CONFIG["冷血"] = settings.wnd_main.spin_count_mingbu_lengxue.value()
        super().cfg_save(plan_name)


class TaskMingJiao(TaskBoss):
    TASK_NAME = "明教高手"
    NPC_POS = {
        "韦一笑": ("恶人谷", 611,285),
        "谢逊": ("无量山", 715,312),
        "殷天正": ("雁门关外", 640,90),
        "紫衫龙王": ("大草原", 540,189),
    }

    @classmethod
    def get_default_biz_config(cls):
        res = {
            "韦一笑": 0,
            "谢逊": 0,
            "殷天正": 0,
            "紫衫龙王": 0,
        }
        res.update(super().get_default_biz_config())
        return res

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_mingjiao_weiyixiao.setValue(cls.CONFIG["韦一笑"])
        settings.wnd_main.spin_count_mingjiao_xiexun.setValue(cls.CONFIG["谢逊"])
        settings.wnd_main.spin_count_mingjiao_yintianzheng.setValue(
            cls.CONFIG["殷天正"]
        )
        settings.wnd_main.spin_count_mingjiao_zishan.setValue(cls.CONFIG["紫衫龙王"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["韦一笑"] = settings.wnd_main.spin_count_mingjiao_weiyixiao.value()
        cls.CONFIG["谢逊"] = settings.wnd_main.spin_count_mingjiao_xiexun.value()
        cls.CONFIG["殷天正"] = (
            settings.wnd_main.spin_count_mingjiao_yintianzheng.value()
        )
        cls.CONFIG["紫衫龙王"] = settings.wnd_main.spin_count_mingjiao_zishan.value()
        super().cfg_save(plan_name)


class TaskWuJue(TaskBoss):
    TASK_NAME = "五绝"
    NPC_POS = {
        "黄药师": ("古墓一层", 670,366),
        "欧阳锋": ("古墓二层", 795,383),
        "段智兴": ("古墓三层", 469,219),
        "洪七": ("秦陵一层", 612,190),
    }

    @classmethod
    def get_default_biz_config(cls):
        res = {
            "黄药师": 0,
            "欧阳锋": 0,
            "段智兴": 0,
            "洪七": 0,
        }
        res.update(super().get_default_biz_config())
        return res
        
    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_wujue_huangyaoshi.setValue(cls.CONFIG["黄药师"])
        settings.wnd_main.spin_count_wujue_ouyangfeng.setValue(cls.CONFIG["欧阳锋"])
        settings.wnd_main.spin_count_wujue_duanzhixing.setValue(cls.CONFIG["段智兴"])
        settings.wnd_main.spin_count_wujue_hongqi.setValue(cls.CONFIG["洪七"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["黄药师"] = settings.wnd_main.spin_count_wujue_huangyaoshi.value()
        cls.CONFIG["欧阳锋"] = settings.wnd_main.spin_count_wujue_ouyangfeng.value()
        cls.CONFIG["段智兴"] = settings.wnd_main.spin_count_wujue_duanzhixing.value()
        cls.CONFIG["洪七"] = settings.wnd_main.spin_count_wujue_hongqi.value()
        super().cfg_save(plan_name)


class TaskZiJin(TaskBoss):
    TASK_NAME = "紫禁高手"
    NPC_POS = {
        "花满楼": ("秦陵二层", 777,264),
        "司空摘星": ("玄霜浅滩", 786,281),
        "叶孤城": ("金风山道", 693,306),
        "西门吹雪": ("赤霞渡口", 754,90),
    }

    @classmethod
    def get_default_biz_config(cls):
        res = {
            "花满楼": 0,
            "司空摘星": 0,
            "叶孤城": 0,
            "西门吹雪": 0,
        }
        res.update(super().get_default_biz_config())
        return res

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_zijin_huamanlou.setValue(cls.CONFIG["花满楼"])
        settings.wnd_main.spin_count_zijin_sikongzhaixing.setValue(
            cls.CONFIG["司空摘星"]
        )
        settings.wnd_main.spin_count_zijin_yegucheng.setValue(cls.CONFIG["叶孤城"])
        settings.wnd_main.spin_count_zijin_ximenchuixue.setValue(cls.CONFIG["西门吹雪"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["花满楼"] = settings.wnd_main.spin_count_zijin_huamanlou.value()
        cls.CONFIG["司空摘星"] = (
            settings.wnd_main.spin_count_zijin_sikongzhaixing.value()
        )
        cls.CONFIG["叶孤城"] = settings.wnd_main.spin_count_zijin_yegucheng.value()
        cls.CONFIG["西门吹雪"] = settings.wnd_main.spin_count_zijin_ximenchuixue.value()
        super().cfg_save(plan_name)


class TaskZhenLong(TaskBoss):
    TASK_NAME = "珍珑棋局"
    NPC_POS = {
        "苏星河": ("青云幽谷", 709,173),
        "丁春秋": ("燕子坞", 792,251),
        "李秋水": ("楼兰古道", 752,146),
        "天山童姥": ("玉门关", 789,170),
    }

    @classmethod
    def get_default_biz_config(cls):
        res = {
            "苏星河": 0,
            "丁春秋": 0,
            "李秋水": 0,
            "天山童姥": 0,
        }
        res.update(super().get_default_biz_config())
        return res

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_zhenlong_suxinghe.setValue(cls.CONFIG["苏星河"])
        settings.wnd_main.spin_count_zhenlong_dingchunqiu.setValue(cls.CONFIG["丁春秋"])
        settings.wnd_main.spin_count_zhenlong_liqiusuhi.setValue(cls.CONFIG["李秋水"])
        settings.wnd_main.spin_count_zhenlong_tianshantongmu.setValue(
            cls.CONFIG["天山童姥"]
        )

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["苏星河"] = settings.wnd_main.spin_count_zhenlong_suxinghe.value()
        cls.CONFIG["丁春秋"] = settings.wnd_main.spin_count_zhenlong_dingchunqiu.value()
        cls.CONFIG["李秋水"] = settings.wnd_main.spin_count_zhenlong_liqiusuhi.value()
        cls.CONFIG["天山童姥"] = (
            settings.wnd_main.spin_count_zhenlong_tianshantongmu.value()
        )
        super().cfg_save(plan_name)
