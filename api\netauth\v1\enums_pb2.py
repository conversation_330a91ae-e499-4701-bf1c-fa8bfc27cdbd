# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/netauth/v1/enums.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='api/netauth/v1/enums.proto',
  package='api.netauth.v1',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x61pi/netauth/v1/enums.proto\x12\x0e\x61pi.netauth.v1*1\n\nCardStatus\x12\n\n\x06Normal\x10\x00\x12\x0b\n\x07\x45xpired\x10\x01\x12\n\n\x06\x46rozen\x10\x02*K\n\nCardRights\x12\x07\n\x03One\x10\x00\x12\x08\n\x04\x46ive\x10\x01\x12\x07\n\x03Ten\x10\x02\x12\n\n\x06Thirty\x10\x03\x12\n\n\x06Twenty\x10\x04\x12\t\n\x05Sixty\x10\x05*5\n\x08\x43\x61rdType\x12\x07\n\x03\x44\x61y\x10\x00\x12\t\n\x05Month\x10\x01\x12\x08\n\x04Year\x10\x02\x12\x0b\n\x07\x46orever\x10\x03*\'\n\rPaymentMethod\x12\n\n\x06\x41lipay\x10\x00\x12\n\n\x06Wechat\x10\x01\x62\x06proto3'
)

_CARDSTATUS = _descriptor.EnumDescriptor(
  name='CardStatus',
  full_name='api.netauth.v1.CardStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Normal', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Expired', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Frozen', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=46,
  serialized_end=95,
)
_sym_db.RegisterEnumDescriptor(_CARDSTATUS)

CardStatus = enum_type_wrapper.EnumTypeWrapper(_CARDSTATUS)
_CARDRIGHTS = _descriptor.EnumDescriptor(
  name='CardRights',
  full_name='api.netauth.v1.CardRights',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='One', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Five', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Ten', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Thirty', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Twenty', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Sixty', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=97,
  serialized_end=172,
)
_sym_db.RegisterEnumDescriptor(_CARDRIGHTS)

CardRights = enum_type_wrapper.EnumTypeWrapper(_CARDRIGHTS)
_CARDTYPE = _descriptor.EnumDescriptor(
  name='CardType',
  full_name='api.netauth.v1.CardType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Day', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Month', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Year', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Forever', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=174,
  serialized_end=227,
)
_sym_db.RegisterEnumDescriptor(_CARDTYPE)

CardType = enum_type_wrapper.EnumTypeWrapper(_CARDTYPE)
_PAYMENTMETHOD = _descriptor.EnumDescriptor(
  name='PaymentMethod',
  full_name='api.netauth.v1.PaymentMethod',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Alipay', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Wechat', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=229,
  serialized_end=268,
)
_sym_db.RegisterEnumDescriptor(_PAYMENTMETHOD)

PaymentMethod = enum_type_wrapper.EnumTypeWrapper(_PAYMENTMETHOD)
Normal = 0
Expired = 1
Frozen = 2
One = 0
Five = 1
Ten = 2
Thirty = 3
Twenty = 4
Sixty = 5
Day = 0
Month = 1
Year = 2
Forever = 3
Alipay = 0
Wechat = 1


DESCRIPTOR.enum_types_by_name['CardStatus'] = _CARDSTATUS
DESCRIPTOR.enum_types_by_name['CardRights'] = _CARDRIGHTS
DESCRIPTOR.enum_types_by_name['CardType'] = _CARDTYPE
DESCRIPTOR.enum_types_by_name['PaymentMethod'] = _PAYMENTMETHOD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)


# @@protoc_insertion_point(module_scope)
