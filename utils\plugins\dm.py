import time
from typing import List
from PySide2.QtCore import QMutex, QMutexLocker
from win32process import WriteProcessMemory
from win32api import GetModuleHandle

from biz.constants.constants import *
from const.const import *
from .utils import *

class Dm():
	COM_NAME = 'dm.dmsoft'

	def __init__(self, obj, is_first=False):
		# com组件对象
		self.obj = obj
		# 线程锁
		self.mutex = QMutex(QMutex.Recursive)
		# 大漠是收费插件, 要破解一下
		if is_first:
			pass_dm_vip(obj)

	def reg(self, reg_code: str, add_code: str) -> bool:
		ret = self.obj.Reg(reg_code, add_code)
		return ret

	# 版本号
	def ver(self):
		locker = QMutexLocker(self.mutex)
		ret = self.obj.Ver()
		return ret
	
	def set_aero(self, enable: bool):
		ret = self.obj.SetAero(enable)
		return ret

	def set_path(self, path_res: str):
		# 设置资源路径
		self.obj.SetPath(path_res)

	def set_dict(self, idx: int, file_zk: str, zk_pwd=""):
		# 设置字库序号
		self.obj.SetDict(idx, file_zk)

	def set_pic_pwd(self, pic_pwd=""):
		self.obj.SetPicPwd(pic_pwd)

	def set_dict_pwd(self, zk_pwd=""):
		self.obj.SetDictPwd(zk_pwd)

	def set_mouse_delay(self, delay=10):
		self.obj.SetMouseDelay("windows", delay)

	def set_keypad_delay(self, delay=10):
		self.obj.SetKeypadDelay("windows", delay)

	def set_keypad_sync(self, open=True, max_waiting_ms=0):
		self.obj.EnableKeypadSync(1 if open else 0, max_waiting_ms)

	def screen_to_client(self, hwnd: int, screen_x, screen_y: int):
		ret, x, y = self.obj.ScreenToClient(hwnd, screen_x, screen_y)
		return x, y

	def get_mouse_point_window(self):
		hwnd = self.obj.GetMousePointWindow()
		return hwnd

	def get_window_state(self, hwnd: int, flag=WND_STATE_NORESP):
		ret = self.obj.GetWindowState(hwnd, flag)
		return ret
	
	def get_window_title(self, hwnd: int):
		ret = self.obj.GetWindowTitle(hwnd)
		return ret

	def set_clipboard(self, text: str):
		self.obj.SetClipboard(text)

	def send_paste(self, hwnd: int):
		locker = QMutexLocker(self.mutex)
		res = self.obj.SendPaste(hwnd)
		print('send_paste:', res)
		print('last error:', self.obj.GetLastError())
		
	def activate_window(self, hwnd: int, flag=1):
		self.obj.SetWindowState(hwnd, 1)

	def focus_window(self, hwnd:int):
		self.obj.SetWindowState(hwnd, 15)

	def down_cpu(self, rate: int):  # 0表示关闭优化，值越大效果越明显
		self.obj.DownCpu(rate)

	def run_app(self, app_path: str, mode=0):
		# 运行程序
		ret = self.obj.RunApp(app_path, mode)
		return ret
	
	def is_bind(self, hwnd: int):
		ret = self.obj.IsBind(hwnd)
		return ret
	
	def get_cursor_shape(self):
		ret = self.obj.GetCursorShape()
		return ret

	# 绑定成功, 返回1, 未绑定成功, 返回错误码
	def bind_window(self, hwnd: int, mode_display, mode_mouse, mode_keypad, mode_back, mode_public="") -> int:
		# locker = QMutexLocker(self.mutex)
		ret = self.obj.BindWindowEx(hwnd, mode_display, mode_mouse, mode_keypad, mode_public, mode_back)
		if ret == 1:
			pass
		else:
			ret = self.obj.GetLastError()
		return ret

	# 获取子窗口句柄, 没找到返回0
	def get_son_hwnd(self, hwnd: int) -> int:
		locker = QMutexLocker(self.mutex)
		return self.obj.GetWindow(hwnd, 1)

	def unbind_window(self):
		locker = QMutexLocker(self.mutex)
		self.obj.UnBindWindow()

	# 切换绑定检测窗口
	def switch_bind_detect_window(self, hwnd: int):
		locker = QMutexLocker(self.mutex)
		self.obj.UnBindWindow()
		self.obj.BindWindow(hwnd, "gdi", "windows", "windows", 0)

	# 切换绑定游戏窗口
	def switch_bind_game_window(
		self, hwnd: int, mode_display, mode_mouse, mode_keypad, mode_public, mode_back
	):
		locker = QMutexLocker(self.mutex)
		self.obj.UnBindWindow()
		self.obj.BindWindowEx(hwnd, mode_display, mode_mouse, mode_keypad,
							  mode_public, mode_back)
		activate_wnd(hwnd)  # 过完验证要激活窗口

	def lock_input(self, state: str):
		locker = QMutexLocker(self.mutex)
		map_dict = {"关闭锁定": 0, "锁定键鼠": 1, "锁定鼠标": 2, "锁定键盘": 3}
		flag = map_dict[state]
		self.obj.LockInput(flag)

	# 截图bmp
	def capture(self, x1, y1, x2, y2, save_path):
		# type: (int, int, int, int, str) -> None
		locker = QMutexLocker(self.mutex)
		self.obj.Capture(x1, y1, x2, y2, save_path)

	# 获取通配符对应的文件集合，每个图片以|分割
	def match_pic(self, pic: str, dir="") -> str:
		locker = QMutexLocker(self.mutex)
		if dir != "":  # 设置指定路径
			self.obj.SetPath(dir)
		res = self.obj.MatchPicName(pic)
		if dir != "":  # 设置回原路径
			self.obj.SetPath(DIR_RES)
		return res

	# 枚举窗口, 枚举到返回一个整数列表, 没枚举出返回空列表
	def enum_window(self, wnd_class="", wnd_title=""):
		locker = QMutexLocker(self.mutex)
		flag = 8 + 32
		if wnd_title != "":
			flag += 1
		if wnd_class != "":
			flag += 2
		hwnds = self.obj.EnumWindow(0, wnd_title, wnd_class, flag)
		if hwnds is None or hwnds == "":
			return []
		hwnd_list = hwnds.split(",")
		hwnd_list = [int(hwnd) for hwnd in hwnd_list]
		return hwnd_list

	# 找第一个满足条件的窗口, 找到返回窗口句柄int, 未找到返回0
	def find_window(self, wnd_class, wnd_title="", timeout=0):
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31)  或 (-1, -1, -1)
			hwnd = self.obj.FindWindow(wnd_class, wnd_title)
			if hwnd or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		return hwnd

	# 设置显示输入, 若mode="pic", 则xxx~"C\1.bmp", 若mode="mem", 则xxx~"addr,size"
	def set_display_input(self, mode: str, xxx=""):
		locker = QMutexLocker(self.mutex)
		if mode not in ("screen", "pic", "mem"):
			return
		if mode == "pic":
			self.obj.SetDisplayInput(f"pic:{xxx}")
		elif mode == "mem":
			self.obj.SetDisplayInput(f"mem:{xxx}")
		else:
			self.obj.SetDisplayInput("screen")

	# 启用真实鼠标
	def enable_real_mouse(self, enable: bool, delay=16, step=64):
		locker = QMutexLocker(self.mutex)
		if enable:
			self.obj.EnableRealMouse(2, delay, step)
		else:
			self.obj.EnableRealMouse(0, delay, step)

	# 显示错误信息
	def show_error_msg(self, show: bool):
		self.obj.SetShowErrorMsg(show)

	# 禁用系统睡眠
	def ban_sys_sleep(self):
		self.obj.DisablePowerSave()

	# 禁用屏幕保护
	def ban_screen_protect(self):
		self.obj.DisableScreenSave()

	# 获取机器码
	def get_machine_code(self):
		return self.obj.GetMachineCode()
	
	# 获取操作系统
	def get_os_type(self):
		return self.obj.GetOSType()
	
	# 获取屏幕色深
	def get_screen_depth(self):
		return self.obj.GetScreenDepth()
	
	# 获取显卡信息
	def get_display_info(self):
		return self.obj.GetDisplayInfo()
	
	# UAC
	def check_uac(self):
		return self.obj.CheckUAC()  # 1开了，0没开
	
	def set_uac(self, on=True):
		return self.obj.SetUAC(on)

	# 关机
	def exit_os(self):
		self.obj.ExitOs(1)

	def set_window_title(self, hwnd, title):
		self.obj.SetWindowText(hwnd, title)
		
	def set_window_state(self, hwnd, state: str):
		state_dict = {"恢复": 5, "焦点": 15, "恢复激活": 12}
		state_int = state_dict.get(state, 5)
		self.obj.SetWindowState(hwnd, state_int)
		
	def create_foobar(self, hwnd, x, y, w, h, rw, rh):
		locker = QMutexLocker(self.mutex)
		self.foobar = self.obj.CreateFoobarRoundRect(hwnd, x, y, w, h, rw, rh)
		print(self.foobar)
		self.obj.FoobarFillRect(self.foobar, 0, 0, w, h, "151517")
		# self.obj.FoobarSetTrans(self.foobar, 1, "151517", 0.3)
		self.obj.FoobarTextRect(self.foobar, 1, 1, w, h)
		self.obj.FoobarSetFont(self.foobar, "宋体", 10, 0)
		self.obj.FoobarLock(self.foobar)
		self.obj.FoobarUpdate(self.foobar)
		self.obj.SetWindowState(hwnd, 15)
		
	def foobar_print_text(self, text, color):
		locker = QMutexLocker(self.mutex)
		if not self.foobar:
			return
		self.obj.FoobarPrintText(self.foobar, text, color)
		self.obj.FoobarUpdate(self.foobar)

	def foobar_close(self, hwnd):
		locker = QMutexLocker(self.mutex)
		self.obj.FoobarClose(hwnd)


	#  ------------------------- 鼠标相关 -------------------------
	def move_to(self, x: int, y: int, is_delay=True):
		locker = QMutexLocker(self.mutex)
		self.obj.MoveTo(x, y)
		if is_delay:
			msleep(SLEEP_TIME)

	# 相对移动
	def move_relative(self, rx: int, ry: int):
		locker = QMutexLocker(self.mutex)
		self.obj.MoveR(rx, ry)

	def re_move(self, x=834, y=6):
		locker = QMutexLocker(self.mutex)
		self.obj.MoveTo(x, y)
		self.obj.MoveR(rnd(1,4),rnd(1,4))

	def left_down(self):
		locker = QMutexLocker(self.mutex)
		self.obj.LeftDown()
		msleep(SLEEP_TIME)

	def left_up(self):
		locker = QMutexLocker(self.mutex)
		self.obj.LeftUp()
		msleep(SLEEP_TIME)

	def left_click(self, re_move=True, is_delay=True):
		locker = QMutexLocker(self.mutex)
		self.obj.LeftClick()
		if is_delay:
			msleep(SLEEP_AFTER_CLICK)
		if re_move:
			self.re_move()

	def left_db_click(self, re_move=True):
		locker = QMutexLocker(self.mutex)
		self.obj.LeftClick()
		msleep(SLEEP_TIME)
		self.obj.LeftClick()
		if re_move:
			self.re_move()

	def right_click(self, re_move=True):
		locker = QMutexLocker(self.mutex)
		self.obj.RightClick()
		msleep(SLEEP_AFTER_CLICK)
		if re_move:
			self.re_move()

	def move_click(self, x, y, click_count=1, re_move=True, is_sleep=True, is_delay=True, relative=False):
		# type: (int, int, int, bool, bool, bool, bool) -> None
		locker = QMutexLocker(self.mutex)
		self.move_to(x, y, is_delay=is_delay)
		if relative:
			self.move_relative(1, 1)
		for i in range(click_count):
			self.obj.LeftClick()
		if is_sleep:
			msleep(SLEEP_AFTER_CLICK)
		if re_move:
			self.re_move()
			
	def move_relative_click(self, x, y, click_count=1, re_move=True, is_sleep=True, limit_border=False, limit_x=0, limit_y=0):
		# type: (int, int, int, bool, bool, bool, int, int) -> None
		locker = QMutexLocker(self.mutex)
		if limit_border:
			if limit_x and x > limit_x:
				x = limit_x
			if limit_y and y > limit_y:
				y = limit_y
		self.move_to(x, y)
		self.obj.MoveR(1, 2)
		self.obj.MoveR(-1, -1)
		for i in range(click_count):
			self.obj.LeftClick()
		if is_sleep:
			msleep(SLEEP_AFTER_CLICK)
		if re_move:
			self.re_move()

	def move_r_click(self, x, y, is_delay=True, re_move=True):
		# type: (int, int, bool, bool) -> None
		locker = QMutexLocker(self.mutex)
		self.move_to(x, y, is_delay)
		self.right_click(re_move)
		
	def move_db_click(self, x, y, re_move=True):
		# type: (int, int, bool) -> None
		locker = QMutexLocker(self.mutex)
		self.move_to(x, y)
		self.left_db_click(re_move)

	def move_wheel_down(self, x, y, count=1, re_move=True):
		# type: (int, int, int, bool) -> None
		locker = QMutexLocker(self.mutex)
		self.move_to(x, y)
		for i in range(count):
			self.obj.WheelDown()
			msleep(10)
		if re_move:
			self.re_move()

	def move_wheel_up(self, x, y, count=1, re_move=True):
		# type: (int, int, int, bool) -> None
		locker = QMutexLocker(self.mutex)
		self.move_to(x, y)
		for i in range(count):
			self.obj.WheelUp()
			msleep(10)
		if re_move:
			self.re_move()

	def move_drag_to(self, x0, y0, x, y, re_move=True):
		# type: (int, int, int, int, bool) -> None
		locker = QMutexLocker(self.mutex)
		if x0 != 0:
			self.move_to(x0, y0)
			msleep(SLEEP_TIME)
		self.left_down()
		msleep(SLEEP_TIME)
		self.move_to(x, y)
		msleep(SLEEP_TIME*2)
		self.left_up()
		msleep(SLEEP_TIME)
		if re_move:
			self.re_move()

	def wait_key(self, vk_code: int, timeout=0):
		self.obj.WaitKey(
			vk_code, timeout
		)  # 虚拟按键码,当此值为0，表示等待任意按键。 鼠标左键是1,鼠标右键时2,鼠标中键是4

	# ------------------------- 键盘相关 -------------------------
	def key_press(self, vk_code: int, num=1, delay=10):
		# type: (int, int, int) -> None
		locker = QMutexLocker(self.mutex)
		for i in range(num):
			self.obj.KeyPress(vk_code)
			msleep(delay)

	# 发送ASCII字符串
	def key_press_str(self, asc_str, delay=50):
		locker = QMutexLocker(self.mutex)
		self.obj.KeyPressStr(asc_str, delay)

	# 按组合键
	def key_press_combo(self, vk_state: int, vk_code: int):
		locker = QMutexLocker(self.mutex)
		self.obj.KeyDown(vk_state)
		msleep(10, 30)
		self.obj.KeyPress(vk_code)
		msleep(10, 30)
		self.obj.KeyUp(vk_state)

	# 按下键
	def key_down(self, vk_code: int):
		locker = QMutexLocker(self.mutex)
		self.obj.KeyDown(vk_code)

	# 抬起键
	def key_up(self, vk_code: int):
		locker = QMutexLocker(self.mutex)
		self.obj.KeyUp(vk_code)

	# 发送任意字符串
	def send_string(self, string, sync=False, lock=True, max_waiting_ms=30):
		if lock:
			locker = QMutexLocker(self.mutex)
		self.set_keypad_sync(sync, max_waiting_ms=max_waiting_ms)
		self.obj.SendString(self.hwnd, string)

	# 发送任意字符串(用于账号密码登录,未绑定窗口时使用)
	def send_string_ex(self, hwnd, string, delay=50):
		locker = QMutexLocker(self.mutex)
		for ch in string:
			self.obj.SendString(hwnd, ch)
			msleep(delay)
	
	# 不停的截取指定区域的图像，然后比较，如果改变就立刻返回0,否则等待直到指定的时间到达, 1表示卡屏，0表示正常    
	def is_display_dead(self, x1, y1, x2, y2, t):
		return self.obj.IsDisplayDead(x1, y1, x2, y2, t)

	# ------------------------- 找图相关 -------------------------
	# 找图, 找到返回True, 未找到返回False
	def find_pic(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31)  或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		ret = True if ret[0] >= 0 else False
		return ret

	# 找图扩展, 同时找多个图, 得到形如[(0,100,20), (1,30,40)]的列表, 一个都未找到返回[]
	def find_pic_ex(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> list[tuple[int, int, int]]
		locker = QMutexLocker(self.mutex)
		while True:
			ret_str = self.obj.FindPicEx(
				x1, y1, x2, y2, pic, delta_color, sim, order)
			if ret_str or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		# "0,100,20|1,30,40" -> [(0,100,20), (1,30,40)]
		ret_list = []
		if ret_str:
			pos_list = ret_str.split("|")  # ["1,100,20", "2,30,40"]
			for pos in pos_list:  # "1,100,20"
				kk = pos.split(",")  # ["1", "100", "20"]
				idx, x, y = int(kk[0]), int(kk[1]), int(kk[2])
				ret_list.append((idx, x, y))
		return ret_list

	# 找图左键点击, 找到返回True, 未找到返回False
	def find_pic_click(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0, re_move=True):
		# type: (int, int, int, int, str, str, float, int, int, bool) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4), re_move=re_move)
		ret = True if ret[0] >= 0 else False
		return ret
	
	# 找图左键相对移动后点击, 找到返回True, 未找到返回False
	def find_pic_relative_click(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0, re_move=True):
		# type: (int, int, int, int, str, str, float, int, int, bool) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_relative_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4), re_move=re_move)
		ret = True if ret[0] >= 0 else False
		return ret

	# 找图右键点击, 找到返回True, 未找到返回False
	def find_pic_r_click(
		self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0, re_move=True
	):
		# type: (int, int, int, int, str, str, float, int, int, bool) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_r_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4), re_move=re_move)
		ret = True if ret[0] >= 0 else False
		return ret

	# 找图shift+鼠标右击, 找到返回True, 未找到返回False
	def find_pic_shift_r_click(
		self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0
	):
		# type: (int, int, int, int, str, str, float, int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			VK_SHIFT = 16
			self.key_down(VK_SHIFT)
			self.move_r_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4))
			self.key_up(VK_SHIFT)
		ret = True if ret[0] >= 0 else False
		return ret

	# 找图左键双击, 找到返回True, 未找到返回False
	def find_pic_db_click(
		self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0
	):
		# type: (int, int, int, int, str, str, float, int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_db_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4))
		ret = True if ret[0] >= 0 else False
		return ret

	# 找图并移动过去
	def find_pic_and_move(
		self,
		x1,
		y1,
		x2,
		y2,
		pic,
		delta_color="050505",
		sim=0.95,
		order=0,
		timeout=0,
		dx=0,
		dy=0,
		is_delay=False,
	):
		# type: (int, int, int, int, str, str, float, int, int, int, int, bool) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_to(ret[1] + dx, ret[2] + dy, is_delay=is_delay)
		return ret[0] >= 0

	# 获取鼠标位置
	def get_cursor_pos(self):
		# type: () -> (int, int)
		locker = QMutexLocker(self.mutex)
		ret, x, y = self.obj.GetCursorPos()  # 没获取成功的话返回的是(0，-1, -1)
		return x, y

	# 返回图片所在窗口在坐标, 找到返回相应坐标, 没找到返回-1, -1
	def get_pic_pos(
		self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0
	):
		# type: (int, int, int, int, str, str, float, int, int) -> (int, int)
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		return ret[1], ret[2]

	# 返回找到的图片的索引, 从0开始, 如果没找到返回-1
	def get_pic_idx(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> int
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic,
								   delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		return ret[0]

	# 返回找到的图片名"物_驱魔香", 未找到返回""(注意:没有图片后缀名)
	def get_pic_name(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0, dir=""):
		# type: (int, int, int, int, str, str, float, int, int, str) -> str
		locker = QMutexLocker(self.mutex)
		if dir != "":  # 设置指定路径
			self.obj.SetPath(dir)
		ret_str = ""
		while True:
			# ret = ("物_驱魔香.bmp", 364, 302) 或 ("", -1, -1)
			ret = self.obj.FindPicS(
				x1, y1, x2, y2, pic, delta_color, sim, order)
			if ret[0] != "" or timeout <= 0:
				ret_str = ret[0].rstrip(".bmp")
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if dir != "":  # 设置回原路径
			self.obj.SetPath(DIR_RES)
		return ret_str

	# 返回找到的图片的数量
	def get_pic_num(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> int
		tuple_list = self.find_pic_ex(
			x1, y1, x2, y2, pic, delta_color, sim, order, timeout)
		ret = len(tuple_list)
		return ret

	# 找出距离某点最近的图片位置
	def find_pic_nearest_pos(self, x1, y1, x2, y2, pic, cx, cy, delta_color="151515", sim=0.95, order=0,
							 timeout=0):
		# type: (int, int, int, int, str, int, int, str, float, int, int) -> (int, int)
		locker = QMutexLocker(self.mutex)
		while True:
			all_pos = self.obj.FindPicEx(
				x1, y1, x2, y2, pic, delta_color, sim, order)
			if all_pos or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		ret_x, ret_y = -1, -1
		if all_pos:
			id_x_y = self.obj.FindNearestPos(all_pos, 0, cx, cy)  # "0,100,200"
			_, ret_x, ret_y = id_x_y.split(",")
			ret_x, ret_y = int(ret_x), int(ret_y)
		return ret_x, ret_y

	# 找图并拖动
	def find_pic_drag_to(self, x1, y1, x2, y2, pic, new_x, new_y, timeout=0):
		locker = QMutexLocker(self.mutex)
		x, y = self.get_pic_pos(x1, y1, x2, y2, pic, timeout=timeout)
		if x < 0:
			return False
		if (x, y) == (new_x, new_y):
			return True
		self.move_drag_to(x, y, new_x, new_y, re_move=False)
		return True

	# 找图偏移点击
	def find_pic_offset_click(self, x1, y1, x2, y2, pic, delta_color="151515", sim=0.95, order=0, timeout=0, dx=0, dy=0, re_move=True, is_sleep=True):
		# type: (int, int, int, int, str, str, float, int, int, int, int, bool, bool, bool) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (0, 43, 31) 或 (-1, -1, -1)
			ret = self.obj.FindPic(x1, y1, x2, y2, pic, delta_color, sim, order)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_click(ret[1] + dx, ret[2] + dy, re_move=re_move, is_sleep=is_sleep)
		ret = True if ret[0] >= 0 else False
		return ret

	# -------------------------找色相关 -------------------------
	# 找色, 找到返回True, 未找到返回False
	def find_color(self, x1, y1, x2, y2, color, sim=1.0, order=0, timeout=0):
		# type: (int, int, int, int, str, float, int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (1, 33, 227) 或 (0, -1, -1)
			ret = self.obj.FindColor(x1, y1, x2, y2, color, sim, order)
			if ret[0] or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		return ret[0]
	
	# 多点找色
	def find_multi_color(self, x1, y1, x2, y2, colors: List[tuple], order=0, timeout=0):
		# type: (int, int, int, int, List[tuple], int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			for c_info in colors:
				# ret = (1, 33, 227) 或 (0, -1, -1)
				ret = self.obj.FindMultiColor(
					x1, y1, x2, y2, *c_info, order)
				if ret[0] > 0:
					return True
			if timeout <= 0:
				return False
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
			
	def get_multi_color_pos(self, x1, y1, x2, y2, colors: List[tuple], order=0, timeout=0):
		# type: (int, int, int, int, List[tuple], int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			for c_info in colors:
				# ret = (1, 33, 227) 或 (0, -1, -1)
				ret = self.obj.FindMultiColor(
					x1, y1, x2, y2, *c_info, order)
				if ret[0] > 0:
					return ret[1], ret[2]
			if timeout <= 0:
				return -1, -1
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
	
	# 多点找色左击
	def find_multi_color_click(self, x1, y1, x2, y2, colors: List[tuple], order=0, timeout=0):
		# type: (int, int, int, int, List[tuple], int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			for c_info in colors:
				# ret = (1, 33, 227) 或 (0, -1, -1)
				ret = self.obj.FindMultiColor(
					x1, y1, x2, y2, *c_info, order)
				if ret[0] > 0:
					self.move_click(ret[1] + rnd(2, 4), ret[2] +
									rnd(2, 4))
					return True
			if timeout <= 0:
				return False
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME

	# 找色左键点击, 找到返回True, 未找到返回False
	def find_color_click(self, x1, y1, x2, y2, color, sim=1.0, order=0, timeout=0, re_move=True):
		# type: (int, int, int, int, str, float, int, int, bool) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (1, 33, 227) 或 (0, -1, -1)
			ret = self.obj.FindColor(x1, y1, x2, y2, color, sim, order)
			if ret[0] or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0]:
			# print(f"找色{color}成功, 坐标{ret[1]}, {ret[2]}, order={order}")
			if order == 0:  # 左右上下
				self.move_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4), re_move=re_move)
			elif order == 1:  # 左右下上
				self.move_click(ret[1] + rnd(2, 4), ret[2] - rnd(2, 4), re_move=re_move)
			elif order == 2:  # 右左上下
				self.move_click(ret[1] - rnd(2, 4), ret[2] + rnd(2, 4), re_move=re_move)
			elif order == 3:  # 右左下上
				self.move_click(ret[1] - rnd(2, 4), ret[2] - rnd(2, 4), re_move=re_move)
		return ret[0]
	
	# 找色左键点击, 找到返回True, 未找到返回False
	def find_color_offset_click(self, x1, y1, x2, y2, color, sim=1.0, order=0, timeout=0, dx=0, dy=0):
		# type: (int, int, int, int, str, float, int, int, int, int) -> bool
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (1, 33, 227) 或 (0, -1, -1)
			ret = self.obj.FindColor(x1, y1, x2, y2, color, sim, order)
			if ret[0] or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0]:
			if order == 3:
				self.move_click(ret[1] - dx, ret[2] - dy)
			else:
				self.move_click(ret[1] + dx, ret[2] + dy)
		return ret[0]

	# 返回颜色所在窗口坐标, 没找到返回-1,-1
	def get_color_pos(self, x1, y1, x2, y2, color, sim=1.0, order=0, timeout=0):
		# type: (int, int, int, int, str, float, int, int) -> (int, int)
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (1, 33, 227) 或 (0, -1, -1)
			ret = self.obj.FindColor(x1, y1, x2, y2, color, sim, order)
			if ret[0] or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		return ret[1], ret[2]

	# 返回指定坐标的颜色, "rrggbb"全小写
	def get_pos_color(self, x: int, y: int) -> str:
		locker = QMutexLocker(self.mutex)
		ret = self.obj.GetColor(x, y)
		return ret

	# ------------------------文字相关 ------------------------
	# 找到字返回True, 没找到返回False
	def find_str(self, x1, y1, x2, y2, string, color, sim=0.95, zk=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> bool
		locker = QMutexLocker(self.mutex)
		self.obj.UseDict(zk)
		while True:
			# ret = (0, 26, 263) 或 (-1, -1, -1)
			ret = self.obj.FindStrFast(x1, y1, x2, y2, string, color, sim)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		ret = True if ret[0] >= 0 else False
		return ret

	# 找字扩展, 找到形如[(0,100,20), (1,30,40)]的列表, 一个都没找到返回[]
	def find_str_ex(self, x1, y1, x2, y2, string, color, sim=0.95, zk=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> List[tuple]
		locker = QMutexLocker(self.mutex)
		self.obj.UseDict(zk)
		while True:
			ret_str = self.obj.FindStrFastEx(
				x1, y1, x2, y2, string, color, sim)
			if ret_str or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		# "1,100,20|2,30,40" -> [(0,100,20), (1,30,40)]
		ret_list = []
		if ret_str:
			pos_list = ret_str.split("|")  # ["1,100,20", "2,30,40"]
			for pos in pos_list:
				kk = pos.split(",")  # ["1", "100", "20"]
				idx, x, y = int(kk[0]), int(kk[1]), int(kk[2])
				ret_list.append((idx, x, y))
		return ret_list

	# 找字左键点击, 找到返回True, 未找到返回False
	def find_str_click(self, x1, y1, x2, y2, string, color, sim=0.95, zk=0, timeout=0, re_move=True, is_sleep=True, use_fast=True):
		# type: (int, int, int, int, str, str, float, int, int, bool, bool, bool) -> bool
		self.obj.UseDict(zk)
		while True:
			# ret = (0, 26, 263) 或 (-1, -1, -1)
			if use_fast:
				ret = self.obj.FindStrFast(x1, y1, x2, y2, string, color, sim)
			else:
				ts1 = time.time()
				ret = self.obj.FindStr(x1, y1, x2, y2, string, color, sim)
				ts2 = time.time()
				print(f"find_str cost time: {ts2 - ts1}")
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4), re_move=re_move, is_sleep=is_sleep, relative=True)
		ret = True if ret[0] >= 0 else False
		return ret

	# 找字左键双击, 找到返回True, 未找到返回False
	def find_str_db_click(self, x1, y1, x2, y2, string, color, sim=0.95, zk=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> bool
		self.obj.UseDict(zk)
		while True:
			# ret = (0, 26, 263) 或 (-1, -1, -1)
			ret = self.obj.FindStrFast(x1, y1, x2, y2, string, color, sim)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			self.move_click(ret[1] + rnd(2, 4), ret[2] + rnd(2, 4), click_count=2)
		ret = True if ret[0] >= 0 else False
		return ret

	# 找字左键点击, 找到返回True, 未找到返回False
	def find_str_offset_click(
		self,
		x1,
		y1,
		x2,
		y2,
		string,
		color,
		sim=0.95,
		zk=0,
		dx=0,
		dy=0,
		timeout=0,
		re_move=True,
		is_sleep=True,
		use_fast=True,
		limit_border=False,
		limit_x=0,
		limit_y=0,
		relative=False,
	):
		# type: (int, int, int, int, str, str, float, int, int, int, int, bool, bool, bool, bool, int, int, bool) -> bool
		self.obj.UseDict(zk)
		while True:
			# ret = (0, 26, 263) 或 (-1, -1, -1)
			if use_fast:
				ret = self.obj.FindStrFast(x1, y1, x2, y2, string, color, sim)
			else:
				ret = self.obj.FindStr(x1, y1, x2, y2, string, color, sim)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		if ret[0] >= 0:
			x = ret[1] + dx
			y = ret[2] + dy
			if limit_border:
				if limit_x and x > limit_x:
					x = limit_x
				if limit_y and y > limit_y:
					y = limit_y
			self.obj.MoveTo(x, y+2)
			self.move_click(
				x,
				y,
				re_move=re_move,
				is_sleep=is_sleep,
				relative=relative,
			)
		ret = True if ret[0] >= 0 else False
		return ret

	# 返回字所在窗口坐标, 没找到返回-1,-1
	def get_str_pos(self, x1, y1, x2, y2, string, color, sim=0.95, zk=0, timeout=0):
		# type: (int, int, int, int, str, str, float, int, int) -> (int, int)
		locker = QMutexLocker(self.mutex)
		self.obj.UseDict(zk)
		while True:
			# ret = (0, 26, 263) 或 (-1, -1, -1)
			ret = self.obj.FindStrFast(x1, y1, x2, y2, string, color, sim)
			if ret[0] >= 0 or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		return ret[1], ret[2]

	# 找出距离某点最近的字位置
	def find_str_nearest_pos(self, x1, y1, x2, y2, string, color, cx, cy, sim=0.95, zk=0, timeout=0):
		# type: (int, int, int, int, str, str, int, int, float, int, int) -> (int, int)
		locker = QMutexLocker(self.mutex)
		self.obj.UseDict(zk)
		while True:
			all_pos = self.obj.FindStrFastEx(
				x1, y1, x2, y2, string, color, sim)
			if all_pos or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		ret_x, ret_y = -1, -1
		if all_pos:
			id_x_y = self.obj.FindNearestPos(all_pos, 0, cx, cy)  # "0,100,200"
			_, ret_x, ret_y = id_x_y.split(",")
			ret_x, ret_y = int(ret_x), int(ret_y)
		return ret_x, ret_y

	# 识别文字,未识别出返回""
	def ocr(self, x1, y1, x2, y2, color, sim=1.0, zk=0, timeout=0):
		# type: (int, int, int, int, str, float, int, int) -> str
		locker = QMutexLocker(self.mutex)
		self.obj.UseDict(zk)
		while True:
			# ret = "制" 或 ""
			ret = self.obj.Ocr(x1, y1, x2, y2, color, sim)
			if ret or timeout <= 0:
				break
			msleep(SLEEP_TIME)
			timeout -= SLEEP_TIME
		return ret
	
	# 获取颜色数量
	def is_region_color_count_enough(self, x1, y1, x2, y2, color, sim=1.0, min_count=10, timeout=0, color2="", min_count2=10):
		# type: (int, int, int, int, str, float, int, int, str, int) -> int
		locker = QMutexLocker(self.mutex)
		while True:
			# ret = (1, 33, 227) 或 (0, -1, -1)
			ret = self.obj.GetColorNum(x1, y1, x2, y2, color, sim)
			# print(f'cur_count:{ret}, min_count:{min_count}')
			if ret >= min_count or timeout <= 0:
				return ret >= min_count
			if color2:
				ret2 = self.obj.GetColorNum(x1, y1, x2, y2, color2, sim)
				# print(f'2cur_count:{ret}, min_count:{min_count}')
				if ret2 >= min_count2 or timeout <= 0:
					return ret2 >= min_count2
			time.sleep(SLEEP_TIME / 1000)
			timeout -= SLEEP_TIME

	# 声音
	def beep(self, freq=800, duration=500):
		locker = QMutexLocker(self.mutex)
		self.obj.Beep(freq, duration)


def pass_dm_vip(com_obj):
	import os
	dll_path = os.path.join(os.getcwd(), "dll", DLL_NAME_DM)
	dw_dll_base = GetModuleHandle(dll_path)
	ver = com_obj.ver()
	if ver == "3.1233":
		WriteProcessMemory(-1, dw_dll_base + 883572, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 883692, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 883696, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 883707, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 883734, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 884224, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 884264, b"\x01")
	elif ver == "5.1423":
		WriteProcessMemory(-1, dw_dll_base + 1074128, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1074132, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1074660, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1074708, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1074712, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1076388, b"\x01")
	elif ver == "6.1538":
		WriteProcessMemory(-1, dw_dll_base + 1078240, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1078244, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1078772, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1078820, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1078824, b"\x01")
	elif ver == "6.1544":
		WriteProcessMemory(-1, dw_dll_base + 1086864, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1086868, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1087396, b"\x01")
		WriteProcessMemory(-1, dw_dll_base + 1087444, b"\x01")
	else:
		print("注册中...")
		ret = com_obj.Reg("your_reg_code", "your_extra_code")
		print("注册结果：", ret)

