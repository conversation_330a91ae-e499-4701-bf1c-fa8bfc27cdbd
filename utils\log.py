from loguru import logger as loguru_logger
from datetime import datetime
import os

from const.const import PATH_SOFTWARE_LOG

# ---------------------- 日志操作 ----------------------
class Logger():
    def __init__(self, path):
        self.logger = loguru_logger
        log_path_info = os.path.join(path, 'info.log')
        self.logger.add(
            sink=log_path_info,
            rotation='192 KB',  # 日志文件最大限制
            retention='2 days',  # 最长保留天
            format="{time:MM-DD HH:mm:ss}|{level}|{message}",  # 日志显示格式
            compression="zip",  # 压缩形式保存
            encoding='utf-8',  # 编码
            level='INFO',  # 日志级别
        )

    def info(self, msg: str):
        self.logger.info(msg)

    def warn(self, msg: str):
        self.logger.warning(msg)


log = Logger(PATH_SOFTWARE_LOG)

if __name__ == '__main__':
    cwd = os.getcwd()
    logger = Logger(os.path.join(cwd, 'logs'))
    logger.info('info')
    logger.warn('warn')
