import time
import os
from datetime import datetime
import pytz

from const import const
from biz.constants import constants as biz_const

wnd_main = None  # 主窗口对象
ori_obj = None  # COM对象(原生)
com_obj = None  # COM对象(封装过的ori_obj)
global_wk = None  # 全局工人对象(是com_obj的封装)
is_first_heart = True  # 第一次心跳, 响应后才置位False
is_first_switch = True  # 第一次切stack页面, 切完立刻置位False
due_ts = 0  # 卡密到期时间戳
dpi = 1.0  # 屏幕缩放率

work_dir = os.getcwd()
cur_time_stamp = int(time.time())
china_tz = pytz.timezone('Asia/Shanghai')
cur_time_fmt = datetime.now(china_tz).strftime("%H:%M:%S")
# print(f'当前时间:{cur_time_fmt}')

# os.environ["FORCE_YT_RELEASE"] = "0"
if os.getenv("LOCAL_YT_DEBUG") == "1" and os.getenv("FORCE_YT_RELEASE") != "1":
    const.APP_NAME += "-DEBUG "
    protocal = "http://"
    server_host_name = "127.0.0.1:8000"
    grpc_server_host_name = "127.0.0.1:9000"
else:
    protocal = "https://"
    server_host_name = "soult.cn"  # ************ soult.cn
    grpc_server_host_name = "************:9000"
official_url = "https://yt.soult.cn"
card_number = ""
machine_code = ""
user_info = {}
# 下面这几个数据是服务端返回的
card_rights = 1  # 如果服务端返回"One", 这里要设置为1
is_free = True  # 是免费用户, 只能用免费功能
extra_rights = 0  # 卡密附带的增值功能,里面放id, 如2+4+8

# 存放所有窗口工人对象的列表
worker_list = [None for _ in range(const.TBE_CONSOLE_ROW)]
# 窗口句柄列表
hwnd_list = [None for _ in range(const.TBE_CONSOLE_ROW)]
# 方案下拉框对象列表，一行就是一个下拉框对象
cmb_plan_list = [None for _ in range(const.TBE_CONSOLE_ROW)]
# 服务器下拉框对象列表，一行就是一个下拉框对象
cmb_server_list = [None for _ in range(const.TBE_CONSOLE_ROW)]
# 队伍列表
team_list = [None for _ in range(const.TBE_CONSOLE_ROW // 5)]
# 任务名: 任务类
task_dict = {}
default_cfg_plan = biz_const.default_biz_cfg_plan
# 方案名：方案对象（默认的方案对象搜set_plan_setting_dict）
cfg_plan_dict = {}
# 当前控件对应的方案配置
cur_cfg_plan = {}
# 通用配置
default_cfg_common = {
    "启用鼠标中键": True,
    "卡密": "",
    "区服": "不忘初心",
    "游戏账号信息": [],
    "获取窗口后是否排列": False,
    "获取窗口后是否设置": True,
    "获取窗口后排列方式": 1,
    "获取窗口后设置方案": 1,
    "双击方案列设置方案": 0,
    "游戏路径": "",
    "定时": False,
    "定时运行全部窗口": False,
    "定时关闭计算机": False,
    "定时运行全部窗口时间": "00:00",
    "定时关闭计算机时间": "23:50",
    "隐藏游戏任务栏图标": False,
    "软件背景": 2,
    "背景透明度": 80,
    "执行速率": 100,
    "突破多开限制": False,
    "MD5": "",
    "HMAC": "",
    "背景图片路径": "",
    "窗口宽": 750,
    "窗口高": 500,
    "商品名称价格表": {
        "刻花白玉杯": 150000,
        "三彩骆驼": 99999,
        "玉蝉出牙环": 300000,
        "鎏金银铜马": 2000000,
        "康乃馨": 99999,
        "狗皮": 9999,
        "猪仔皮": 9999,
        "玉石骰": 9999,
        "某某剑法残谱": 9999,
        "开山锤": 9999,
        "小龙女的画像": 9999,
        "刺帽针": 9999,
        "眼罩": 9999,
        "马奶酒": 9999,
        "破碎的绸衣": 9999,
        "用旧的大马士革刀": 9999,
        "生锈的斧子": 19999,
        "破面罩": 19999,
        "龟壳": 29999,
        "天地铲": 29999,
        "秘制香囊": 29999,
        "锋利的匕首": 29999,
        "红链珠": 29999
    },
    "物价表": [
        {
            "名称": "刻花白玉杯",
            "价格": 150000,
            "备注": "芳芳任务链古董"
        },
        {
            "名称": "三彩骆驼",
            "价格": 99999,
            "备注": "芳芳任务链古董"
        },
        {
            "名称": "玉蝉出牙环",
            "价格": 300000,
            "备注": "芳芳任务链古董"
        },
        {
            "名称": "鎏金银铜马",
            "价格": 2000000,
            "备注": "芳芳任务链古董"
        },
        {
            "名称": "康乃馨",
            "价格": 99999,
            "备注": "吉禾送花"
        },
        {
            "名称": "狗皮",
            "价格": 9999,
            "备注": "10-30级师门任务品"
        },
        {
            "名称": "猪仔皮",
            "价格": 9999,
            "备注": "10-30级师门任务品"
        },
        {
            "名称": "玉石骰",
            "价格": 9999,
            "备注": "30-99级师门任务品"
        },
        {
            "名称": "某某剑法残谱",
            "价格": 9999,
            "备注": "30-99级师门任务品"
        },
        {
            "名称": "小龙女的画像",
            "价格": 9999,
            "备注": "30-99级师门任务品"
        },
        {
            "名称": "开山锤",
            "价格": 9999,
            "备注": "30-99级师门任务品"
        },
        {
            "名称": "刺帽针",
            "价格": 9999,
            "备注": "100-119级师门任务品"
        },
        {
            "名称": "眼罩",
            "价格": 9999,
            "备注": "100-119级师门任务品"
        },
        {
            "名称": "马奶酒",
            "价格": 9999,
            "备注": "100-119级师门任务品"
        },
        {
            "名称": "破碎的绸衣",
            "价格": 9999,
            "备注": "100-119级师门任务品"
        },
        {
            "名称": "用旧的大马士革刀",
            "价格": 9999,
            "备注": "120-139级师门任务品"
        },
        {
            "名称": "生锈的斧子",
            "价格": 19999,
            "备注": "120-139级师门任务品"
        },
        {
            "名称": "破面罩",
            "价格": 19999,
            "备注": "120-139级师门任务品"
        },
        {
            "名称": "龟壳",
            "价格": 29999,
            "备注": "120-139级师门任务品"
        },
        {
            "名称": "天地铲",
            "价格": 29999,
            "备注": "140级师门任务品"
        },
        {
            "名称": "秘制香囊",
            "价格": 29999,
            "备注": "140级师门任务品"
        },
        {
            "名称": "锋利的匕首",
            "价格": 29999,
            "备注": "140级师门任务品"
        },
        {
            "名称": "红链珠",
            "价格": 29999,
            "备注": "140级师门任务品"
        }
    ]
}
cfg_common = default_cfg_common

SCREEN_W, SCREEN_H = 1920, 1080  # 这两个值当主窗口创建成功后要重新获取
RECT_FULL_SCREEN = (0, 0, SCREEN_W, SCREEN_H)
RECT_SCREEN_CENTER_GAME = RECT_FULL_SCREEN
exec_rate = 1.0
