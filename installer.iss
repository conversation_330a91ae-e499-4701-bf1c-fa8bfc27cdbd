#define MyAppName "QiLing"
#define MyAppVersion "4.0"
#define MyAppExeName "launcher.exe"

[Setup]
AppId={{F1A3E8BB-B751-4CB1-B127-972E946C109B}}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
DefaultDirName=D:\QiLing
DisableProgramGroupPage=yes
PrivilegesRequired=admin
OutputDir=./dist
OutputBaseFilename=installer
SetupIconFile=./ui/installer.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name:"cn";MessagesFile:"compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "./dist/qiling/launcher.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "./dist/qiling/*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs


[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon; Parameters: "/desktopicon"; \
AfterInstall: SetElevationBit('{autodesktop}\{#MyAppName}.lnk')

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent runascurrentuser

[Code]
procedure SetElevationBit(Filename: string);
var
  Buffer: string;
  Stream: TStream;
begin
  Filename := ExpandConstant(Filename);
  Log('Setting elevation bit for ' + Filename);

  Stream := TFileStream.Create(FileName, fmOpenReadWrite);
  try
    Stream.Seek(21, soFromBeginning);
    SetLength(Buffer, 1);
    Stream.ReadBuffer(Buffer, 1);
    Buffer[1] := Chr(Ord(Buffer[1]) or $20);
    Stream.Seek(-1, soFromCurrent);
    Stream.WriteBuffer(Buffer, 1);
  finally
    Stream.Free;
  end;
end;
